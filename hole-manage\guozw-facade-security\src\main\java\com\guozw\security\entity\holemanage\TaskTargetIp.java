package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 检测目标表
 * 检测类型为：IP地址
 * 目标类型为：资产
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "task_target_ip")
public class TaskTargetIp extends BaseEntity<TaskTargetIp> implements Serializable {

    private static final long serialVersionUID = -5475098298449655901L;

    /**
     * 主键
     */
    @TableId
    private String task_target_ip_id;
    /**
     * task_basic表主键
     */
    private String task_basic_id;
    /**
     * 分组部门id
     */
    private String asset_department_id;
    /**
     * ip类型
     */
    private String asset_ip_type;
    /**
     * 检测目标ip
     */
    private String asset_ip_list;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
