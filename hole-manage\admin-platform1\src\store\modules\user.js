import { login, logout, getInfo } from "@/api/user";
import { login2, logout2, getUserDetailInfo } from "@/api/security/user";
import { getToken, setToken, removeToken } from "@/utils/auth";
import router, { resetRouter } from "@/router";

const state = {
    token: getToken(),
    name: "",
    avatar: "",
    introduction: "",
    roles: [],
    btnpermissions: [],
    userInfo: {}
};

const mutations = {
    SET_TOKEN: (state, token) => {
        state.token = token;
    },
    SET_INTRODUCTION: (state, introduction) => {
        state.introduction = introduction;
    },
    SET_NAME: (state, name) => {
        state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
        state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
        state.roles = roles;
    },
    SET_USERINFO: (state, userInfo) => {
        state.userInfo = userInfo;
    },
    SET_BTNPERMISSIONS: (state, btnpermissions) => {
        state.btnpermissions = btnpermissions;
    },
    
};

const actions = {

    // user login
    login({ commit }, userInfo) {
        return new Promise((resolve, reject) => {
            login2(userInfo)
                .then(response => {
                    const { data } = response;
                    commit("SET_TOKEN", data);
                    setToken(data);
                    resolve();
                })
                .catch(error => {
                    reject(error);
            })
        });
    },

    // get user info
    getInfo({ commit, state }) {
        return new Promise((resolve, reject) => {
            getUserDetailInfo()
                .then(response => {
                    const { data } = response;

                    if (!data) {
                        reject("Verification failed, please Login again.");
                    }

                    console.log(`用户信息`, data);
                    // const { roles, name, avatar, introduction } = data

                    const {
                        rolecodes,
                        username,
                        useravatar,
                        userintroduction,
                        btnpermissions
                    } = data;
                    // roles must be a non-empty array
                    // if (!roles || roles.length <= 0) {
                    //   reject('getInfo: roles must be a non-null array!')
                    // }

                    if (!rolecodes || rolecodes.length <= 0) {
                        reject("getUserInfo: roles must be a non-null array!");
                    }

                    // commit('SET_ROLES', roles)
                    // commit('SET_NAME', name)
                    // commit('SET_AVATAR', avatar)
                    // commit('SET_INTRODUCTION', introduction)

                    commit("SET_ROLES", rolecodes);
                    commit("SET_NAME", username);
                    commit("SET_AVATAR", useravatar);
                    commit("SET_INTRODUCTION", userintroduction);
                    commit("SET_USERINFO", data);
                    commit("SET_BTNPERMISSIONS", btnpermissions);
                    resolve(data);
                })
                .catch(error => {
                    reject(error);
                });
        });
    },

    // user logout
    logout({ commit, state, dispatch }) {
        return new Promise((resolve, reject) => {
            logout2()
                .then(() => {
                    commit("SET_TOKEN", "");
                    commit("SET_ROLES", []);
                    removeToken();
                    resetRouter();

                    // reset visited views and cached views
                    // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
                    dispatch("tagsView/delAllViews", null, { root: true });

                    resolve();
                })
                .catch(error => {
                    reject(error);
                });
        });
    },

    // remove token
    resetToken({ commit }) {
        return new Promise(resolve => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            removeToken();
            resolve();
        });
    },

    // dynamically modify permissions
    changeRoles({ commit, dispatch }, role) {
        return new Promise(async resolve => {
            const token = role + "-token";

            commit("SET_TOKEN", token);
            setToken(token);

            const { roles } = await dispatch("getInfo");

            resetRouter();

            // generate accessible routes map based on roles
            const accessRoutes = await dispatch(
                "permission/generateRoutes",
                roles,
                { root: true }
            );

            // dynamically add accessible routes
            router.addRoutes(accessRoutes);

            // reset visited views and cached views
            dispatch("tagsView/delAllViews", null, { root: true });

            resolve();
        });
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
