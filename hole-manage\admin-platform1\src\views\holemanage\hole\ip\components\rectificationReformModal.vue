<template>
  <div>
    <vxe-modal
      :height="myModalHeight"
      width="30%"
      position="centers"
      resize
      title="整改下发通知"
      :loading="loading"
      v-model="modalShow"
      @close="handleModalClose"
      showFooter>
      <vxe-form
        ref="myForm"
        align="center"
        title-width="120"
        title-align="right"
        :data="myFormData"
        :rules="rules"
        prevent-submit
        span="20"
      >
        <vxe-form-item title="要求完成日期" field="request_success_time" >
          <template v-slot="scope">
            <vxe-input v-model="myFormData.request_success_time" placeholder="日期选择" type="date"
                       @change="handleDateChange"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="负责整改部门" field="reform_department_id">
          <template v-slot="scope">
            <el-select v-model="myFormData.reform_department_id" filterable placeholder="请选择负责整改部门"
                       style="width: 100%" >
              <el-option
                v-for="item in departmentList"
                :key="item.departmentcode"
                :label="item.departmentname"
                :value="item.departmentcode">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="特别说明" field="special_remark">
          <template v-slot="scope">
            <vxe-textarea v-model="myFormData.special_remark" placeholder="特别说明" ></vxe-textarea>
          </template>
        </vxe-form-item>

        <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
          <template v-slot="scope">
            <el-upload
              class="upload-demo"
              ref="upload"
              :action="''"
              :on-preview="(file, fileList) => handlePreview(file, 1)"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 1)"
              :on-change="(file, fileList) => handleChange(file, fileList, 1)"
              :file-list="planBackFileList"
              :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip" style="color:red">上传文件不超过5M</div>
            </el-upload>
          </template>
        </vxe-form-item>
        <vxe-form-item>
          <el-divider content-position="left">通知方式</el-divider>
        </vxe-form-item>
        <vxe-form-item align="left" title="系统通知">
          <vxe-checkbox v-model="systemCheckBox" @change="(value)=>checkBoxChange(value,1)"></vxe-checkbox>
        </vxe-form-item>
        <vxe-form-item title="系统用户" field="workorderWarnVO.userIdList" v-if="systemCheckBox">
          <template v-slot="scope">
            <el-select
              v-model="myFormData.workorderWarnVO.userIdList"
              clearable
              filterable
              multiple
              placeholder="请选择系统用户"
              style="width:100%"
              @change="handleSelectChange"
            >
              <el-option
                v-for="item in userEnum"
                :key="item.usercode"
                :label="item.nickname"
                :value="item.usercode"
              >
                <span style="float: left">{{ item.nickname }}</span>
                <span
                  style="float: right; color: #8492a6; font-size: 13px;margin-left:10px"
                >{{ item.departmentname }}</span
                >
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="消息标题" field="workorderWarnVO.workorder_warn_name" v-if="systemCheckBox">
          <template v-slot="scope">
            <vxe-input v-model="myFormData.workorderWarnVO.workorder_warn_name"
                       placeholder="消息标题"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="消息内容" field="workorderWarnVO.workorder_warn_content" v-if="systemCheckBox">
          <template v-slot="scope">
            <vxe-textarea v-model="myFormData.workorderWarnVO.workorder_warn_content"
                       placeholder="消息内容"></vxe-textarea>
          </template>
        </vxe-form-item>
        <vxe-form-item align="left" title="邮箱通知">
          <vxe-checkbox v-model="emailCheckBox" @change="(value)=>checkBoxChange(value,2)"></vxe-checkbox>
        </vxe-form-item>
        <vxe-form-item>
          <send-hole-email-form ref="sendEmailForm" :show="emailCheckBox" :user="userList"
                           :email-template="emailTemplate" :hole-batch-list="holeBatchList"></send-hole-email-form>
        </vxe-form-item>
      </vxe-form>
      <template v-slot:footer>
        <el-button type="" @click="handleModalClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm"
        >确定
        </el-button
        >
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import { parseTime } from '@/utils'
import {doReform,listHolePatch} from '@/api/holemanage/asset'
import SendHoleEmailForm from "@/views/holemanage/hole/ip/components/sendHoleEmailForm";
import XEUtils from 'xe-utils'

export default {
  name: "holePlanBackModal",
  components: {SendHoleEmailForm},
  data() {
    return {
      myFormData: {
        workorderWarnVO: {
          workorder_warn_name: '',
          workorder_warn_content: '',
          userIdList: []
        },
        request_success_time: new Date(),
        operationType:'0'
      },
      rules: {
        'workorderWarnVO.userIdList': [{required: true, message: "必填字段", trigger: 'change'}],

        request_success_time: [{required: true, message: "请选择完成日期", trigger: 'change'}],
        reform_department_id: [{required: true, message: "请选择整改部门", trigger: 'change'}],
      },
      loading: false,
      planBackFileList: [],
      systemCheckBox: false,
      emailCheckBox: false,
      myModalHeight: '99%',
      holeBatchList: []
    }
  },
  props: {
    modalShow: {
      type: Boolean,
      default: false
    },
    userList: {
      type: Array,
      require: true
    },
    emailTemplate: {
      type: Array,
      require: true
    },
    holeBasicInfo: {
      type: Array,
      default: ()=>[]
    },
    holeType:{
      type: String
    },
    departmentList:{
      type: Array
    }

  },
  created() {

  },
  methods: {

    handleModalClose() {
      this.$refs.myForm.clearValidate();
      this.$refs.myForm.reset();
      this.$refs.sendEmailForm.clearValidate();
      this.$refs.sendEmailForm.reset();
      this.systemCheckBox = false;
      this.emailCheckBox = false;
      this.myFormData.workorderWarnVO.userIdList = [];
      this.planBackFileList = [];
      this.myFormData.request_success_time = new Date();
      this.$emit('close');
    },
    handleRemove(file, fileList, value) {
      console.log(file, fileList);
    },
    handlePreview(file, value) {
      console.log(file);
    },
    handleChange(file, fileList, value) {
      console.log("onChange", file, fileList);
      if (this.checkFile(file)) {
        // 移除校验失败的文件
        this.$refs.upload.uploadFiles.splice(
          this.$refs.upload.uploadFiles
            .length - 1,
          1
        );
        console.log("onChange", fileList);
        fileList.push(file);
        if (value === 1) {
          this.planBackFileList = fileList;
        } else {

        }
        return;
      }
    },
    /* 文件校验 */
    checkFile(file) {
      // 判断文件大小是否符合要求
      if (file.size / 1024 / 1024 > 5) {
        this.$XModal.message({
          message: "单个上传文件大小不能超过 5 M",
          status: "error"
        });
        return false;
      }

      return true;
    },
    checkBoxChange(checked, value) {
      if (checked.checked) {
        this.$set(this.rules, 'workorderWarnVO.userIdList', [{required: true, message: "必填字段", trigger: 'change'}]);
      } else {
        delete this.rules['workorderWarnVO.userIdList'];
      }

      console.log(checked);
      /*if (this.systemCheckBox && this.emailCheckBox) {
        this.myModalHeight = '70%';
      } else if (this.systemCheckBox) {
        this.myModalHeight = '55%';
      } else if (this.emailCheckBox) {
        this.myModalHeight = '65%';
      } else {
        this.myModalHeight = '50%';
      }*/
      if (value == 1){
        if (checked.checked) {
          this.myFormData.workorderWarnVO.workorder_warn_name = this.holeBasicInfo.hole_name;
          this.myFormData.workorderWarnVO.workorder_warn_content = '你好，你单位新增漏洞1个，请尽快进行核实和整改，' + (this.myFormData.request_success_time != null ?'并于'+parseTime(this.myFormData.request_success_time,'{y}-{m}-{d}') + '前在系统中' : '请于近期') + '反馈整改情况'
          this.myFormData.workorderWarnVO.userIdList = this.userEnum.map(e=> e.usercode);
        } else {
          this.myFormData.workorderWarnVO.workorder_warn_name = '';
          this.myFormData.workorderWarnVO.workorder_warn_content = '';
          this.myFormData.workorderWarnVO.userIdList = [];

        }
      }else {
        if (checked.checked) {
          console.log('giao')
          console.log(this.myFormData.workorderWarnVO.userIdList);
          if (this.myFormData.workorderWarnVO.userIdList.length > 0) {
            this.$refs.sendEmailForm.emailForm.userIdList = this.myFormData.workorderWarnVO.userIdList;
            this.$nextTick(() => {
              this.$refs.sendEmailForm.selectUserChange(this.userEnum.map(e => e.usercode));

            })
          } else {
            this.$refs.sendEmailForm.emailForm.userIdList = this.userEnum.map(e => e.usercode);
            this.$nextTick(() => {
              this.$refs.sendEmailForm.selectUserChange(this.userEnum.map(e => e.usercode));

            })
          }
          this.$refs.sendEmailForm.selectUserChange(this.myFormData.workorderWarnVO.userIdList);
          listHolePatch({cveNumberList: this.holeBasicInfo.map(e => e.hole_cve)}).then(res =>{
            console.log(res);
            if (res.data) {
              let holeBatchList = [];
              let haveBatchHoleBasicIdList = []
              for( let  hole in this.holeBasicInfo) {
                let holeBatch = {
                  hole_basic_id: this.holeBasicInfo[hole].hole_basic_id,
                  hole_name: this.holeBasicInfo[hole].hole_name,
                  hole_cve: this.holeBasicInfo[hole].hole_cve,
                }
                holeBatch.haveBatch = false;

                for (let resKey in res.data) {
                  if (hole.hole_cve === resKey.cveNumber) {
                    holeBatch.haveBatch = true;
                    haveBatchHoleBasicIdList.push(hole.hole_basic_id);
                    break;
                  }
                }
                holeBatchList.push(holeBatch);
              }
              this.holeBatchList = holeBatchList;
              this.$refs.sendEmailForm.emailForm.haveBatchHoleBasicIdList = haveBatchHoleBasicIdList;
              console.log(holeBatchList)
            }
          })
        } else {
          this.holeBatchList = [];
          this.$refs.sendEmailForm.clearValidate();
          this.$refs.sendEmailForm.reset();

        }
      }



    },
    //提交表单
    submitForm() {
      this.loading = true;
      this.$refs.myForm
        .validate()
        .then(async () => {

          let formData = new FormData();
          let myFormData = this.myFormData;
          console.log(myFormData);

          if (this.emailCheckBox) {
            let emailVO = await this.$refs.sendEmailForm.getDate();
            myFormData.holeEmailVO = emailVO.vo;
            if (emailVO.file) {
              formData.append('emailFile', emailVO.file);
            }
          }
          if (!this.systemCheckBox) {
            this.myFormData.workorderWarnVO.workorder_warn_name = '';
            this.myFormData.workorderWarnVO.workorder_warn_content = '';
            this.myFormData.workorderWarnVO.userIdList = [];
            this.myFormData.workorderWarnVO.params = {};
          } else {
            let url = {
              path: '/holemanage/hole/holeRectificationBack/index',
              /*query: {
                asset_ip_id: this.holeBasicInfo.asset_ip_id
              }*/
            }
            this.myFormData.workorderWarnVO.workorder_warn_url = JSON.stringify(url);
          }
          if (this.holeType === '1'){
            myFormData.holeBasicIdList = this.holeBasicInfo.map(e =>e.hole_basic_ip_id)
          }else if (this.holeType === '2') {
            myFormData.holeBasicIdList = this.holeBasicInfo.map(e => e.hole_basic_website_id);
          }
          myFormData.hole_type = this.holeType;
          console.log(myFormData);
          this.getFormDate(myFormData, formData, null);

          if (this.planBackFileList && this.planBackFileList.length > 0) {
            formData.append('reformFile', this.planBackFileList[0].raw);
          }
          doReform(formData).then(res=>{
            this.$XModal.message({
              message: "操作成功",
              status: "success"
            });
            this.handleModalClose()
            this.$emit("close");
          }).finally(e=>{
            this.loading = false;
          })

        })
        .catch(err => {
          this.loading = false;
          console.log(err);
        });

    },
    handleSelectChange(value){
      if (this.emailCheckBox){
        console.log(this.myFormData.workorderWarnVO.userIdList);
        this.$refs.sendEmailForm.emailForm.userIdList = this.myFormData.workorderWarnVO.userIdList;
        this.$refs.sendEmailForm.selectUserChange(this.myFormData.workorderWarnVO.userIdList);
      }
    },

    getFormDate(object, formData, prefix) {
      Object.keys(object).forEach(key => {
        const value = object[key]
        if (value == null || value == undefined) {
          return
        }
        if (Array.isArray(value)) {
          if (value.length == 0) {
            return;
          }
          console.log('giao');
          console.log(value)
          value.forEach((subValue, i) => {
              if (prefix) {
                formData.append(prefix + '.' + key + `[${i}]`, subValue)
              } else {
                formData.append(key + `[${i}]`, subValue)
              }
            }
          )
        } else if (Object.prototype.toString.call(object[key]) === '[object Object]') {
          if (Object.keys(value).length == 0) {
            return;
          }
          if (prefix) {
            this.getFormDate(object[key], formData, prefix + '.' + key);
          } else {
            this.getFormDate(object[key], formData, key)
          }
        } else {
          if (value == '') {
            return;
          }
          if (prefix) {
            formData.append(prefix + '.' + key, object[key])
          } else {
            formData.append(key, object[key])
          }
        }
      })
    },
    //处理改变日期的时候改变消息通知的值
    handleDateChange(value) {
      console.log('handleDateChange', value);
      this.myFormData.request_success_time =XEUtils.toStringDate(value.value);
      console.log('handleDateChange', this.myFormData.request_success_time);

      if (this.systemCheckBox) {

          console.log(this.myFormData.workorderWarnVO)
        this.myFormData.workorderWarnVO.workorder_warn_content = '你好，你单位新增漏洞1个，请尽快进行核实和整改，' + (this.myFormData.request_success_time != null ?'并于'+parseTime(this.myFormData.request_success_time,'{y}-{m}-{d}') + '前在系统中' : '请于近期') + '反馈整改情况'

      }

    },
  },
  computed: {

    userEnum() {
      let ary = [];

      for (let userEl of this.userList) {
        if (this.myFormData.reform_department_id != null && this.myFormData.reform_department_id != ''){
          if (userEl.departmentcode === this.myFormData.reform_department_id) {
            ary.push(userEl);
          }
        }else {
          for (let departmentEl of this.departmentList) {
            if (userEl.departmentcode === departmentEl.departmentcode){
              ary.push(userEl);
            }
          }
        }
      }
      return ary;
    },

  }
}
</script>

<style scoped>

</style>
