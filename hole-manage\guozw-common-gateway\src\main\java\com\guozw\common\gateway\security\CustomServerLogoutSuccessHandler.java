package com.guozw.common.gateway.security;

import cn.hutool.core.util.CharsetUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.JwtConstant;
import com.guozw.common.core.util.JacksonUtils;
import com.guozw.common.core.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.WebFilterExchange;
import org.springframework.security.web.server.authentication.logout.ServerLogoutSuccessHandler;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 注销成功处理
 */
@Slf4j
@Component
public class CustomServerLogoutSuccessHandler implements ServerLogoutSuccessHandler {

    @Override
    public Mono<Void> onLogoutSuccess(WebFilterExchange webFilterExchange, Authentication authentication) {
        log.info("注销成功");
        ServerHttpRequest request = webFilterExchange.getExchange().getRequest();
        String token = request.getHeaders().getFirst(JwtConstant.JWT_TOKEN_HEADER);
        // 将token从redis缓存中删除
        RedisUtils.delete(token);

        ServerHttpResponse response = webFilterExchange.getExchange().getResponse();
        response.setStatusCode(HttpStatus.OK);
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String result = JacksonUtils.obj2json(BaseResult.success());
        DataBuffer buffer = response.bufferFactory().wrap(result.getBytes(CharsetUtil.CHARSET_UTF_8));

        return response.writeWith(Mono.just(buffer));
    }
}
