package com.guozw.common.core.constant;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.util.Properties;

/**
 *
 * 读取security.properties配置文件的常量
 * <AUTHOR>
 * @date 2020/5/5
 */
@Slf4j
public class SecurityConstant {

    /**
     * 配置文件路径
     */
    private static final String PROPERTIES_FILE_PATH = "security.properties";

    static {

        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties(PROPERTIES_FILE_PATH);

        } catch (Exception e) {
            e.printStackTrace();
            log.debug("security.properties 配置文件未找到");
        }
    }



    /**
     * 用户初始密码
     */
    public static final String USER_INIT_PASSWORD = "123456";

    public static final String CRYPTO_AESKEY = "AesKey";

    /**
     * 超级管理员角色编码
     */
    public static final String SUPER_ADMIN_ROLE_CODE = "10000";
    /**
     * 系统管理员角色编码
     */
    public static final String SYS_ADMIN_ROLE_CODE = "20000";
    /**
     * 通用角色编码
     * 新增用户默认为通用角色
     */
    public static final String SYS_COMMON_ROLE_CODE = "30000";
    /**
     * 县公司管理员角色编码
     */
    public static final String SYS_COUNTY_ROLE_CODE = "72b18afa5ceb98a999cbab02aeb05631";
    /**
     * 地市公司、直属单位管理员角色编码
     */
    public static final String SYS_CITY_ROLE_CODE = "f433a285a91dc6f38c62509492e55513";


}
