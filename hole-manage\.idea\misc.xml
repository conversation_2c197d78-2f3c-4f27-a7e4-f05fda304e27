<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/guozw-provider-security/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-provider-log/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-common-parent/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-common-gateway/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-common-core/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-common-config/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-facade-security/pom.xml" />
        <option value="$PROJECT_DIR$/guozw-facade-log/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>