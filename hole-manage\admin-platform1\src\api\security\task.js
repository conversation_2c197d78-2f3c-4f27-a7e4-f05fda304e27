import request from '@/utils/request'
import qs from 'qs'


export function getFileEncTaskList(data) {
    return request({
        url: '/security/task/getFileEncTaskList',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function startFileEncTask(data) {
    return request({
        url: '/security/task/startFileEncTask',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function getFileCleanTaskList(data) {
    return request({
        url: '/security/task/getFileCleanTaskList',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function startFileCleanTask(data) {
    return request({
        url: '/security/task/startFileCleanTask',
        method: 'post',
        data: qs.stringify(data)
    })
}


export function saveTask(data) {
    return request({
        url: '/security/task/saveTask',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function modifyTask(data) {
    return request({
        url: '/security/task/modifyTask',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function deleteTask(data) {
    return request({
        url: '/security/task/deleteTask',
        method: 'post',
        data: qs.stringify(data)
    })
}


export function stopTask(data) {
    return request({
        url: '/security/task/stopTask',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function startFileSyncTaskOnce(data) {
    return request({
        url: '/security/task/startFileSyncTaskOnce',
        method: 'post',
        data: qs.stringify(data)
    })
}
