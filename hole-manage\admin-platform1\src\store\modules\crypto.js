
const state = {
    backendPublickey: "", // 后端公钥
    frontendPublickey: "", // 前端公钥
    frontendPrivatekey: "", // 前端私钥
    aesKey: "", // aes加密key值
};

const mutations = {
    SET_BACKEND_PUBLICKEY: (state, backendPublickey) => {
        state.backendPublickey = backendPublickey;
    },
    SET_FRONTEND_PUBLICKEY: (state, frontendPublickey) => {
        state.frontendPublickey = frontendPublickey;
    },
    SET_FRONTEND_PRIVATEKEY: (state, frontendPrivatekey) => {
        state.frontendPrivatekey = frontendPrivatekey;
    },
    SET_AES_KEY: (state, aesKey) => {
        state.aesKey = aesKey;
    },
};

const actions = {
    setBackendPublickey({ commit }, backendPublickey) {
        commit('SET_BACKEND_PUBLICKEY', backendPublickey)
    },
    setFrontendPublickey({ commit }, frontendPublickey) {
        commit('SET_FRONTEND_PUBLICKEY', frontendPublickey)
    },
    setFrontendPrivatekey({ commit }, frontendPrivatekey) {
        commit('SET_FRONTEND_PRIVATEKEY', frontendPrivatekey)
    },
    setAeskey({ commit }, aesKey) {
        commit('SET_AES_KEY', aesKey)
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
