package com.guozw.common.core.constant;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.util.Properties;

/**
 *
 * 读取jwt.properties配置文件的常量
 * <AUTHOR>
 * @date 2020/5/5
 */
@Slf4j
public class JwtConstant {

    /**
     * 配置文件路径
     */
    private static final String PROPERTIES_FILE_PATH = "jwt.properties";

    static {

        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties(PROPERTIES_FILE_PATH);
            JWT_TOKEN_HEADER = properties.getProperty("jwt.token.header");
            JWT_TOKEN_SECRET = properties.getProperty("jwt.token.secret");
            JWT_TOKEN_ISS = properties.getProperty("jwt.token.iss");
            JWT_TOKEN_EXPIRATION = properties.getProperty("jwt.token.expiration");
            JWT_TOKEN_REMEMBER_EXPIRATION = properties.getProperty("jwt.token.rememberme.expiration");
            JWT_TOKEN_REMAINING = properties.getProperty("jwt.token.remaining");
            JWT_TOKEN_PREFIX = properties.getProperty("jwt.token.prefix");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("jwt.properties 配置文件未找到");
        }
    }


    public static String JWT_TOKEN_HEADER;
    public static String JWT_TOKEN_SECRET;
    public static String JWT_TOKEN_ISS;
    public static String JWT_TOKEN_EXPIRATION;
    public static String JWT_TOKEN_REMEMBER_EXPIRATION;
    public static String JWT_TOKEN_REMAINING;
    public static String JWT_TOKEN_PREFIX;
}
