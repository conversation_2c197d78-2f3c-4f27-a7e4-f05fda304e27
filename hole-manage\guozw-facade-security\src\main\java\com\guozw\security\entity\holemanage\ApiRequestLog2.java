package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * 盛邦资产探测api请求日志
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "api_request_log2")
public class ApiRequestLog2 extends BaseEntity<ApiRequestLog2> implements Serializable {

    private static final long serialVersionUID = 5968812764995137630L;

    @TableId
    private String api_request_log_id;
    private String request_params;
    private String response_params;
    private Date request_time;
    private Date response_time;
    private String cost_time;
    private String request_url;
    private String task_id;
    private String hole_update_success;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
