package com.guozw.common.core.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.guozw.common.core.util.JacksonUtils;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class EasyExcelTest {
    private static final String fileName = "C:\\Users\\<USER>\\Desktop\\11\\\\省生态环境厅公共信用信息平台生态厅双公示台账（2019-7-1至2020-6-30）permission.xlsx";
    private static final String fileName2 = "C:\\Users\\<USER>\\Desktop\\11\\\\commentWrite-1606564265175.xlsx";
    public static void main(String[] args) {

        log.info("EasyExcelTest");
    }

    /**
     * 不创建对象的读
     */
    @Test
    public void noModelReadTest() {

        NoModelDataListener noModelDataListener = new NoModelDataListener();
        // 读取第一个sheet，读取完成后会自动finish
        EasyExcel
                .read(fileName, noModelDataListener)
                // 需要读取批注 默认不读取
                .extraRead(CellExtraTypeEnum.COMMENT)
                .sheet()
                .doRead();

        List<Map<Integer, String>> list = noModelDataListener.getList();
        String s = "";
    }

    /**
     * 不创建对象的写
     */
    @Test
    public void noModelWriteTest() {
        String fileName = String.format("C:\\Users\\<USER>\\Desktop\\11\\noModelWrite-%s.xlsx", System.currentTimeMillis());
        // 写入到第一个sheet，名字为easyexcel，然后文件流对自动关闭
        EasyExcel.write(fileName).head(head()).sheet("easyexcel").doWrite(dataList());
        log.info("写入完毕");
    }

    /**
     * 根据模板写入
     */
    @Test
    public void templateWriteTest() {
        String templateFileName = "C:\\Users\\<USER>\\Desktop\\11\\demo.xlsx";
        String fileName = String.format("C:\\Users\\<USER>\\Desktop\\11\\templateWrite-%s.xlsx", System.currentTimeMillis());
        // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
        EasyExcel.write(fileName)
                .head(head())
                .withTemplate(templateFileName)
                .inMemory(Boolean.TRUE)
                .registerWriteHandler(new CommentWriteHandler())
                .sheet()
                .doWrite(dataList())
        ;
    }

    /**
     * 插入批注
     */
    @Test
    public void commentWriteTest() {
        String fileName = String.format("C:\\Users\\<USER>\\Desktop\\11\\commentWrite-%s.xlsx", System.currentTimeMillis());
        // 这里要注意inMemory 要设置为true，才能支持批注。目前没有好的办法解决 不在内存处理批注。这个需要自己选择。
        EasyExcel.write(fileName)
                .head(head())
                .inMemory(Boolean.TRUE)
                .registerWriteHandler(new CommentWriteHandler())
                .sheet("模板")
                .doWrite(dataList());
    }


    private List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("字符串" + System.currentTimeMillis());
        List<String> head1 = new ArrayList<>();
        head1.add("数字" + System.currentTimeMillis());
        List<String> head2 = new ArrayList<>();
        head2.add("日期" + System.currentTimeMillis());
        list.add(head0);
        list.add(head1);
        list.add(head2);
        return list;
    }

    private List<List<Object>> dataList() {
        List<List<Object>> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            List<Object> data = new ArrayList<>();
            data.add("字符串" + i);
            data.add(new Date());
            data.add(0.56);
            list.add(data);
        }
        return list;
    }
}
@Slf4j
class NoModelDataListener extends AnalysisEventListener<Map<Integer, String>> {

    @Getter
    private List<Map<Integer, String>> list = new ArrayList<>();
    public static void main(String[] args) {

        log.info("EasyExcelTest2");
    }

    @SneakyThrows
    @Override
    public void invoke(Map<Integer, String> item, AnalysisContext analysisContext) {

        log.info("解析到一条数据：【{}】", JacksonUtils.obj2json(item));
        list.add(item);
    }

    @SneakyThrows
    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        log.info("读取到了一条额外信息:{}", JacksonUtils.obj2json(extra));
        switch (extra.getType()) {
            case COMMENT:
                log.info("额外信息是批注,在rowIndex:{},columnIndex;{},内容是:{}", extra.getRowIndex(), extra.getColumnIndex(),
                        extra.getText());
                break;
            case HYPERLINK:
                if ("Sheet1!A1".equals(extra.getText())) {
                    log.info("额外信息是超链接,在rowIndex:{},columnIndex;{},内容是:{}", extra.getRowIndex(),
                            extra.getColumnIndex(), extra.getText());
                } else if ("Sheet2!A1".equals(extra.getText())) {
                    log.info(
                            "额外信息是超链接,而且覆盖了一个区间,在firstRowIndex:{},firstColumnIndex;{},lastRowIndex:{},lastColumnIndex:{},"
                                    + "内容是:{}",
                            extra.getFirstRowIndex(), extra.getFirstColumnIndex(), extra.getLastRowIndex(),
                            extra.getLastColumnIndex(), extra.getText());
                } else {
                    Assert.fail("Unknown hyperlink!");
                }
                break;
            case MERGE:
                log.info(
                        "额外信息是超链接,而且覆盖了一个区间,在firstRowIndex:{},firstColumnIndex;{},lastRowIndex:{},lastColumnIndex:{}",
                        extra.getFirstRowIndex(), extra.getFirstColumnIndex(), extra.getLastRowIndex(),
                        extra.getLastColumnIndex());
                break;
            default:
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成");
    }
}

/**
 * 自定义拦截器，新增注释，第一行头加批注
 */
@Slf4j
class CommentWriteHandler extends AbstractRowWriteHandler {
    @Override
    public void beforeRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Integer rowIndex, Integer relativeRowIndex, Boolean isHead) {
        log.info("beforeRowCreate-【{}】", relativeRowIndex);
    }

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        log.info("afterRowCreate-【{}】", relativeRowIndex);
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        log.info("afterRowDispose-【{}】", relativeRowIndex);
        if (isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
            // 在第一行 第二列创建一个批注
            Comment comment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 1, 0, (short) 2, 1));
            // 输入批注信息
            comment.setString(new XSSFRichTextString("创建批注!"));
            // 将批注添加到单元格对象中
            sheet.getRow(0).getCell(1).setCellComment(comment);
        }
    }
}
