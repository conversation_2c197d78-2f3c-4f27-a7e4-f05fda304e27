import request from "@/utils/request";



export function getFile(url, data, fileName) {
  request({
    url: url ? url : "/security/export/exportExcel",
    method: 'post',
    data: data,
    responseType: 'blob'
  }).then(reData => {
    let name = fileName +".xls"
    download(name, reData)
  })
}


export function downloadFile(data) {
  request({
    url: "/security/file/download",
    method: 'post',
    data: data,
    responseType: 'blob'
  }).then(reData => {

    download( data.file_real_name,reData)
  })
}

function download(name,downloadUrl) {
  let a = document.createElement('a');

  //ArrayBuffer 转为 Blob
  let blob = new Blob([downloadUrl], {type: "application/vnd.ms-excel"});

  let objectUrl = URL.createObjectURL(blob);
  a.setAttribute("href",objectUrl);
  a.setAttribute("download", name);
  a.click();
}

