package com.guozw.log.entity;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 * 平台访问日志
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "platformaccesslog")
public class PlatformAccessLog extends BaseEntity<PlatformAccessLog> implements Serializable {


    private static final long serialVersionUID = -4717426413768601141L;
    /**
     * 平台日志编码
     */
    @TableId
    private Long platformaccesslogcode;
    private String accessmodule;
    private String accesstype;
    private String accessdesc;
    private String clientip;
    private String requesturi;
    private String requestmethod;
    private String requestparams;
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date requeststartdate;
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date requestenddate;
    private Long requestcostdate;
    private String resultparams;
    private String exceptionname;
    private String exceptioninfo;
    private String usercode;
    private String username;
    private String departmentcode;
    private String departmentname;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
