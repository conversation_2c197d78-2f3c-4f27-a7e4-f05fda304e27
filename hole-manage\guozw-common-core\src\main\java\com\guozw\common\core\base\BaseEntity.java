package com.guozw.common.core.base;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体基类
 * <AUTHOR>
 * @date 2020/6/27
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class BaseEntity<T extends Model<?>> extends Model<T> implements Serializable {

    private static final long serialVersionUID = 4646657695938660791L;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createdate;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date modifydate;

    /**
     * 当前页
     */
    @TableField(exist = false)
    private int pagenumber = 1;

    /**
     * 每页大小
     */
    @TableField(exist = false)
    private int pagesize = 10;

    /**
     * 排序字段
     */
    @TableField(exist = false)
    private String sort_field;

    /**
     * 排序方式
     */
    @TableField(exist = false)
    private String sort_order;

}
