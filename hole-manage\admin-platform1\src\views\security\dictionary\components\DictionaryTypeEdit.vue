<template>
  <div>
    <vxe-modal
                height="90%"
                width="600"
                position="center"
                resize
                :title="dictionaryTypeModal.title"
                v-model="dictionaryTypeModal.show"
                @close="handleDialogClose()"
                showFooter
    >
      <vxe-form
        ref="ruleForm"
        title-width="120"
        :data="dialogTreeForm"
        :rules="rules"
        prevent-submit
      >
        <vxe-form-item title="字典类型名称" field="dictionarytypename" span="24">
          <vxe-input v-model="dialogTreeForm.dictionarytypename" placeholder="字典类型名称"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="字典类型描述" field="dictionarytypenote" span="24">
          <vxe-input v-model="dialogTreeForm.dictionarytypenote" placeholder="字典类型描述"></vxe-input>
        </vxe-form-item>
      </vxe-form>
      <template v-slot:footer>
        <vxe-button type="button" content="取消" @click="handleDialogCancel"></vxe-button>
        <vxe-button type="button" status="primary" content="确定" @click="handleDialogSubmit"></vxe-button>
      </template>
    </vxe-modal>
  </div>
</template>
<script>
import{saveDictionaryType,modifyDictionaryType} from '@/api/security/dictionaryType'
export default {
  name: "DictionaryTypeEdit",
 props:{
    dictionaryTypeModal:{
        type:Object,
        default:()=>{}
    },
    treeForm:{
        type:Object,
        default:()=>{}
    }
    },
  data(){
        return{
            dialogTreeForm:{},
            formLabelWidth:"120px",
          rules:{
          dictionarytypename:[
              {required: true, message: '请输入字典名称'},
               { min:1, max: 50, message: '长度最大为50字符' }
              ],
          dictionarytypenote:[
              {required: true, message: '请选择字典描述'},
              { min:1, max: 100, message: '长度最大为100字符' }
              ],
        }
            
        }
    },
     watch:{
        treeForm(newVal,oldVal){
            this.dialogTreeForm=JSON.parse(JSON.stringify(newVal))
        }
    },
    methods:{
            handleDialogClose(){
            this.dictionaryTypeModal.show=false;
            this.$refs.ruleForm.clearValidate();
            },
            handleDialogCancel(){
            this.dictionaryTypeModal.show=false;
             this.$refs.ruleForm.clearValidate();
            },
            handleDialogSubmit(){
                 this.$refs.ruleForm.validate().then(()=>{
                   if(this.dictionaryTypeModal.type=="Add"){
             saveDictionaryType(this.dialogTreeForm).then(response=>{
              this.$XModal.message({
                                message: "新增成功",
                                status: "success"
                            });
              this.$emit('treeUpdata')
              this.dictionaryTypeModal.show=false;
            }).catch(err=>{
               this.$XModal.message({
                                message: "新增失败",
                                status: "error"
                            });
                
                this.dictionaryTypeModal.show=false;
            })    
        }else if(this.dictionaryTypeModal.type=="Edit"){
            modifyDictionaryType(this.dialogTreeForm).then(response=>{
              this.$XModal.message({
                                message: "修改成功",
                                status: "success"
                            });
              this.dictionaryTypeModal.show=false;
              this.$emit('treeUpdata')
            }).catch(err=>{
                console.log(err)
               this.$XModal.message({
                                message: "修改失败",
                                status: "error"
                            });
                this.dictionaryTypeModal.show=false;
            })    
        }
                 }).catch(err=>{
                   console.log(err)
                   return false;
                 })
            
          }
      
            }
    }

</script>


 
 