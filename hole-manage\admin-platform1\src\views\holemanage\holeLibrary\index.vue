<template>
  <el-container>
    <el-main>
      <!-- region 查询条件表单-->
      <vxe-form ref="queryConditionForm" title-width="100" title-align="right" span="8" :data="queryParams" @submit="handleQueryConditionFormSubmit" @toggle-collapse="handleQueryConditionFormToggleCollapse">
        <vxe-form-item field="hole_library_name" title="漏洞名称">
          <vxe-input clearable placeholder="漏洞名称" v-model="queryParams.hole_library_name"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="hole_library_cve" title="CVE">
          <vxe-input clearable placeholder="CVE" v-model="queryParams.hole_library_cve"></vxe-input>
        </vxe-form-item>

        <vxe-form-item align="center" collapse-node>
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>

        <vxe-form-item field="hole_library_vkb" title="VKB" folding>
          <vxe-input clearable placeholder="VKB" v-model="queryParams.hole_library_vkb"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="hole_library_cwe" title="CWE" folding>
          <vxe-input clearable placeholder="CWE" v-model="queryParams.hole_library_cwe"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="hole_library_cnnvd" title="CNNVD" folding>
          <vxe-input clearable placeholder="CNNVD" v-model="queryParams.hole_library_cnnvd"></vxe-input>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <!-- <el-button type="primary" icon="fa fa-plus" @click="handleAdd">
            新增</el-button> -->
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleImport" v-btnpermission="'hole:holelibrary:import'">导入文件</el-button>
          </el-col>
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table id="myTable" ref="myTable" v-loading="loading" element-loading-text="拼命加载中" border auto-resize resizable height="auto" show-overflow :sort-config="{ trigger: 'cell', remote: true }" @sort-change="sortChange" :custom-config="{ storage: true }" :data="page.records" :checkbox-config="{ trigger: 'row' }">
          <!-- <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column> -->
          <vxe-table-column title="序号" type="seq" width="60" fixed="left"></vxe-table-column>
          <vxe-table-column field="hole_library_name" title="漏洞名称"></vxe-table-column>
          <vxe-table-column field="hole_library_cve" title="cve"></vxe-table-column>
          <vxe-table-column field="hole_library_vkb" title="vkb"></vxe-table-column>
          <vxe-table-column field="hole_library_cwe" title="cwe"></vxe-table-column>
          <vxe-table-column field="hole_library_cnnvd" title="cnnvd"></vxe-table-column>
          <vxe-table-column field="file_real_name" title="补丁">
            <template slot-scope="scope">
              <el-link type="primary" v-if="scope.row.holePatchList !=null" @click="gowarn(scope.row)">补丁下载</el-link>
            </template>
          </vxe-table-column>
          <vxe-table-column field="" title="操作" fixed="right" width="">
            <template slot-scope="scope">
              <!-- <i class="el-icon-edit" style="font-size: 18px; color: #409EFF" @click="handleUpdate(scope.row)"></i> -->
              <i class="el-icon-view" style="font-size: 18px; color: #409EFF" @click="handleView(scope.row)"></i>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <vxe-modal ref="resourceFormModal" height="99%" width="700" position="center" resize :title="resourceFormModalTitle" :showFooter="!disabled">
        <vxe-form ref="resourceForm" title-align="right" title-width="100" :data="resourceInfo" :rules="resourceFormRules" prevent-submit>
          <vxe-form-item title="漏洞名称" field="hole_library_name" span="24">
            <template>
              <el-input type="textarea" :rows="3" clearable :disabled="disabled" v-model="resourceInfo.hole_library_name">
              </el-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="cve" field="hole_library_cve" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_cve" placeholder="请输入cve" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="vkb" field="hole_library_vkb" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_vkb" placeholder="请输入vkb" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="cwe" field="hole_library_cwe" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_cwe" placeholder="请输入cwe" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="cnnvd" field="hole_library_cnnvd" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_cnnvd" placeholder="请输入cnnvd" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="漏洞级别" field="hole_library_level" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_level" placeholder="请输入漏洞级别" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="漏洞类型" field="hole_library_type" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_type" placeholder="请输入漏洞类型" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="公布时间" field="hole_library_public_time" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.hole_library_public_time" placeholder="请输入公布时间" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="漏洞说明" field="hole_library_remarkString" span="24">
            <template>
              <el-input type="textarea" :rows="10" clearable :disabled="disabled" v-model="resourceInfo.hole_library_remarkString">
              </el-input>
            </template>
          </vxe-form-item>
        </vxe-form>
        <template v-slot:footer>
          <vxe-button type="button" content="取消" @click="$refs.resourceFormModal.close()"></vxe-button>
          <vxe-button type="button" status="primary" content="确定" @click="handleResourceFormModalConfirm"></vxe-button>
        </template>
      </vxe-modal>

      <el-dialog :title="resourceFormModalTitle" :visible.sync="handOPen" width="500px" append-to-body>
        <el-form ref="postOrderForm" :model="queryParams" label-width="80px">
          <el-upload ref="upload" :limit="1" accept=".xml" :headers="upload.headers" :data="uploadData" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <div class="el-upload__tip" slot="tip">

            </div>

            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>

            <!-- <div class="el-upload__tip" slot="tip">
            <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板</el-link>
          </div> -->
            <div class="el-upload__tip" style="color: red" slot="tip">
              提示：仅允许导入“xml”格式文件！
            </div>
          </el-upload>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <!-- region 分页-->
      <vxe-pager ref="pager" :current-page="queryParams.pagenumber" :page-size="queryParams.pagesize" :total="page.total" @page-change="handlePageChange">
      </vxe-pager>
      <!-- endregion -->
     </el-main>
  </el-container>
</template>

<script>
import { pageHoleLibrary, downloadHoleLibraryFile } from "@/api/holemanage/asset";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import AssetIpBatchEdit from "@/views/holemanage/asset/ip/components/AssetIpBatchEdit";
import { mapGetters } from "vuex";
import { getToken } from '@/utils/auth';

export default {
  name: "index",
  components: { DepartmentTree, AssetIpBatchEdit },
  data() {
    return {
      activeName: "0",
      tabsHeight: 0,
      handOPen: false,
      uploadData: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（文件导入）
        postOrder: {},
        // 弹出层标题（文件导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/holemanage/holeLibrary/importData',
      },
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      userList: [],
      loading: true,
      disabled: false, // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
      page: {
        total: 0,
        records: [],
      },
      resourceInfo: {
      },
      resourceFormRules: {

      },
      resourceFormModalTitle: "详细信息",
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        hole_library_id: null,
        hole_library_name: null,
        hole_library_level: null,
        hole_library_type: null,
        hole_library_cve: null,
        hole_library_vkb: null,
        hole_library_cwe: null,
        hole_library_cnnvd: null,
        hole_library_cvss: null,
        hole_library_attack_route: null,
        hole_library_attack_diff: null,
        hole_library_attack_auth: null,
        hole_library_privacy: null,
        hole_library_integrity: null,
        hole_library_usability: null,
        hole_library_remark: null,
        hole_library_repair: null,
        hole_library_referenceinfo: null,
      },

      // 设备类型
      deviceTypeEnum: [],
      // 系统类型
      systemTypeEnum: [],
      // 等级保护
      protectionEnum: [],
      // 重要程度
      importanceEnum: [],
      // 涉密状态
      secretEnum: [],
      // 资产状态枚举
      assetStatusEnum: [],
      // 系统负载枚举
      systemLoadEnum: [],

    };
  },
  created() {
    this.initData();
    this.loadTable();
  },

  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
  },
  methods: {
    initData() {
    },
    /**导入 */
    handleImport() {
      this.handOPen = true;
      this.resourceFormModalTitle = '导入文件';
    },
    handleAdd() { },
    handleUpdate() { },
    cancel() {
      this.handOPen = false;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.handOPen = false;
      this.$refs.upload.clearFiles();
      this.$alert("上传成功", '导入结果', { dangerouslyUseHTMLString: true });
      this.loadTable();
    },
    // 提交上传文件
    submitFileForm() {

      this.$refs.upload.submit();
      this.uploadData = {};
      // this.handOPen = false;
    },
    // 跳转地址
    gowarn(row) {
      this.handleWarnUserStatus(row);
      // const urlJson = JSON.parse(row.workorder_warn_url);
      this.$router.push({ path: '/holemanage/holePatch/index', query: { 'hole_patch_id': row.hole_patch_id } });
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      this.queryParams.is_read = this.activeName;
      this.loadTable();
    },
    handleWarnUserStatus(row) {


    },
    // 处理查看
    handleView(row) {
      console.log(row)
      this.$refs.resourceFormModal.open()
      this.disabled = true;
      this.resourceInfo = row;
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight =
        this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight =
          this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },

    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },

    /* 字段排序 */
    sortChange(e) {
      console.log("sortChange", e);
      const { field, order } = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },

    // 查询条件
    getQueryCondition() {

      this.queryParams.params = {};
      this.queryParams.params['beginCreatedTime'] = this.queryParams.startDate;
      this.queryParams.params['endCreatedTime'] = this.queryParams.endDate;

      this.queryParams.params['beginReadTime'] = this.queryParams.startDateRead;
      this.queryParams.params['endReadTime'] = this.queryParams.endDateRead;
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));

      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      pageHoleLibrary(this.getQueryCondition())
        .then((response) => {
          this.page.records = response.data.records;
          console.log(this.page.records)
          this.page.total = response.data.total;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },

  },
  computed: {
    ...mapGetters(["extraHeight"]),
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val;
    },
  },
};
</script>
<style scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
