<template>
    <el-container>
        <el-main>
            <!-- region 表格工具栏 -->
            <vxe-toolbar
                ref="toolbar"
                :refresh="{ query: loadRoleList }"
                custom
            >
                <template v-slot:buttons>
                    <el-button-group>
                        <el-button
                            type="primary"
                            icon="fa fa-plus"
                            @click="handleRoleAdd"
                             v-btnpermission="'security:role:add'"
                        >
                            新增</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-edit"
                            @click="handleRoleModify"
                             v-btnpermission="'security:role:modify'"
                        >
                            修改</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-trash"
                            @click="handleRoleDelete"
                             v-btnpermission="'security:role:del'"
                        >
                            删除</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-list-alt"
                            @click="handleRoleLook"
                        >
                            查看</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-link"
                            @click="handleResourceAssign"
                             v-btnpermission="'security:role:resourceassign'"
                        >
                            分配权限</el-button
                        >
                    </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->
            <!-- region 角色表格 -->
            <vxe-table
                id="roleTable"
                ref="roleTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                resizable
                 auto-resize
                :custom-config="{ storage: true }"
                :data="roles"
                :checkbox-config="{ trigger: 'row' }"
            >
                <vxe-table-column
                    type="checkbox"
                    width="50"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    title="序号"
                    type="seq"
                    width="60"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="rolename"
                    title="角色名称"
                ></vxe-table-column>
                <vxe-table-column
                    field="rolenote"
                    title="角色描述"
                ></vxe-table-column>
                <vxe-table-column
                    field="createdate"
                    title="创建时间"
                ></vxe-table-column>
            </vxe-table>

            <!-- endregion -->

            <!-- region 弹窗 角色新增|修改|查看-->
            <vxe-modal
                ref="roleFormModal"
                height="99%"
                width="600"
                position="center"
                resize
                :title="roleFormModalTitle"
                :showFooter="!disabled"
            >
                <vxe-form
                    ref="roleForm"
                    title-align="right"
                    title-width="100"
                    :data="roleInfo"
                    :rules="roleFormRules"
                    prevent-submit
                >
                    <vxe-form-item title="角色名称" field="rolename" span="24">
                        <template v-slot="scope">
                            <vxe-input
                                v-model="roleInfo.rolename"
                                placeholder="请输入角色名称"
                                clearable
                                :disabled="disabled"
                                @input="$refs.roleForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item title="角色描述" field="rolenote" span="24">
                        <template v-slot="scope">
                            <vxe-input
                                v-model="roleInfo.rolenote"
                                placeholder="请输入角色描述"
                                clearable
                                :disabled="disabled"
                                @input="$refs.roleForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="角色排序"
                        field="ordernumber"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="roleInfo.ordernumber"
                                type="number"
                                placeholder="请输入角色排序"
                                clearable
                                :disabled="disabled"
                                @input="$refs.roleForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                </vxe-form>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.roleFormModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleRoleFormModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->

            <!-- region 弹窗 分配权限-->
            <vxe-modal
                ref="resourceAssignModal"
                height="90%"
                width="400"
                position="center"
                resize
                title="分配权限"
                showFooter
            >
                <el-tree
                    ref="resourceTree"
                    node-key="resourcecode"
                    :data="resourceTreeNodes"
                    show-checkbox
                    check-on-click-node
                    default-expand-all
                    :expand-on-click-node="false"
                    check-strictly
                    :props="{ children: 'children', label: 'resourcenote' }"
                >
                </el-tree>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.resourceAssignModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleResourceAssignModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import {
    deleteRole,
    listRoles,
    modifyRole,
    oneRole,
    roleExists,
    saveRole,
    saveRoleResourceRelation
} from "@/api/security/role";
import {
    listResources,
    listRoleResourceRelations
} from "@/api/security/resource";
import { listToTreeList } from "@/utils/guozw-core";

export default {
    name: "index",
    data() {
        return {
            loading: true,
            disabled: false,
            roles: [],
            roleInfo: {
                rolecode: null,
                rolename: null,
                rolenote: null,
                ordernumber: null
            },
            roleFormModalTitle: null,
            roleFormRules: {
                rolename: [
                    { required: true, message: "请输入角色名称" },
                    {
                        pattern: "^[A-Z_]{5,30}$",
                        message: "只能输入 5 到 30 个大写英文字母或下划线"
                    }
                ],
                rolenote: [
                    { required: false, message: "请输入角色名称" },
                    { min: 0, max: 50, message: "长度在 0 到 50 个字符" }
                ],
                ordernumber: [{ required: true, message: "请输入角色排序" }]
            },
            resourceTreeConfig: {
                id: "resourcecode",
                parentId: "resourceparentcode",
                children: "children"
            },
            resourceTreeNodes: []
        };
    },
    created() {
        this.loadRoleList();
    },
    methods: {
        // 加载角色列表
        loadRoleList() {
            this.loading = true;
            listRoles()
                .then(response => {
                    this.roles = response.data;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },

        // 处理角色新增按钮点击
        handleRoleAdd() {
            this.disabled = false;
            Object.keys(this.roleInfo).forEach(
                key => (this.roleInfo[key] = "")
            );

            this.roleFormModalTitle = "新增角色";
            this.$refs.roleFormModal.open();
            this.$nextTick(() => {
                this.$refs.roleForm.clearValidate();
            });
        },
        // 处理角色修改按钮点击
        handleRoleModify() {
            this.disabled = false;

            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                oneRole({ rolecode: checkedRecords[0].rolecode }).then(
                    response => {
                        const roleInfo = response.data;
                        Object.keys(this.roleInfo).forEach(
                            key => (this.roleInfo[key] = roleInfo[key])
                        );
                        this.roleFormModalTitle = "修改角色";
                        this.$refs.roleFormModal.open();

                        this.$nextTick(() => {
                            this.$refs.roleForm.clearValidate();
                        });
                    }
                );
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        // 处理角色删除按钮点击
        handleRoleDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                this.$XModal
                    .confirm({
                        message: "确定要删除吗？",
                        position: "center",
                        status: "warning"
                    })
                    .then(type => {
                        if (type === "confirm") {
                            deleteRole({
                                rolecode: checkedRecords[0].rolecode
                            }).then(response => {
                                this.$XModal.message({
                                    message: "删除成功",
                                    status: "success"
                                });
                                this.loadRoleList();
                            });
                        }
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        // 处理角色查看按钮点击
        handleRoleLook() {
            this.disabled = true;

            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                oneRole({ rolecode: checkedRecords[0].rolecode }).then(
                    response => {
                        const roleInfo = response.data;
                        Object.keys(this.roleInfo).forEach(key => {
                            this.roleInfo[key] = roleInfo[key];
                        });
                        this.roleFormModalTitle = "查看角色";
                        this.$refs.roleFormModal.open();

                        this.$nextTick(() => {
                            this.$refs.roleForm.clearValidate();
                        });
                    }
                );
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理分配权限按钮点击
        handleResourceAssign() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                listResources()
                    .then(response => {
                        this.resourceTreeNodes = listToTreeList(
                            response.data,
                            this.resourceTreeConfig.id,
                            this.resourceTreeConfig.parentId
                        );
                    })
                    .then(() => {
                        listRoleResourceRelations({
                            rolecode: checkedRecords[0].rolecode
                        }).then(response => {
                            const roleResourceRelations = response.data;
                            const resourcecodes = roleResourceRelations.map(
                                (item, index, arr) => {
                                    return item.resourcecode;
                                }
                            );

                            this.$refs.resourceAssignModal.open();
                            this.$nextTick(() => {
                                this.$refs.resourceTree.setCheckedKeys(
                                    resourcecodes
                                );
                            });
                        });
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        // 处理分配权限弹窗确定按钮点击
        handleResourceAssignModalConfirm() {
            // 获取选中的资源编码
            const checkedKeys = this.$refs.resourceTree.getCheckedKeys();
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            saveRoleResourceRelation({
                rolecode: checkedRecords[0].rolecode,
                resourcecodes: checkedKeys
            }).then(response => {
                this.$refs.resourceAssignModal.close();
                this.$XModal.nessage({
                    message: "分配成功",
                    status: "success"
                });
            });
        },
        // 处理角色表单弹窗确定
        handleRoleFormModalConfirm() {
            this.$refs.roleForm
                .validate()
                .then(() => {
                    if (this.roleInfo.rolecode) {
                        // 修改角色
                        modifyRole(this.roleInfo).then(response => {
                            this.$refs.roleFormModal.close();
                            this.$XModal.message({
                                message: "修改成功",
                                status: "success"
                            });
                            this.loadRoleList();
                        });
                    } else {
                        // 新增角色
                        roleExists({ rolename: this.roleInfo.rolename })
                            .then(response => {
                                return response.data;
                            })
                            .then(response => {
                                // 角色已经存在
                                if (response) {
                                    this.$XModal.message({
                                        message: "角色已存在",
                                        status: "warning"
                                    });
                                } else {
                                    saveRole(this.roleInfo).then(response => {
                                        this.$refs.roleFormModal.close();
                                        this.$XModal.message({
                                            message: "新增成功",
                                            status: "success"
                                        });
                                        this.loadRoleList();
                                    });
                                }
                            });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        }
    }
};
</script>

<style scoped></style>
