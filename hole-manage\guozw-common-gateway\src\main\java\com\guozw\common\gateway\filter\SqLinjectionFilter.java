package com.guozw.common.gateway.filter;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.util.SqLinjectionRuleUtils;
import com.guozw.common.gateway.config.CustomPropertiesConfig;
import com.guozw.common.gateway.util.CommonUtils;
import io.netty.buffer.ByteBufAllocator;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.NettyDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Component
public class SqLinjectionFilter implements GlobalFilter, Ordered {

    @Autowired
    private CustomPropertiesConfig customPropertiesConfig;

    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain){
        log.info("自定义防XSS攻击网关全局过滤器生效");

        String[] whitelist = ArrayUtil.newArray(String.class, 0);
        if (StrUtil.isNotBlank(customPropertiesConfig.getXss_whitelist())) {
            whitelist = StrUtil.splitToArray(customPropertiesConfig.getXss_whitelist(), StrUtil.COMMA);
        }
        ServerHttpRequest serverHttpRequest = exchange.getRequest();
        HttpMethod method = serverHttpRequest.getMethod();
        String contentType = serverHttpRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
        URI uri = exchange.getRequest().getURI();
        String path = uri.getPath();

        if(isMatchPath(path, whitelist)){
            log.info("请求【{}】在sql注入过滤白名单中，直接放行", path);
            return chain.filter(exchange);
        }

        Boolean postFlag = (method == HttpMethod.POST || method == HttpMethod.PUT) &&
                (MediaType.APPLICATION_FORM_URLENCODED_VALUE.equalsIgnoreCase(contentType) || MediaType.APPLICATION_JSON_VALUE.equals(contentType));

        //过滤get请求
        if (method == HttpMethod.GET) {

            String rawQuery = uri.getRawQuery();
            if (StrUtil.isBlank(rawQuery)){
                return chain.filter(exchange);
            }

            log.info("请求参数为【{}】", rawQuery);
            boolean checkResult = SqLinjectionRuleUtils.getRequestSqlKeyWordsCheck(rawQuery);

            // 如果存在sql注入,直接拦截请求
            if (checkResult) {
                log.error("请求【" + uri.getRawPath() + uri.getRawQuery() + "】参数中包含不允许sql的关键词, 请求拒绝");
                return setUnauthorizedResponse(exchange);
            }

            // 透传参数，不对参数做任何处理
            return chain.filter(exchange);
        }
        // post请求时，如果是文件上传之类的请求，不修改请求消息体
        else if (postFlag){

            return DataBufferUtils.join(serverHttpRequest.getBody()).flatMap(d -> Mono.just(Optional.of(d))).defaultIfEmpty(
                            Optional.empty())
                    .flatMap(optional -> {
                        // 取出body中的参数
                        String bodyString = StrUtil.EMPTY;
                        if (optional.isPresent()) {
                            byte[] oldBytes = new byte[optional.get().readableByteCount()];
                            optional.get().read(oldBytes);
                            bodyString = new String(oldBytes, StandardCharsets.UTF_8);
                        }
                        HttpHeaders httpHeaders = serverHttpRequest.getHeaders();
                        // 执行XSS清理
                        log.info("请求方法【{}】-请求路径【{}】-XSS处理前参数【{}】", method, path, bodyString);
                        boolean checkResult = false;
                        // 如果MediaType是json才执行json方式验证
                        if (MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(contentType)) {
                            checkResult = SqLinjectionRuleUtils.postRequestSqlKeyWordsCheck(bodyString);
                        }
                        // form表单方式，需要走get请求
                        else if (MediaType.APPLICATION_FORM_URLENCODED_VALUE.equals(contentType)) {
                            checkResult = SqLinjectionRuleUtils.getRequestSqlKeyWordsCheck(bodyString);
                        }

                        log.info("请求方法【{}】-请求uri【{}】-XSS处理后参数【{}】", method, path, bodyString);

                        //  如果存在sql注入,直接拦截请求
                        if (checkResult) {
                            log.error("请求方法【{}】-请求路径【{}】-请求参数【{}】, 包含不允许sql的关键词，请求拒绝", method, path, bodyString);
                            return setUnauthorizedResponse(exchange);
                        }

                        // 由于请求参数可能发生改变，所以构造新的请求
                        ServerHttpRequest newRequest = serverHttpRequest.mutate().uri(uri).build();

                        // 重新构造body
                        byte[] newBytes = bodyString.getBytes(CharsetUtil.CHARSET_UTF_8);
                        DataBuffer bodyDataBuffer = toDataBuffer(newBytes);
                        Flux<DataBuffer> bodyFlux = Flux.just(bodyDataBuffer);

                        // 重新构造header
                        HttpHeaders headers = new HttpHeaders();
                        headers.putAll(httpHeaders);
                        // 由于修改了传递参数，需要重新设置CONTENT_LENGTH，长度是字节长度，不是字符串长度
                        int length = newBytes.length;
                        headers.remove(HttpHeaders.CONTENT_LENGTH);
                        headers.setContentLength(length);
                        headers.set(HttpHeaders.CONTENT_TYPE, contentType);
                        // 重写ServerHttpRequestDecorator，修改了body和header，重写getBody和getHeaders方法
                        newRequest = new ServerHttpRequestDecorator(newRequest) {
                            @Override
                            public Flux<DataBuffer> getBody() {
                                return bodyFlux;
                            }

                            @Override
                            public HttpHeaders getHeaders() {
                                return headers;
                            }
                        };

                        return chain.filter(exchange.mutate().request(newRequest).build());
                    });
        }

        return chain.filter(exchange);

    }


    // 自定义过滤器执行的顺序，数值越大越靠后执行，越小就越先执行
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    /**
     * 设置403拦截状态
     */
    private Mono<Void> setUnauthorizedResponse(ServerWebExchange exchange) {
        BaseResult result = BaseResult.forbidden().setMessage("请求参数存在sql注入风险，请求已被拒绝。");
        return CommonUtils.responseWrite(exchange, HttpStatus.FORBIDDEN.value(), result);
    }

    /**
     * 字节数组转DataBuffer
     *
     * @param bytes 字节数组
     * @return DataBuffer
     */
    private DataBuffer toDataBuffer(byte[] bytes) {
        NettyDataBufferFactory nettyDataBufferFactory = new NettyDataBufferFactory(ByteBufAllocator.DEFAULT);
        DataBuffer buffer = nettyDataBufferFactory.allocateBuffer(bytes.length);
        buffer.write(bytes);
        return buffer;
    }
    /**
     * 通配符匹配要过滤的路径
     * @param path 要匹配的路径
     * @param urls Spring通配符路径
     * @return
     */
    private static boolean isMatchPath(String path, String... urls){
        boolean isMatchPath = Arrays.stream(urls).anyMatch(igUrl -> {
            PathMatcher pm = new AntPathMatcher();
            return pm.match(igUrl, path);
        });
        return isMatchPath;
    }
}