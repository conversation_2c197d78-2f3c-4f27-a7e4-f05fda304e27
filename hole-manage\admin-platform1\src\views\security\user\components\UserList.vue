/* 用户列表 */
<template>
    <el-container>
        <el-main>
            <!-- region 查询条件表单-->
            <vxe-form
                ref="queryConditionForm"
                title-width="100"
                title-align="right"
                span="8"
                :data="queryParams"
                @submit="handleQueryConditionFormSubmit"
            >
                <vxe-form-item field="username" title="用户名">
                    <vxe-input
                        clearable
                        placeholder="用户名"
                        v-model="queryParams.username"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="nickname" title="用户昵称">
                    <vxe-input
                        clearable
                        placeholder="用户昵称"
                        v-model="queryParams.nickname"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item align="right">
                    <vxe-button
                        type="submit"
                        status="primary"
                        icon="fa fa-search"
                        >查询</vxe-button
                    >
                    <vxe-button type="reset" icon="fa fa-refresh"
                        >重置</vxe-button
                    >
                </vxe-form-item>
            </vxe-form>
            <!-- endregion-->

            <!-- region 表格工具栏 -->
            <vxe-toolbar
                ref="toolbar"
                :refresh="{ query: loadUserTable }"
                custom
            >
                <template v-slot:buttons>
                    <el-button-group> </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->

            <!-- region 表格 -->
            <vxe-table
                id="myTable"
                ref="myTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                border
                :max-height="maxHeight"
                :custom-config="{ storage: true }"
                :data="page.records"
                :checkbox-config="{ trigger: 'row' }"
                :radio-config="{ trigger: 'row' }"
            >
                <vxe-table-column
                    :type="type"
                    width="50"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    type="seq"
                    width="60"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="username"
                    title="用户名"
                ></vxe-table-column>
                <vxe-table-column
                    field="nickname"
                    title="用户昵称"
                ></vxe-table-column>
                <vxe-table-column
                    field="userphone"
                    title="用户手机"
                ></vxe-table-column>
                <vxe-table-column
                    field="createdate"
                    title="创建时间"
                ></vxe-table-column>
                <vxe-table-column
                    field="departmentname"
                    title="所属部门"
                ></vxe-table-column>
            </vxe-table>
            <!-- endregion-->

            <!-- region 分页-->
            <vxe-pager
                ref="pager"
                :current-page="queryParams.pagenumber"
                :page-size="queryParams.pagesize"
                :total="page.total"
                @page-change="handlePageChange"
            >
            </vxe-pager>
            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import { pageUsers } from "@/api/security/user";
import { copyProperty } from "@/utils/guozw-core";
export default {
    name: "UserList",
    components: {},
    props: {
        maxHeight: {
            type: [Number, String],
            default: 500
        },
        type: {
            type: String,
            default: "radio"
        },
        extraQueryCondition: {
            type: Object,
            default: () => {
                return {};
            }
        }
    },
    data() {
        return {
            queryConditionFormHeight: 0, //查询条件表单高度
            loading: true,
            page: {
                total: 0,
                records: []
            },
            // 查询条件
            queryParams: {
                pagenumber: 1,
                pagesize: 10,
                username: null,
                nickname: null,
                createdatestart: null,
                createdateend: null,
                isenabled: null,
                departmentcode: null,
                rolecodes: []
            }
        };
    },
    created() {
        this.loadUserTable();
    },

    mounted() {},
    methods: {
        // 处理查询条件表单提交
        handleQueryConditionFormSubmit({ data }) {
            this.queryParams.pagenumber = 1;
            this.loadUserTable();
        },
        getQueryCondition() {
            let queryCondition = JSON.parse(JSON.stringify(this.queryParams));
            Object.keys(this.extraQueryCondition).forEach(key => {
                if (queryCondition.hasOwnProperty(key)) {
                    queryCondition[key] = this.extraQueryCondition[key];
                }
            });

            console.log("getQueryCondition", queryCondition);
            return queryCondition;
        },
        // 加载右侧用户表格
        loadUserTable() {
            this.loading = true;
            pageUsers(this.getQueryCondition())
                .then(response => {
                    this.page.records = response.data.records;
                    this.page.total = response.data.total;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        // 处理页码变化
        handlePageChange({ currentPage, pageSize }) {
            this.queryParams.pagenumber = currentPage;
            this.queryParams.pagesize = pageSize;
            this.loadUserTable();
        },
        getCheckedRecords() {
            /* 获取选中记录 */
            const checkedRecords = this.$refs.myTable.getCheckboxRecords();
            return checkedRecords;
        },
        getRadioRecord() {
            const radioRecord = this.$refs.myTable.getRadioRecord();
            return radioRecord;
        }
    },

    watch: {}
};
</script>
