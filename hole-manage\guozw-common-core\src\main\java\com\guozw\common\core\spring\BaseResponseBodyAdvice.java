package com.guozw.common.core.spring;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.annotation.EncryptAnno;
import com.guozw.common.core.annotation.NotEncryptAnno;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.core.exception.CustomException;
import com.guozw.common.core.util.CommonUtils;
import com.guozw.common.core.util.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一响应处理、统一异常处理
 */
@RestControllerAdvice
@Slf4j
public class BaseResponseBodyAdvice implements ResponseBodyAdvice<Object> {
    private Class<EncryptAnno> encryptAnnoClass = EncryptAnno.class;
    private Class<NotEncryptAnno> notEncryptAnnoClass = NotEncryptAnno.class;

    /**
     * @param methodParameter
     * @param aClass
     * @return 返回true会执行拦截，返回false不执行拦截
     */
    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        o = encryptResponse(methodParameter, o);

        // o is null -> return response
        if (o == null) {
            return BaseResult.success();
        }
        // o is instanceof ConmmonResponse -> return o
        if (o instanceof BaseResult) {
            return (BaseResult) o;
        }
        // string 特殊处理
        if (o instanceof String) {
            return JSONUtil.toJsonStr(BaseResult.success().setData(o));
        }

        return BaseResult.success().setData(o);
    }

    /**
     * 响应数据加密
     * @param methodParameter
     * @param o 响应数据
     * @return
     */
    private Object encryptResponse(MethodParameter methodParameter, Object o) {
        EncryptAnno encryptAnno = methodParameter.getMethodAnnotation(encryptAnnoClass);
        if (ObjectUtil.isNull(encryptAnno)) {
            encryptAnno = methodParameter.getDeclaringClass().getAnnotation(encryptAnnoClass);
        }
        NotEncryptAnno notEncryptAnno = methodParameter.getMethodAnnotation(notEncryptAnnoClass);
        if (ObjectUtil.isNull(notEncryptAnno)) {
            notEncryptAnno = methodParameter.getDeclaringClass().getAnnotation(notEncryptAnnoClass);
        }
        if (ObjectUtil.isNull(notEncryptAnno) && ObjectUtil.isNotNull(encryptAnno) && ObjectUtil.isNotNull(o)) {
            String appSecret = TenantContextHolder.getTenantId();
            o = SecureUtil.aes(StrUtil.bytes(appSecret, CharsetUtil.UTF_8)).encryptBase64(JSONUtil.toJsonStr(o));
        }

        return o;
    }

    /**
     * 除数不能为0
     * @param e
     * @return
     */
    @ExceptionHandler(value = ArithmeticException.class)
    public BaseResult customExceptionHandler(ArithmeticException e) {
        log.error(ExceptionUtil.stacktraceToString(e));
        return new BaseResult(ErrorCodeEnum.DIVISIOR_ZERO);
    }

    /**
     * json参数校验失败
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public BaseResult customExceptionHandler(MethodArgumentNotValidException e) {
        log.error(ExceptionUtil.stacktraceToString(e));
        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
        String errorMessage = StrUtil.EMPTY;
        if (CollUtil.isNotEmpty(allErrors)) {
            errorMessage = allErrors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(";"));
        }
        return BaseResult.of(ErrorCodeEnum.PARAM_VALIDATE_FAIL).setMessage(errorMessage);
    }

    /**
     * 表单参数校验失败
     * @param e
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    public BaseResult customExceptionHandler(BindException e) {
        log.error(ExceptionUtil.stacktraceToString(e));
        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
        String errorMessage = StrUtil.EMPTY;
        if (CollUtil.isNotEmpty(allErrors)) {
            errorMessage = allErrors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(";"));
        }
        return BaseResult.of(ErrorCodeEnum.PARAM_VALIDATE_FAIL).setMessage(errorMessage);
    }
    /**
     * 处理自定义异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = CustomException.class)
    public BaseResult customExceptionHandler(CustomException e) {
        log.error(ExceptionUtil.stacktraceToString(e));
        return new BaseResult(e.getCode(), e.getMessage()).setStatus(Boolean.FALSE);
    }

    /**
     * 处理其他异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public BaseResult exceptionHandler(Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e));
        return BaseResult.failed().setMessage("系统出小差了，请稍后再试，或者联系系统管理员。").setData(ExceptionUtil.stacktraceToString(e));
    }
}
