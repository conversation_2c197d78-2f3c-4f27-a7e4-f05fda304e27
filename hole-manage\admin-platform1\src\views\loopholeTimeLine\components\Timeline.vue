<template>
  <div class="Timeline-container">
    <el-timeline v-bind="$attrs">
      <el-timeline-item
        hide-timestamp
        placement="top"
        v-for="item of timeDataList"
        :key="item.hole_plan_back_id"
        icon="el-icon-alarm-clock"
        color="transparent"
        size="large"
      >
        <el-card>
          <p class="date">{{ item.createdate }}</p>
          <div class="content">
            <p class="role" v-if="item.userNickName">{{ item.userNickName }}</p>
            <p class="operation">{{ backData(item.plan_back_type,item.plan_back_status).type }}</p>
            <p :class="['matters', true ? 'done' : '']">
              {{ backData(item.plan_back_type,item.plan_back_status).status }}
            </p>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  name: "Timeline",
  components: {},
  props: {
    timeDataList: {
      type: Array,
      require: true,
      default: () => []
    },
    planBackTypeEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    testStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    repairStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    retestStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    closeStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },

  },
  watch: {},
  computed: {
    backData() {
      return (type,status)=>{
        let backData = {};
        //处置修复
        if (type === '0') {
          if (status === '0') {
            backData.type = '关闭';
            backData.status = '修复'
          } else {
            backData.type = '开启';
            backData.status = '修复'
          }
        }
        //处置验证
        else if (type === '1') {
          if (status === '0') {
            backData.type = '关闭';
            backData.status = '验证'
          } else {
            backData.type = '开启';
            backData.status = '验证'
          }
        } //处置复测
        else if (type === '2') {
          if (status === '0') {
            backData.type = '关闭';
            backData.status = '复测'
          } else {
            backData.type = '开启';
            backData.status = '复测'
          }
        }
        //回执修复
        else if (type === '3') {
          backData.type = '标记为';
          for (let el of this.repairStatusEnum) {
            if (status === el.value) {
              backData.status = el.label;
              break;
            }
          }
        }
        //回执验证
        else if (type === '4') {
          backData.type = '标记为';
          for (let el of this.testStatusEnum) {
            if (status === el.value) {
              backData.status = el.label;
              break;
            }
          }
        }
        //回执复测
        else if (type === '5') {
          backData.type = '标记为';
          for (let el of this.retestStatusEnum) {
            if (status === el.value) {
              backData.status = el.label;
              break;
            }
          }
        }
        //回执复测
        else if (type === '6') {
          backData.type = '标记为';
          for (let el of this.closeStatusEnum) {
            if (status === el.value) {
              backData.status = el.label;
              break;
            }
          }
        }
        return backData;
      }
    }


  },
  data() {
    return {}
  },
  created() {
  },
  mounted() {
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.Timeline-container {
  margin-left: 30%;

  p {
    padding: 0;
    margin: 0;
  }

  /deep/ .el-timeline {
    .el-timeline-item {
      .el-timeline-item__tail {
        height: 70%;
        top: 20%;
      }

      .el-timeline-item__icon {
        color: #45b1f6;
        font-weight: bold;
        font-size: 20px;
      }

      .el-timeline-item__wrapper {
        .el-timeline-item__content {
          position: relative;
          //   &::before {
          //     content: "";
          //     position: absolute;
          //     top: 0;
          //   }
          .el-card {
            .date {
              color: #45b1f6;
              font-weight: bold;
              font-size: 12px;
            }

            .content {
              display: flex;
              align-items: center;

              > p {
                margin-right: 10px;
                font-size: 12px;
              }

              .done {
                padding: 0px 5px;
                color: #fff;
                background: red;
              }
            }
          }
        }
      }

      &:nth-of-type(even) {
        .el-timeline-item__wrapper {
          left: -110%;
          //   .el-timeline-item__content {
          //     border: 10px solid #fff;
          //     border-left-color: transparent;
          //   }
        }
      }
    }
  }
}
</style>
