<template>
  <el-container v-loading="mainLoading">
    <el-aside width="200px">
      <department-tree
        ref="departmentTree"
        :show-hole-basic-website-count="true"
        :show-action-button="false"
        :treeHeight="leftDeptTreeHeight"
        @department-tree-node-click="handleDepartmentTreeNodeClick"
      ></department-tree>
    </el-aside>
    <el-main>
      <el-tabs
        ref="tabs"
        v-model="activeName"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="需关注" name="0"></el-tab-pane>
        <el-tab-pane label="已关闭" name="1"></el-tab-pane>
        <el-tab-pane label="白名单" name="2"></el-tab-pane>
        <el-tab-pane label="误报" name="3"></el-tab-pane>
        <el-tab-pane label="全部" name="4"></el-tab-pane>
      </el-tabs>
      <!-- region 查询条件表单-->
      <vxe-form
        ref="queryConditionForm"
        title-width="100"
        title-align="right"
        span="8"
        :data="queryParams"
        @submit="handleQueryConditionFormSubmit"
        @toggle-collapse="handleQueryConditionFormToggleCollapse"
      >
        <vxe-form-item field="hole_name" title="漏洞名称">
          <vxe-input
            clearable
            placeholder="漏洞名称"
            v-model="queryParams.hole_name"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item
          field="hole_cve"
          title="CVE编号"

        >
          <vxe-input
            clearable
            placeholder="CVE编号"
            v-model="queryParams.hole_cve"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item align="left" collapse-node>
          <vxe-button
            type="submit"
            status="primary"
            icon="fa fa-search"
          >查询
          </vxe-button
          >
          <vxe-button type="reset" icon="fa fa-refresh"
          >重置
          </vxe-button
          >
        </vxe-form-item>
        <vxe-form-item field="hole_risk" title="风险值" folding>
          <vxe-input type="number" v-model="queryParams.hole_risk_gt" style="width: 30%" :min="1"
                     :max="10"></vxe-input>
          至
          <vxe-input type="number" v-model="queryParams.hole_risk_lt" style="width: 30%" :min="1"
                     :max="10"></vxe-input>

        </vxe-form-item>

        <vxe-form-item field="hole_type" title="漏洞类型" folding>
          <el-select
            v-model="queryParams.hole_type"
            clearable
            filterable
            placeholder="漏洞类型"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in holeTypeEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_discovery_tool" title="探针品牌" folding>
          <el-select
            v-model="queryParams.hole_discovery_tool"
            clearable
            filterable
            placeholder="探针品牌"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in toolBasicEnum"
              :key="item.tool_basic_id"
              :label="item.tool_name"
              :value="item.tool_basic_id"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_discovery_status" title="发现状态" folding>
          <el-select
            v-model="queryParams.hole_discovery_status"
            clearable
            filterable
            placeholder="发现状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in discoveryStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_test_status" title="验证状态" folding>
          <el-select
            v-model="queryParams.hole_test_status"
            clearable
            filterable
            placeholder="验证状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in testStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_repair_status" title="修复状态" folding>
          <el-select
            v-model="queryParams.hole_repair_status"
            clearable
            filterable
            placeholder="修复状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in repairStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_retest_status" title="复测状态" folding>
          <el-select
            v-model="queryParams.hole_retest_status"
            clearable
            filterable
            placeholder="复测状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in retestStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>

        <vxe-form-item field="hole_discovery_method" title="发现方式" folding>
          <el-select
            v-model="queryParams.hole_discovery_method"
            clearable
            filterable
            placeholder="发现方式"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in discoveryMethodEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_level" title="漏洞级别" folding>
          <el-select
            v-model="queryParams.hole_level"
            clearable
            filterable
            placeholder="漏洞级别"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in holeLevelEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="asset_ipv4" title="IP地址" folding>
          <vxe-input
            clearable
            placeholder="IP地址"
            v-model="queryParams.asset_ipv4"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="asset_website_name" title="网站名称" folding>
          <vxe-input
            clearable
            placeholder="网站名称"
            v-model="queryParams.asset_website_name"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="contactPhone" title="资产联系人" folding>
          <vxe-input
            clearable
            placeholder="资产联系人"
            v-model="queryParams.contactPhone"
          ></vxe-input>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <el-dropdown @command="openHolePlanBackModal">
            <el-button type="primary">
              处置计划<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item of planEnum" :command="item.value" v-btnpermission="item.btnpermission">{{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="openHolePlanBackModal">
            <el-button type="primary">
              反馈计划<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item of backEnum" :command="item.value" v-btnpermission="item.btnpermission">{{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            type="primary"
            icon="el-icon-message"
            @click="openHolePlanBackModal('8')"
            v-btnpermission="'holemanage:websitedetail:planbackfalse'"
          >
            误报
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-message"
            @click="openHolePlanBackModal('7')"
            v-btnpermission="'holemanage:websitedetail:planbackwhite'"
          >
            白名单
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-message"
            @click="handleRectificationInform"
          >
            整改通知
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-message"
            @click="sendEmailShow = true"
            v-btnpermission="'holemanage:websitedetail:sendemail'"
          >
            邮件通知
          </el-button>
          <el-button
            type="danger"
            icon="fa fa-trash"
            @click="handleBatchDelete"
            v-btnpermission="'holemanage:websitedetail:del'"
          >
            批量删除
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-printer"
            v-btnpermission="'holemanage:websitedetail:export'"
            @click="exportExcel">
            导出
          </el-button
          >
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="myTable"
          ref="myTable"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          auto-resize
          resizable
          height="auto"
          show-overflow
          :sort-config="{ trigger: 'cell', remote: true }"
          @sort-change="sortChange"
          :custom-config="{ storage: true }"
          :data="page.records"
          :checkbox-config="{ trigger: 'row' }"
          row-class-name="cellx"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="asset_website_name"
            title="网址名称"
            width="130"
          >
            <template v-slot="{ row }">
              <el-link type="primary" @click="routeHoleBasicIpDetail(row)">{{ row.asset_website_name }}</el-link>
            </template>
          </vxe-table-column>
          <vxe-table-column field="departmentname" title="隶属关系" width="150"/>
          <vxe-table-column
            field="affectUrl"
            title="影响URL"
            width="130"
          ></vxe-table-column>
          <vxe-table-column field="hole_name" title="漏洞名称" width="350">
            <template v-slot="{row}">
              <el-link type="primary" @click="routeHoleBasicIpDetail(row)">{{ row.hole_name }}</el-link>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="hole_level"
            title="漏洞级别"
            min-width="100"
            :formatter="['formatSelect', holeLevelEnum]"
          ></vxe-table-column>
          <vxe-table-column
            field="hole_discovery_status"
            title="发现状态"
            min-width="100"
            :formatter="['formatSelect', discoveryStatusEnum]"
          ></vxe-table-column>
          <vxe-table-column
            field="hole_discovery_method"
            title="发现方式"
            min-width="100"
            :formatter="['formatSelect', discoveryMethodEnum]"
          ></vxe-table-column>
          <vxe-table-column
            field="hole_test_status"
            title="验证状态"
            min-width="100"
            :formatter="['formatSelect', testStatusEnum]"
            style="height: 25px"
          >
            <template v-slot="{row}">
              <span>
                {{ formatterEnum(row.hole_test_status, testStatusEnum) }}

              </span>

            </template>

          </vxe-table-column>
          <vxe-table-column
            field="hole_retest_status"
            title="复测状态"
            min-width="100"
            :formatter="['formatSelect', retestStatusEnum]"
          >

            <template v-slot="{row}">
              <span>
                {{ formatterEnum(row.hole_retest_status, retestStatusEnum) }}
              </span>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="hole_repair_status"
            title="修复状态"
            min-width="100"
            :formatter="['formatSelect', repairStatusEnum]"
          >
            <template v-slot="{row}">
              <span>
                {{ formatterEnum(row.hole_repair_status, repairStatusEnum) }}

              </span>

            </template>
          </vxe-table-column>
          <vxe-table-column
            field="hole_close_status"
            title="关闭状态"
            min-width="100"
            :formatter="['formatSelect', closeStatusEnum]"
          ></vxe-table-column>
<!--          <vxe-table-column
            field=""
            title="生命周期"
            fixed="right"
            min-width="100"
          >
            <template v-slot="{ row }">
              <i @click="showTimeLine(row)">
                <svg-icon icon-class="timeline" style="font-size: 25px"/>
              </i>
            </template>
          </vxe-table-column>-->

        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager
        ref="pager"
        :current-page="queryParams.pagenumber"
        :page-size="queryParams.pagesize"
        :total="page.total"
        @page-change="handlePageChange"
      >
      </vxe-pager>
      <!-- endregion -->
      <hole-plan-back-modal
        ref="holePlanBackModal"
        :modal-show="holePlanBackModalShow"
        :close-enum="closeStatusEnum"
        :repair-enum="repairStatusEnum"
        :retest-enum="retestStatusEnum"
        :test-enum="testStatusEnum"
        :plan-back-type="planBackType"
        :is-hole-website="true"
        :user-list="userList"
        :email-template="emailTemplate"
        asset-type="2"
        @refreshTable="refreshTable"
        @close="holePlanBackModalShow = false"
      />
      <vxe-modal
        height="99%"
        width="30%"
        position="centers"
        resize
        title="发送邮件"
        v-model="sendEmailShow"
        showFooter
      >
        <send-email-form ref="sendEmailForm" :show="true" :user="userList"
                         :email-template="emailTemplate"></send-email-form>
        <template v-slot:footer>
          <el-button type="" @click="closeEmailModal">取消</el-button>
          <el-button type="primary" :loading="loading" @click="submitEmailForm"
          >确定
          </el-button
          >
        </template>
      </vxe-modal>
      <div class="loopholeTimeLine-container">

        <el-drawer
          title="漏洞时间线"
          :visible.sync="drawerShow"
          direction="rtl"
          size="35%"
        >
          <Timeline :timeDataList="timelineData" :plan-back-status-enum="planBackTypeEnum"
                    :close-status-enum="closeStatusEnum" :repair-status-enum="repairStatusEnum"
                    :retest-status-enum="retestStatusEnum" :test-status-enum="testStatusEnum"/>
        </el-drawer>
      </div>
      <AssetWebsiteEdit
        :modalInfo="editModalInfo"
        :formDefaultData="formDefaultData"
        :deviceTypeEnum="deviceTypeEnum"
        :systemTypeEnum="systemTypeEnum"
        :systemLoadEnum="systemLoadEnum"
        :protectionEnum="protectionEnum"
        :importanceEnum="importanceEnum"
        :secretEnum="secretEnum"
        :assetStatusEnum="assetStatusEnum"
      ></AssetWebsiteEdit>
      <rectification-reform-modal
        ref="reformModal"
        :modal-show="rectificationReformModalShow"
        :hole-basic-info="rectificationReformModalBasicInfo"
        :department-list="rectificationReformModalDepartmentList"
        :hole-type="'2'"
        :user-list="userList"
        :email-template="emailTemplate" @close="handleReformModalClose"/>
    </el-main>
  </el-container>
</template>

<script>
import {
  sendEmail,
  pageHoleWebsite,
  listHoleBasicWebsite,
  deleteHoleBasicIp,
  batchDeleteHoleBasicWebsite,
  pageAssetWebsite
} from "@/api/holemanage/asset";
import AssetWebsiteEdit from "@/views/holemanage/asset/website/components/AssetWebsiteEdit";
import {listConfigEmailTemplate} from "@/api/config/email";
import {listUser, pageUsers} from "@/api/security/user"
import {listDictionarys} from "@/api/security/dictionary";
import Timeline from "@/views/loopholeTimeLine/components/Timeline";
import holePlanBackModal from "@/views/holemanage/hole/ip/components/holePlanBackModal";
import sendEmailForm from "@/views/holemanage/hole/ip/components/sendEmailForm";
import departmentTree from "@/views/security/department/components/DepartmentTree";
import {mapGetters} from "vuex";
import {listToolBasic} from "@/api/testManage";
import {getFile} from "@/utils/file";
import rectificationReformModal from "@/views/holemanage/hole/ip/components/rectificationReformModal";
import {listDepartments} from "@/api/security/department";


export default {
  name: "ipDetail",
  components: {holePlanBackModal, sendEmailForm, departmentTree, Timeline, AssetWebsiteEdit,rectificationReformModal},
  data() {
    return {
      activeName: "0",
      tabsHeight: 54,
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      loading: true,
      mainLoading: false,
      page: {
        total: 0,
        records: []
      },
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        hole_name: '',
        hole_type: '',
        needNotice: 1,
        hole_cve: '',
        hole_discovery_tool: '',
        hole_discovery_status: '',
        hole_test_status: '',
        hole_repair_status: '',
        hole_retest_status: '',
        hole_close_status: '',
        hole_white_status: '',
        hole_false_status: '',
        hole_discovery_method: '',
        hole_level: '',
        asset_ipv4: '',
        asset_website_name: '',
        contactPhone: '',
      },

      //漏洞类型
      holeTypeEnum: [],
      toolBasicEnum: [],
      //发现状态
      discoveryStatusEnum: [],
      //验证状态
      testStatusEnum: [],
      //修复状态
      repairStatusEnum: [],
      //复测状态
      retestStatusEnum: [],
      //关闭状态
      closeStatusEnum: [],
      //发现方式
      discoveryMethodEnum: [],
      //是否
      enumCommon: [],
      //漏洞级别
      holeLevelEnum: [],
      //漏洞级别
      deviceTypeEnum: [],
      //处置反馈类型
      planBackTypeEnum: [],
      planBackStatusEnum: [],
      systemTypeEnum: [],
      systemLoadEnum: [],
      protectionEnum: [],
      importanceEnum: [],
      secretEnum: [],
      assetStatusEnum: [],
      holePlanBackModalShow: false,
      sendEmailShow: false,
      userList: [],
      emailTemplate: [],
      planBackType: '',
      leftDeptTreeHeight: 400,
      asset_website_id: '',
      deleteButtonVisible: false,
      drawerShow: false,
      timelineData: [],
      formDefaultData: {},
      editModalInfo: {},
      rectificationReformModalShow: false,
      rectificationReformModalBasicInfo: {},
      rectificationReformModalDepartmentList:[],
      departmentList: [],
    };
  },
  created() {
    this.asset_website_id = this.$route.query.asset_website_id;

    this.initData();
    this.loadTable();
    this.test();
  },

  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
    this.windowResize();

  },
  methods: {
    test() {
      var pattern = /^([a-zA-Z\d][\w-]{2,})@(\w{2,})\.([a-z]{2,})(\.[a-z]{2,})?$/;
      let str = '<EMAIL>';
      console.log('正则表达式', pattern.test(str));
    },
    initData() {
      // 加载字典
      listDictionarys().then(res => {
        res.data.forEach(item => {
          const dict = {
            label: item.dictionaryname,
            value: item.dictionaryvalue
          };
          // 漏洞类型
          if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeType
              .dictionarytypecode
          ) {
            this.holeTypeEnum.push(dict);
          }
          //发现状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeDiscoveryMethod
              .dictionarytypecode
          ) {
            this.discoveryMethodEnum.push(dict)
          }
          //漏洞级别
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeLevel
              .dictionarytypecode
          ) {
            this.holeLevelEnum.push(dict)
          }
          //验证状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeTestStatus
              .dictionarytypecode
          ) {
            this.testStatusEnum.push(dict)
          }
          //修复
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRepairStatus
              .dictionarytypecode
          ) {
            this.repairStatusEnum.push(dict)
          }
          //复测
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRetestStatus
              .dictionarytypecode
          ) {
            this.retestStatusEnum.push(dict)
          }
          //关闭
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeCloseStatus
              .dictionarytypecode
          ) {
            this.closeStatusEnum.push(dict)
          }

          //设备类型
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.deviceType
              .dictionarytypecode
          ) {
            this.deviceTypeEnum.push(dict)
          }
          //处置反馈类型
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holePlanBackType
              .dictionarytypecode
          ) {
            this.planBackTypeEnum.push(dict)
          }
          //系统负载
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.systemLoad
              .dictionarytypecode
          ) {
            this.systemTypeEnum.push(dict)
          }
          //系统类型
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.systemType
              .dictionarytypecode
          ) {
            this.systemLoadEnum.push(dict)
          }
          //等级保护
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.protection
              .dictionarytypecode
          ) {
            this.protectionEnum.push(dict)
          }
          //重要程度
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.importance
              .dictionarytypecode
          ) {
            this.importanceEnum.push(dict)
          }
          //涉密状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.secret
              .dictionarytypecode
          ) {
            this.secretEnum.push(dict)
          }
          //资产状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.assetStatus
              .dictionarytypecode
          ) {
            this.assetStatusEnum.push(dict)
          }
          //资产状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeDiscoveryStatus
              .dictionarytypecode
          ) {
            this.discoveryStatusEnum.push(dict)
          }
        });
      });
      this.enumCommon = this.$guozwSettings.enumCommon;
      listDepartments().then(res =>{
        this.departmentList = res.data;
      });
      pageUsers({
        pagenumber: 1,
        pagesize: 1000
      }).then(({ code, data }) => {
        this.userList = data.records
      });
      listConfigEmailTemplate({}).then(res => {
        this.emailTemplate = res.data;
      })
      listToolBasic().then(res => {
        this.toolBasicEnum = res.data;
      })
    },
    showTimeLine(row) {
      this.timelineData = row.holePlanBackVOList;
      this.drawerShow = true;
    },
    // 处理tabs点击
    handleTabClick(tab, event) {

      let activeNmae = this.activeName;
      //需关注
      if (activeNmae === '0') {
        this.queryParams.needNotice = 1;
        this.queryParams.hole_close_status = '';
        this.queryParams.hole_white_status = '';
        this.queryParams.hole_false_status = '';
      }
      //已关闭
      else if (activeNmae === '1') {
        this.queryParams.needNotice = '';
        this.queryParams.hole_close_status = '1';
        this.queryParams.hole_white_status = '';
        this.queryParams.hole_false_status = '';
      }
      //白名单
      else if (activeNmae === '2') {
        this.queryParams.needNotice = '';
        this.queryParams.hole_close_status = '';
        this.queryParams.hole_white_status = '1';
        this.queryParams.hole_false_status = '';
      }
      //误报
      else if (activeNmae === '3') {
        this.queryParams.needNotice = '';
        this.queryParams.hole_close_status = '';
        this.queryParams.hole_white_status = '';
        this.queryParams.hole_false_status = '1';
      } else {
        this.queryParams.needNotice = '';
        this.queryParams.hole_close_status = '';
        this.queryParams.hole_white_status = '';
        this.queryParams.hole_false_status = '';
      }
      this.loadTable();
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight;
      this.leftDeptTreeHeight = window.innerHeight - this.extraHeight  - 35;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this;
      window.onresize = () => {
        return (() => {
          that.setTableHeight();
          console.log(
            // "窗口resize-----------------" + that.tableHeight
          );
        })();
      };
    },

    exportExcel(){
      let queryCondition = this.getQueryCondition();
      queryCondition.type = 'holeBasicWebsite';
      console.log(queryCondition);
      getFile(null, queryCondition, '网站漏洞');
    },
    // 处理查看
    handleView(row) {
      pageHoleWebsite({assetIpIdList: [row.asset_website_id]}).then(res => {
        if (res.data.records.length > 0) {
          this.formDefaultData = res.data.records[0];
          this.editModalInfo = {
            title: "查看资产",
            show: true,
            isActionView: true
          };
        } else {
          this.$XModal({
            message: '未找到网站资产！',
            status: 'warning'
          })
        }
      })


    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({data}) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },

    // 处理页码变化
    handlePageChange({currentPage, pageSize}) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },

    /* 字段排序 */
    sortChange(e) {
      console.log("sortChange", e);
      const {field, order} = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },
    refreshTable() {
      this.loadTable();
    },
    // 查询条件
    getQueryCondition() {
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
      quryCondition.asset_website_id = this.asset_website_id;
      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      pageHoleWebsite(this.getQueryCondition())
        .then(response => {
          this.page.records = response.data.records;
          this.page.total = response.data.total;
          this.loading = false;
          console.log('deviceTypeEnum', this.deviceTypeEnum);
        })
        .catch(error => {
          this.loading = false;
        });
    },

    // 处理左侧部门树节点点击
    handleDepartmentTreeNodeClick(node) {
      this.queryParams.asset_department_id = node.departmentcode;
      this.loadTable();
    },
    closeEmailModal() {
      this.sendEmailShow = false;
      this.$refs.sendEmailForm.clearValidate();
    },
    submitEmailForm() {
      this.$refs.sendEmailForm.getFormDate().then(res => {
        console.log('res', res);
        saveAndSendEmail(res).then(res => {
          console.log('senEmail', res);
          this.$XModal.message({
            message: "发送成功",
            status: "success"
          });
          this.$refs.sendEmailForm.clearValidate();
          this.sendEmailShow = false;
        })
      })
    },
    openHolePlanBackModal(value) {
      this.planBackType = value;
      this.mainLoading = true;
      // 获取表格选中的记录
      const checkedRecords = this.$refs.myTable.getCheckboxRecords();
      if (checkedRecords && checkedRecords.length > 0) {
        if (checkedRecords.length === this.$refs.pager.pageSize && this.page.total / this.queryParams.pagesize > 1) {
          this.$XModal.message({
            message: "确定只选择本页数据嘛？如果需要选择全部数据，请不要勾选记录。",
            status: "warning"
          });
        }
        //反馈修复
        if (value === '3') {
          for (let i = 0; i < checkedRecords.length; i++) {
            let checkRecord = checkedRecords[i];
            if (checkRecord.hole_repair_status != '0') {
              this.$XModal.message({
                message: "漏洞：" + checkRecord.hole_name + "，修复计划未开启！",
                status: "warning"
              })
              return;
            }
          }
          this.holePlanBackModalShow = true;
        }
        //反馈验证
        else if (value === '4') {
          for (let i = 0; i < checkedRecords.length; i++) {
            let checkRecord = checkedRecords[i];
            console.log('4', checkRecord);
            if (checkRecord.hole_test_status != '0') {
              this.$XModal.message({
                message: "漏洞：" + checkRecord.hole_name + ",验证计划未开启！",
                status: "warning"
              })
              return;
            }
          }
          this.holePlanBackModalShow = true;
        }
        //反馈复测
        else if (value === '5') {
          for (let i = 0; i < checkedRecords.length; i++) {
            let checkRecord = checkedRecords[i];
            if (checkRecord.hole_retest_status != '0') {
              this.$XModal.message({
                message: "漏洞：" + checkRecord.hole_name + "，复测计划未开启！",
                status: "warning"
              })
              return;
            }
          }
          this.holePlanBackModalShow = true;
        }
        //反馈复测
        else {
          this.holePlanBackModalShow = true;
        }
        this.$refs.holePlanBackModal.holeBasicIpList = checkedRecords;
        this.mainLoading = false;
      } else {

        let queryData = this.getQueryCondition();
        let modalMessage = '';
        //反馈修复
        if (value === '3') {
          queryData.hole_repair_status = '0';
          modalMessage = '当前并无开启修复计划的漏洞！'
          console.log(3)
        }
        //反馈验证
        else if (value === '4') {
          queryData.hole_test_status = '0';
          modalMessage = '当前并无开启验证计划的漏洞！'
          console.log(4)


        }
        //反馈复测
        else if (value === '5') {
          queryData.hole_retest_status = '0';
          modalMessage = '当前并无开启复测计划的漏洞！'
          console.log(5)

        } else {
          modalMessage = '当前并无可用数据！'
        }
        console.log(value)
        listHoleBasicWebsite(queryData).then(res => {
          if (res.data.length > 0) {
            this.$refs.holePlanBackModal.holeBasicIpList = res.data;
            this.holePlanBackModalShow = true;
          } else {
            this.$XModal.message({
              message: modalMessage,
              status: 'warning'
            })
          }
        }).finally(e => {
          this.mainLoading = false;
        });
      }
    },
    //批量删除
    handleBatchDelete() {

      // 获取表格选中的记录
      const checkedRecords = this.$refs.myTable.getCheckboxRecords();
      let queryCondition = null
      let holeBasicWebsiteIdList = []
      let content = ''
      if (!checkedRecords || checkedRecords.length == 0) {
        content = '确定要批量删除当前查询条件下的所有记录吗？'
        queryCondition = this.getQueryCondition()
        queryCondition = JSON.stringify(this.getQueryCondition());
      } else {
        content = '确定要批量删除当前选中的记录吗？'
        checkedRecords.forEach(item => {
          holeBasicWebsiteIdList.push(item.hole_basic_website_id)
        })
      }
      this.$XModal
        .confirm({
          message: content,
          position: "center",
          status: "warning"
        })
        .then(type => {
          if (type === "confirm") {
            batchDeleteHoleBasicWebsite({holeBasicWebsiteIdList, queryCondition}).then(res => {
              this.$XModal.message({
                message: "批量删除成功",
                status: "success"
              });
              this.loadTable()
            })
          }
        });


    },
    //路由到ip漏洞详情
    routeHoleBasicIpDetail(row) {
      this.$router.push({
        path: "/holemanage/hole/ip/holeBasicIpDetail",
        query: {
          holeBasicInfo: row,
          enumData: {
            holeLevelEnum: this.holeLevelEnum,
            holeTypeEnum: this.holeTypeEnum,
            holeDiscoveryMethodEnum: this.discoveryMethodEnum,
            testStatusEnum: this.testStatusEnum,
            retestStatusEnum: this.retestStatusEnum,
            closeStatusEnum: this.closeStatusEnum,
            repairStatusEnum: this.repairStatusEnum,
            planBackTypeEnum: this.planBackTypeEnum
          },
          holeType: '2'
        }
      })
    },
    //处理整改通知按钮
    handleRectificationInform(){
      // 获取表格选中的记录
      const checkedRecords = this.$refs.myTable.getCheckboxRecords();
      if (checkedRecords && checkedRecords.length == 1) {
        let data = checkedRecords[0];
        if(data.hole_repair_status){
          this.$XModal.message({
            message: '选中的数据已开启整改计划！',
            status:'warning'
          })
          return;
        }

        this.rectificationReformModalBasicInfo = data;
        this.rectificationReformModalShow = true;
        let ary = Array.prototype.slice.call(this.departmentList);
        let departmentList = [];
        for (let t of ary) {
          if (t.departmentparentcode === this.$store.getters.userInfo.departmentcode){
            departmentList.push(t);
          }
        }
        this.rectificationReformModalDepartmentList = departmentList;
        console.log(this.$store.getters.userInfo);
      }else {
        this.$XModal.message({
          message: '只可勾选一条数据！',
          status:'warning'
        })
      }
    },
    handleReformModalClose(){
      this.rectificationReformModalShow = false;
      this.loadTable();
    },

  },
  computed: {
    ...mapGetters(["extraHeight"]),
    planEnum() {
      let ary = this.planBackTypeEnum.slice(0, 3);
      console.log('planEnum', ary, this.planBackTypeEnum);
      for (let aryElement of ary) {
        if (aryElement.value === '0'){
          aryElement.btnpermission = 'holemanage:websitedetail:planrepair';
        }else if(aryElement.value === '1'){
          aryElement.btnpermission = 'holemanage:websitedetail:plantest';
        }else if(aryElement.value === '2'){
          aryElement.btnpermission = 'holemanage:websitedetail:planretest';
        }
      }
      return ary;
    },
    backEnum() {
      let ary = this.planBackTypeEnum.slice(3, 7);
      for (let aryElement of ary) {
        if (aryElement.value === '3'){
          aryElement.btnpermission = 'holemanage:websitedetail:planbackrepair';
        }else if(aryElement.value === '4'){
          aryElement.btnpermission = 'holemanage:websitedetail:planbacktest';
        }else if(aryElement.value === '5'){
          aryElement.btnpermission = 'holemanage:websitedetail:planbackretest';
        }else if(aryElement.value === '6'){
          aryElement.btnpermission = 'holemanage:websitedetail:planbackclose';
        }
      }

      return ary;
    },
    userEnum() {
      let userEnum = [];
      for (let userElement of this.user) {
        userEnum.push({label: userElement.nickname, value: userElement.usercode})
      }
      return userEnum;
    },
    formatterEnum: function () {
      return (value, enumList) => {
        for (let el of enumList) {
          if (el.value === value) {
            return el.label;
          }
        }
      }

    },
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      console.log("监听查询条件表单高度变化--" + val);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val;
    }
  }
};
</script>
<style lang="scss" scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}

.loopholeTimeLine-container {
  /deep/ .el-drawer__body {
    display: flex;
    justify-content: flex-end;
    margin-right: 10%;
  }
}

.cellx {
  height: 50px;
}
</style>
