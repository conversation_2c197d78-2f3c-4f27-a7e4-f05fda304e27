package com.guozw.common.core.spring;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.annotation.DecryptAnno;
import com.guozw.common.core.annotation.NotDecryptAnno;
import com.guozw.common.core.annotation.NotSignatureAnno;
import com.guozw.common.core.annotation.SignatureAnno;
import com.guozw.common.core.bean.ApiCryptoBody;
import com.guozw.common.core.bean.AppInfo;
import com.guozw.common.core.bean.InputMessage;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.core.exception.CustomException;
import com.guozw.common.core.util.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.DigestUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * 参数验签、参数解密
 */
@RestControllerAdvice
@Slf4j
public class BaseRequestBodyAdvice implements RequestBodyAdvice {

    private Class<SignatureAnno> signatureAnnoClass = SignatureAnno.class;
    private Class<NotSignatureAnno> notSignatureAnnoClass = NotSignatureAnno.class;
    private Class<DecryptAnno> decryptAnnoClass = DecryptAnno.class;
    private Class<NotDecryptAnno> notDecryptAnnoClass = NotDecryptAnno.class;

    /**
     * 超时时间（单位：秒），数字越小安全性越高，但要注意容错
     */
    private static final long timeout = 300000;
    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {

        boolean hasNotSignatureAnno = methodParameter.hasMethodAnnotation(notSignatureAnnoClass) || methodParameter.getDeclaringClass().getAnnotation(notSignatureAnnoClass) != null;
        boolean hasSignatureAnno = methodParameter.hasMethodAnnotation(signatureAnnoClass) || methodParameter.getDeclaringClass().getAnnotation(signatureAnnoClass) != null;
        return !hasNotSignatureAnno && hasSignatureAnno;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        // 请求数据
        String requestData = StrUtil.EMPTY;
        // 请求体
        String requestBody = StreamUtils.copyToString(httpInputMessage.getBody(), CharsetUtil.CHARSET_UTF_8);
        ApiCryptoBody apiCryptoBody = JSONUtil.toBean(requestBody, ApiCryptoBody.class);
        // 参与签名的秘钥
        String appSecret = StrUtil.EMPTY;
        if (StrUtil.isBlank(apiCryptoBody.getAppId())) {
            ErrorCodeEnum paramAppIdMissing = ErrorCodeEnum.PARAM_APPID_MISSING;
            log.error(paramAppIdMissing.getMessage());
            throw new CustomException(paramAppIdMissing);
        } else {
            for (AppInfo item : CommonConstant.apiAppList) {
                if (item.getAppId().equals(apiCryptoBody.getAppId())) {
                    appSecret = item.getAppSecret();
                    // 使用阿里TTL线程间传递appSecret参数，给响应加密
                    TenantContextHolder.setTenantId(item.getAppSecret());
                    break;
                }
            }
            if (StrUtil.isBlank(appSecret)) {
                ErrorCodeEnum invalidAppId = ErrorCodeEnum.INVALID_APPID;
                log.error(invalidAppId.getMessage());
                throw new CustomException(invalidAppId);
            }
        }
        if (StrUtil.isBlank(apiCryptoBody.getData())) {
            ErrorCodeEnum paramDataMissing = ErrorCodeEnum.PARAM_DATA_MISSING;
            log.error(paramDataMissing.getMessage());
            throw new CustomException(paramDataMissing);
        } else {
            requestData = apiCryptoBody.getData();
        }
        if (StrUtil.isBlank(apiCryptoBody.getNonce())) {
            ErrorCodeEnum paramNonceMissing = ErrorCodeEnum.PARAM_NONCE_MISSING;
            log.error(paramNonceMissing.getMessage());
            throw new CustomException(paramNonceMissing);
        }
        if (ObjectUtil.isNull(apiCryptoBody.getTimestamp())) {
            ErrorCodeEnum paramTimestampMissing = ErrorCodeEnum.PARAM_TIMESTAMP_MISSING;
            log.error(paramTimestampMissing.getMessage());
            throw new CustomException(paramTimestampMissing);
        }
        if (StrUtil.isBlank(apiCryptoBody.getSign())) {
            ErrorCodeEnum paramSignMissing = ErrorCodeEnum.PARAM_SIGN_MISSING;
            log.error(paramSignMissing.getMessage());
            throw new CustomException(paramSignMissing);
        }
        if (timeout > 0) {
            long time = DateUtil.currentSeconds() - apiCryptoBody.getTimestamp();
            if (time > timeout) {
                ErrorCodeEnum signatureTimedOut = ErrorCodeEnum.SIGNATURE_TIME_OUT;
                log.error(signatureTimedOut.getMessage());
                throw new CustomException(signatureTimedOut);
            }
        }
        if (!signature(apiCryptoBody, appSecret).equals(apiCryptoBody.getSign())) {
            ErrorCodeEnum verifySignatureFailed = ErrorCodeEnum.VERIFY_SIGNATURE_FAILED;
            log.error(verifySignatureFailed.getMessage());
            throw new CustomException(verifySignatureFailed);
        }

        NotDecryptAnno notDecryptAnno = methodParameter.getMethodAnnotation(notDecryptAnnoClass);
        if (ObjectUtil.isNull(notDecryptAnno)) {
            notDecryptAnno = methodParameter.getDeclaringClass().getAnnotation(notDecryptAnnoClass);
        }

        DecryptAnno decryptAnno = methodParameter.getMethodAnnotation(decryptAnnoClass);
        if (ObjectUtil.isNull(decryptAnno)) {
            decryptAnno = methodParameter.getDeclaringClass().getAnnotation(decryptAnnoClass);
        }
        // 请求参数解密
        if (ObjectUtil.isNull(notDecryptAnno) && ObjectUtil.isNotNull(decryptAnno) && StrUtil.isNotBlank(requestData)) {
            requestData = SecureUtil.aes(StrUtil.bytes(appSecret, CharsetUtil.UTF_8)).decryptStr(requestData);
        }

        return InputMessage.of(StrUtil.bytes(requestData, CharsetUtil.UTF_8), httpInputMessage.getHeaders());
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

    private String signature(ApiCryptoBody apiCryptoBody, String appSecret) {
        try {
            String str = "data=" + apiCryptoBody.getData() +
                    "&timestamp=" + apiCryptoBody.getTimestamp() +
                    "&nonce=" + apiCryptoBody.getNonce() +
                    "&appSecret=" + appSecret;
            return DigestUtils.md5DigestAsHex(str.getBytes(CharsetUtil.CHARSET_UTF_8));
        } catch (Exception e) {
            ErrorCodeEnum signatureFailed = ErrorCodeEnum.SIGNATURE_FAILED;
            log.error("【{}】【{}】", signatureFailed.getMessage(), e.getMessage());
            throw new CustomException(signatureFailed);
        }
    }

}
