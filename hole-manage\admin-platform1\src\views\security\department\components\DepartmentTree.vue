<template>
    <div id="topDiv">
        <div class="toolDiv">
            <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText"
                clearable
            ></el-input>
            <vxe-toolbar ref="toolbar" v-if="showActionButton">
                <template v-slot:buttons>
                  <div >
                    <el-button
                      type="primary"
                      icon="fa fa-plus"
                      @click="handleAdd"
                      v-btnpermission="'asset:group:add'"
                      v-if="showAddButton"
                    ></el-button>
                    <el-button
                      type="primary"
                      icon="fa fa-edit"
                      @click="handleModify"
                      v-btnpermission="'asset:group:modify'"
                      v-if="showEditButton"
                    ></el-button>
                    <el-button
                      type="primary"
                      icon="fa fa-trash"
                      @click="handleDel"
                      v-btnpermission="'asset:group:del'"
                      v-if="showDelButton"
                    ></el-button>
                    <el-button
                      type="primary"
                      v-btnpermission="'deparment:choose'"
                      @click="selectAll()"
                      v-if="!isCheckOnlyOne && showCheckbox"
                    >全选/全不选</el-button>
                  </div>


                </template>

            </vxe-toolbar>
        </div>
        <div class="treeDiv" v-bind:style="{ height: treeHeight + 'px' }">
            <el-tree
                v-loading="treeLoading"
                element-loading-text="拼命加载中"
                ref="departmentTree"
                class="filter-tree"
                :node-key="nodeKey"
                :data="departmentTreeNodes"
                :default-checked-keys="defaultCheckedKeys"
                :props="defaultProps"
                default-expand-all
                check-on-click-node
                highlight-current
                :check-strictly="checkStrictly"
                :show-checkbox="showCheckbox"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                @check-change="handleCheckChange"
            >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                    <span>{{ node.label }}</span>
                    <span v-if="showAssetIpCount"
                        >({{ data.assetIpCount }})</span
                    >
                    <span v-if="showAssetWebsiteCount"
                        >({{ data.assetWebsiteCount }})</span
                    >
                  <span v-if="showHoleBasicIpCount"
                        >({{ data.holeIpCount }})</span
                    >
                  <span v-if="showHoleBasicWebsiteCount"
                        >({{ data.holeWebsiteCount }})</span
                    >

                </span>
            </el-tree>
        </div>
        <DepartmentEdit
            :modalInfo="editModalInfo"
            :formDefaultData="formDefaultData"
            :departmentTreeNodes="departmentTreeNodes"
            :assetType="assetType"
            @refreshDepartmentTree="refreshDepartmentTree"
            v-on="$listeners"
        ></DepartmentEdit>
    </div>
</template>

<script>
import { listDepartments, deleteDepartment } from "@/api/security/department";
import { recurseAssetIpCount, recurseAssetWebsiteCount,recurseHoleWebsiteCount,recurseholeIpCount } from "@/api/holemanage/asset";
import { listToTreeList } from "@/utils/guozw-core";
import DepartmentEdit from "@/views/security/department/components/DepartmentEdit";
export default {
  name: "DepartmentTree",
  components: {DepartmentEdit},
  data() {
    return {
      name: "DepartmentTree",
      filterText: "",
      treeLoading: false,
      departmentTreeConfig: {
        id: "departmentcode",
        parentId: "departmentparentcode",
        children: "children"
      },
      departmentTreeNodes: [],
      // 选中的树节点
      selectedTreeNode: {},
      // 资产状态 默认在线状态
      assetStatus: "1",
      defaultProps: {
        children: "children",
        label: "departmentname"
      },
      formDefaultData: {},
      editModalInfo: {},
      departmentList:[],
      checked1: true
    };
  },
  props: {
    // 是否显示资产数量
    showAssetIpCount: {type: Boolean, default: false},
    showAssetWebsiteCount: {type: Boolean, default: false},
    showHoleBasicIpCount: {type: Boolean, default: false},
    showHoleBasicWebsiteCount: {type: Boolean, default: false},
    // 是否显示操作按钮
    showActionButton: {type: Boolean, default: true},
    showCheckbox: {type: Boolean, default: false},
    checkStrictly: {type: Boolean, default: true},
    nodeKey: {type: String, default: "departmentcode"},
    defaultCheckedKeys: Array,
    // 是否只能选中一个
    isCheckOnlyOne: {type: Boolean, default: true},
    showDelButton: {type: Boolean, default: true},
    showEditButton: {type: Boolean, default: true},
    showAddButton: {type: Boolean, default: true},
    treeHeight: {type: Number, default: 400},
    // 资产类型
    assetType: {
      type: Number,
      default: 1
    }
  },

  watch: {
    filterText(val) {
      this.$refs.departmentTree.filter(val);
    }
  },

  created() {
    this.loadDepartmentTree();
  },
  mounted() {
  },
  methods: {
    getElTree() {
      return this.$refs.departmentTree;
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.departmentname.indexOf(value) !== -1;
    },

    // 处理节点点击
    handleNodeClick(data, node) {
      this.selectedTreeNode = node.data;
      console.log("选中的节点", this.selectedTreeNode);
      this.$emit("department-tree-node-click", node.data);
    },
    // 处理节点选中状态改变
    handleCheckChange(data, checked, node) {
      if (this.isCheckOnlyOne) {
        if (checked) {
          this.$refs.departmentTree.setCheckedNodes([data]);
        }
      }
    },
    // 加载左侧部门树
    loadDepartmentTree(departmentcode) {
      this.treeLoading = true
      listDepartments({
        isenabled: 1,
        assetStatus: this.assetStatus,
        showHoleBasicIpCount: this.showHoleBasicIpCount,
        showHoleBasicWebsiteCount: this.showHoleBasicWebsiteCount
      }).then(response => {
        const {data} = response
        const treeList = listToTreeList(
          JSON.parse(JSON.stringify(data)),
          this.departmentTreeConfig.id,
          this.departmentTreeConfig.parentId
        );
        this.departmentList = response.data;
        this.departmentTreeNodes = treeList;
        console.log('treeList',treeList);
        // 如果有传递部门编码，则默认选中
        if (departmentcode) {
          this.$nextTick(() => {
            this.$refs.departmentTree.setCurrentKey(departmentcode);
            this.selectedTreeNode = this.$refs.departmentTree.getCurrentNode();
          });
        }
        return data
      }).then(res => {
        // 加载IP资产数量
        this.loadAssetIpCount(res)
        // 加载网站资产数量
        this.loadAssetWebsiteCount(res)
        this.loadHoleWebsiteCount(res)
        this.loadHoleIpCount(res)
      }).finally(() => {
        this.treeLoading = false
      });
    },
    // 新增部门之后，刷新部门树，并且节点定位到刚刚新增的部门
    refreshDepartmentTree(departmentcode, assetStatus) {
      console.log("子组件传来的消息", departmentcode);
      this.assetStatus = assetStatus;
      this.loadDepartmentTree(departmentcode);
    },
    // 刷新指定部门下的在线资产数量
    refreshAssetCount(assetStatus, departmentcode) {
      console.log("refreshAssetCount", assetStatus, departmentcode);
      this.assetStatus = assetStatus;
      this.loadDepartmentTree(departmentcode);
    },
    handleAdd() {
      this.formDefaultData = {
        departmentparentcode: this.selectedTreeNode.departmentcode,
        departmentparentname: this.selectedTreeNode.departmentname
      };
      this.editModalInfo = {
        title: "新增分组",
        show: true
      };
    },
    handleModify() {
      if (this.selectedTreeNode && this.selectedTreeNode.departmentname) {
        this.formDefaultData = this.selectedTreeNode;
        this.editModalInfo = {
          show: true,
          title: "修改分组"
        };
      } else {
        this.$XModal.message({
          message: "请选择要修改的分组",
          status: "warning"
        });
      }
    },
    handleDel() {
      console.log("选中的节点", this.selectedTreeNode);
      if (this.selectedTreeNode && this.selectedTreeNode.departmentname) {
        if (
          this.selectedTreeNode.departmentparentcode ==
          this.$guozwSettings.rootId
        ) {
          this.$XModal.message({
            message: "顶级节点不能删除",
            status: "warning"
          });
          return;
        }
        this.$XModal
          .confirm({
            message:
              "此操作将会删除分组以及所有子分组和分组下的所有资产，确定要删除吗？",
            position: "center",
            status: "warning"
          })
          .then(type => {
            if (type === "confirm") {
              deleteDepartment({
                departmentcode: this.selectedTreeNode
                  .departmentcode
              }).then(response => {
                this.$XModal.message({
                  message: "删除成功",
                  status: "success"
                });
                setTimeout(this.loadDepartmentTree(), 500);
              });
            }
          });
      } else {
        this.$XModal.message({
          message: "请选择要删除的分组",
          status: "warning"
        });
      }
    },
    selectAll() {
      console.log('departmentList',this.departmentList);
      // 判断按钮的状态
      if (this.checked1) {
        // 设置
        this.$refs.departmentTree.setCheckedNodes(this.departmentList);
        this.checked1 = false;
      } else {
        this.$refs.departmentTree.setCheckedNodes([]);
        this.checked1 = true
      }
    },
    // 加载各部门IP资产数量
    loadAssetIpCount(deptList) {
      if (this.showAssetIpCount) {
        if (deptList && deptList.length > 0) {
          deptList.forEach(item => {
            recurseAssetIpCount({departmentcode: item.departmentcode, assetStatus: this.assetStatus}).then(res => {
              let node = this.$refs.departmentTree.getNode(item.departmentcode);
              if (node) {
                // 更新资产数量
                this.$set(node.data, 'assetIpCount', res.data)
              }
            })
          })
        }
      }
    },

    // 加载各部门望着你资产数量
    loadAssetWebsiteCount(deptList) {
      if (this.showAssetWebsiteCount) {
        if (deptList && deptList.length > 0) {
          deptList.forEach(item => {
            recurseAssetWebsiteCount({departmentcode: item.departmentcode, assetStatus: this.assetStatus}).then(res => {
              let node = this.$refs.departmentTree.getNode(item.departmentcode);
              if (node) {
                // 更新资产数量
                this.$set(node.data, 'assetWebsiteCount', res.data)
              }
            })
          })
        }
      }
    },
    // 加载各部门望着你资产数量
    loadHoleWebsiteCount(deptList) {
      if (this.showHoleBasicWebsiteCount) {
        if (deptList && deptList.length > 0) {
          deptList.forEach(item => {
            recurseHoleWebsiteCount(item.departmentcode).then(res => {
              let node = this.$refs.departmentTree.getNode(item.departmentcode);
              if (node) {
                // 更新资产数量
                this.$set(node.data, 'holeWebsiteCount', res.data)
              }
            })
          })
        }
      }
    },
    // 加载各部门望着你资产数量
    loadHoleIpCount(deptList) {
      if (this.showHoleBasicIpCount) {
        if (deptList && deptList.length > 0) {
          deptList.forEach(item => {
            recurseholeIpCount(item.departmentcode).then(res => {
              let node = this.$refs.departmentTree.getNode(item.departmentcode);
              if (node) {
                // 更新资产数量
                this.$set(node.data, 'holeIpCount', res.data)
              }
            })
          })
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>
#topDiv {
    /deep/ .filter-tree {
        .custom-tree-node {
            font-size: 12px;
        }
    }
	.treeDiv {
		overflow: hidden;
		&:hover {
			overflow: auto
		}
	}
}
</style>
