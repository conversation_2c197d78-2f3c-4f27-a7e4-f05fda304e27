<template>
  <el-container>
    <el-aside width="200px">
      <department-tree
        ref="departmentTree"
        :show-hole-basic-ip-count="true"
        :tree-height="leftDeptTreeHeight"
        :show-action-button="false"
        @department-tree-node-click="handleDepartmentTreeNodeClick"
      />
    </el-aside>
    <el-main>
      <el-tabs
        ref="tabs"
        v-model="activeName"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="需关注" name="0" />
        <el-tab-pane label="已关闭" name="1" />
        <el-tab-pane label="白名单" name="2" />
        <el-tab-pane label="误报" name="3" />
        <el-tab-pane label="全部" name="4" />
      </el-tabs>
      <!-- region 查询条件表单-->
      <vxe-form
        ref="queryConditionForm"
        title-width="100"
        title-align="right"
        span="8"
        :data="queryParams"
        @submit="handleQueryConditionFormSubmit"
        @toggle-collapse="handleQueryConditionFormToggleCollapse"
      >
        <vxe-form-item field="hole_name" title="漏洞名称">
          <vxe-input
            v-model="queryParams.hole_name"
            clearable
            placeholder="漏洞名称"
          />
        </vxe-form-item>
        <vxe-form-item
          field="hole_cve"
          title="CVE编号"
        >
          <vxe-input
            v-model="queryParams.hole_cve"
            clearable
            placeholder="CVE编号"
          />
        </vxe-form-item>
        <vxe-form-item align="left" collapse-node>
          <vxe-button
            type="submit"
            status="primary"
            icon="fa fa-search"
          >查询
          </vxe-button>
          <vxe-button
            type="reset"
            icon="fa fa-refresh"
          >重置
          </vxe-button>
        </vxe-form-item>
        <vxe-form-item field="hole_risk" title="风险值" folding>
          <vxe-input
            v-model="queryParams.hole_risk_gt"
            type="number"
            style="width: 30%"
            :min="1"
            :max="10"
          />
          至
          <vxe-input
            v-model="queryParams.hole_risk_lt"
            type="number"
            style="width: 30%"
            :min="1"
            :max="10"
          />

        </vxe-form-item>

        <vxe-form-item field="hole_type" title="漏洞类型" folding>
          <el-select
            v-model="queryParams.hole_type"
            clearable
            filterable
            placeholder="漏洞类型"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in holeTypeEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_discovery_tool" title="探针品牌" folding>
          <el-select
            v-model="queryParams.hole_discovery_tool"
            clearable
            filterable
            placeholder="探针品牌"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in toolBasicEnum"
              :key="item.tool_basic_id"
              :label="item.tool_name"
              :value="item.tool_basic_id"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_discovery_status" title="发现状态" folding>
          <el-select
            v-model="queryParams.hole_discovery_status"
            clearable
            filterable
            placeholder="发现状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in discoveryStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_test_status" title="验证状态" folding>
          <el-select
            v-model="queryParams.hole_test_status"
            clearable
            filterable
            placeholder="验证状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in testStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_repair_status" title="修复状态" folding>
          <el-select
            v-model="queryParams.hole_repair_status"
            clearable
            filterable
            placeholder="修复状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in repairStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_retest_status" title="复测状态" folding>
          <el-select
            v-model="queryParams.hole_retest_status"
            clearable
            filterable
            placeholder="复测状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in retestStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_close_status" title="关闭状态" folding>
          <el-select
            v-model="queryParams.hole_close_status"
            clearable
            filterable
            placeholder="关闭状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in closeStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_white_status" title="白名单" folding>
          <el-select
            v-model="queryParams.hole_white_status"
            clearable
            filterable
            placeholder="是否白名单"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in enumCommon"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_false_status" title="误报状态" folding>
          <el-select
            v-model="queryParams.hole_false_status"
            clearable
            filterable
            placeholder="是否误报状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in enumCommon"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_discovery_method" title="发现方式" folding>
          <el-select
            v-model="queryParams.hole_discovery_method"
            clearable
            filterable
            placeholder="发现方式"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in discoveryMethodEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="hole_level" title="漏洞级别" folding>
          <el-select
            v-model="queryParams.hole_level"
            clearable
            filterable
            placeholder="漏洞级别"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in holeLevelEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="asset_ip" title="IP地址" folding>
          <vxe-input
            v-model="queryParams.asset_ip"
            clearable
            placeholder="IP地址"
          />
        </vxe-form-item>
        <vxe-form-item field="contactPhone" title="资产联系人" folding>
          <vxe-input
            v-model="queryParams.contactPhone"
            clearable
            placeholder="资产联系人"
          />
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>

          <el-button
            type="primary"
            icon="el-icon-message"
            @click="sendEmailShow = true"
          >
            邮件通知
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-printer"
            @click="exportExcel"
          >
            导出
          </el-button>
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div :style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="myTable"
          ref="myTable"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          auto-resize
          resizable
          height="auto"
          show-overflow
          :sort-config="{ trigger: 'cell', remote: true }"
          :custom-config="{ storage: true }"
          :data="page.records"
          :checkbox-config="{ trigger: 'row' }"
          @sort-change="sortChange"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
          />
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          />
          <vxe-table-column
            field="asset_ip"
            title="IP地址"
            width="150"
          >
            <template v-slot="{ row }">
              <el-link type="primary" @click="goIpDetail(row)">{{ row.asset_ip }}</el-link>
            </template>
          </vxe-table-column>
          <vxe-table-column field="departmentname" title="隶属关系" width="150" />
          <vxe-table-column
            field="device_type"
            title="设备类型"
            width="150"
            :formatter="['formatSelect', deviceTypeEnum]"
          />
          <vxe-table-column field="needNotice" min-width="110" title="需关注漏洞总数" />
          <vxe-table-column field="hole_level4" min-width="100" title="超危漏洞数" />
          <vxe-table-column field="hole_level3" min-width="100" title="高危漏洞数" />
          <vxe-table-column field="hole_level2" min-width="100" title="中危漏洞数" />
          <vxe-table-column field="hole_level1" min-width="100" title="低危漏洞数" />

        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager
        ref="pager"
        :current-page="queryParams.pagenumber"
        :page-size="queryParams.pagesize"
        :total="page.total"
        @page-change="handlePageChange"
      />
      <!-- endregion -->

      <vxe-modal
        v-model="sendEmailShow"
        height="99%"
        width="30%"
        position="centers"
        resize
        title="发送邮件"
        show-footer
      >
        <send-email-form
          ref="sendEmailForm"
          :show="true"
          :user="userList"
          :email-template="emailTemplate"
        />
        <template v-slot:footer>
          <el-button type="" @click="closeEmailModal">取消</el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="submitEmailForm"
          >确定
          </el-button>
        </template>
      </vxe-modal>
    </el-main>
  </el-container>
</template>

<script>
import { pageHoleIpAgg, saveAndSendEmail } from '@/api/holemanage/asset'
import { listUser, pageUsers } from '@/api/security/user'
import { listDictionarys } from '@/api/security/dictionary'
import sendEmailForm from '@/views/holemanage/hole/ip/components/sendEmailForm'
import departmentTree from '@/views/security/department/components/DepartmentTree'
import { mapGetters } from 'vuex'
import { listConfigEmailTemplate } from '@/api/config/email'
import { listToolBasic } from '@/api/testManage'
import { getFile } from '@/utils/file'

export default {
  name: 'HolemanageHoleIpIndex',
  components: { sendEmailForm, departmentTree },
  data() {
    return {
      activeName: '0',
      tabsHeight: 54,
      queryConditionFormHeight: 0, // 查询条件表单高度
      tableHeight: 0, // 表格高度
      loading: true,
      page: {
        total: 0,
        records: []
      },
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        hole_name: '',
        hole_type: '',
        needNotice: 1,
        hole_discovery_tool: '',
        hole_discovery_status: '',
        hole_test_status: '',
        hole_repair_status: '',
        hole_retest_status: '',
        hole_close_status: '',
        hole_white_status: '',
        hole_false_status: '',
        hole_discovery_method: '',
        hole_level: '',
        asset_ip: '',
        contactPhone: ''

      },
      leftDeptTreeHeight: 400,
      // 漏洞类型
      holeTypeEnum: [],
      toolBasicEnum: [],
      // 发现状态
      discoveryStatusEnum: [],
      // 验证状态
      testStatusEnum: [],
      // 修复状态
      repairStatusEnum: [],
      // 复测状态
      retestStatusEnum: [],
      // 关闭状态
      closeStatusEnum: [],
      // 发现方式
      discoveryMethodEnum: [],
      // 是否
      enumCommon: [],
      // 漏洞级别
      holeLevelEnum: [],
      // 漏洞级别
      deviceTypeEnum: [],

      sendEmailShow: false,
      userList: [],
      emailTemplate: []

    }
  },
  created() {
    this.initData()
    this.loadTable()
  },

  mounted() {
    this.setQueryConditionFormHeight()
    this.setTableHeight()
    this.windowResize()
  },
  methods: {
    initData() {
      // 加载字典
      listDictionarys().then(res => {
        res.data.forEach(item => {
          const dict = {
            label: item.dictionaryname,
            value: item.dictionaryvalue
          }
          // 漏洞类型
          if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeType
              .dictionarytypecode
          ) {
            this.holeTypeEnum.push(dict)
          }
          // 发现状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeDiscoveryStatus
              .dictionarytypecode
          ) {
            this.discoveryStatusEnum.push(dict)
          }
          // 漏洞级别
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeLevel
              .dictionarytypecode
          ) {
            this.holeLevelEnum.push(dict)
          }
          // 验证状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeTestStatus
              .dictionarytypecode
          ) {
            this.testStatusEnum.push(dict)
          }
          // 修复
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRepairStatus
              .dictionarytypecode
          ) {
            this.repairStatusEnum.push(dict)
          }
          // 复测
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRetestStatus
              .dictionarytypecode
          ) {
            this.retestStatusEnum.push(dict)
          }
          // 关闭
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeCloseStatus
              .dictionarytypecode
          ) {
            this.closeStatusEnum.push(dict)
          }
          // 关闭
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeDiscoveryMethod
              .dictionarytypecode
          ) {
            this.discoveryMethodEnum.push(dict)
          }
          // 设备类型
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.deviceType
              .dictionarytypecode
          ) {
            this.deviceTypeEnum.push(dict)
          }
        })
      })
      this.enumCommon = this.$guozwSettings.enumCommon
      listConfigEmailTemplate({}).then(res => {
        this.emailTemplate = res.data
      })

      pageUsers({
        pagenumber: 1,
        pagesize: 1000
      }).then(res => {
        this.userList = res.data.records
      })
      listToolBasic().then(res => {
        this.toolBasicEnum = res.data
      })
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      const activeNmae = this.activeName
      // 需关注
      if (activeNmae === '0') {
        this.queryParams.needNotice = 1
        this.queryParams.hole_close_status = ''
        this.queryParams.hole_white_status = ''
        this.queryParams.hole_false_status = ''
      }
      // 已关闭
      else if (activeNmae === '1') {
        this.queryParams.needNotice = ''
        this.queryParams.hole_close_status = '1'
        this.queryParams.hole_white_status = ''
        this.queryParams.hole_false_status = ''
      }
      // 白名单
      else if (activeNmae === '2') {
        this.queryParams.needNotice = ''
        this.queryParams.hole_close_status = ''
        this.queryParams.hole_white_status = '1'
        this.queryParams.hole_false_status = ''
      }
      // 误报
      else if (activeNmae === '3') {
        this.queryParams.needNotice = ''
        this.queryParams.hole_close_status = ''
        this.queryParams.hole_white_status = ''
        this.queryParams.hole_false_status = '1'
      } else {
        this.queryParams.needNotice = ''
        this.queryParams.hole_close_status = ''
        this.queryParams.hole_white_status = ''
        this.queryParams.hole_false_status = ''
      }
      this.loadTable()
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight
      this.leftDeptTreeHeight = window.innerHeight - this.extraHeight - 35
      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this
      window.onresize = () => {
        return (() => {
          that.setTableHeight()
          console.log(
            // "窗口resize-----------------" + that.tableHeight
          )
        })()
      }
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function() {
        this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight
      })
    },
    exportExcel() {
      const queryCondition = this.getQueryCondition()
      queryCondition.type = 'holeBasicIpAgg'
      console.log(queryCondition)
      getFile(null, queryCondition, 'ip漏洞')
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1
      this.loadTable()
    },

    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage
      this.queryParams.pagesize = pageSize
      this.loadTable()
    },

    /* 字段排序 */
    sortChange(e) {
      console.log('sortChange', e)
      const { field, order } = e

      this.queryParams.sort_field = field
      this.queryParams.sort_order = order

      this.loadTable()
    },

    // 查询条件
    getQueryCondition() {
      const quryCondition = JSON.parse(JSON.stringify(this.queryParams))

      return quryCondition
    },
    // 加载表格
    loadTable() {
      this.loading = true
      pageHoleIpAgg(this.getQueryCondition())
        .then(response => {
          this.page.records = response.data.records
          this.page.total = response.data.total
          this.loading = false
          console.log('deviceTypeEnum', this.deviceTypeEnum)
        })
        .catch(error => {
          this.loading = false
        })
    },

    // 处理左侧部门树节点点击
    handleDepartmentTreeNodeClick(node) {
      this.queryParams.asset_department_id = node.departmentcode
      this.loadTable()
    },
    closeEmailModal() {
      this.sendEmailShow = false
      this.$refs.sendEmailForm.clearValidate()
    },
    submitEmailForm() {
      this.$refs.sendEmailForm.getFormDate().then(res => {
        console.log('res', res)
        saveAndSendEmail(res).then(res => {
          console.log('senEmail', res)
          this.$XModal.message({
            message: '发送成功',
            status: 'success'
          })
          this.$refs.sendEmailForm.clearValidate()
          this.sendEmailShow = false
        }).catch(err => {
          console.log(err)
        })
      })
    },
    // 调到ipdetail页
    goIpDetail(item) {
      this.$router.push({ path: '/holemanage/hole/ip/ipDetail?a=aaa,111,222', query: { asset_ip_id: item.asset_ip_id }})
    }

  },
  computed: {
    ...mapGetters(['extraHeight'])
  },
  watch: {
    extraHeight() {
      this.setTableHeight()
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      console.log('监听查询条件表单高度变化--' + val)
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val
    }
  }
}
</script>
<style scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
