package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 漏洞处置反馈
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "hole_plan_back")
public class HolePlanBack extends BaseEntity<HolePlanBack> implements Serializable {

    private static final long serialVersionUID = -6996535100699886102L;
    @TableId
    private String hole_plan_back_id;
    /**
     * 漏洞所属资产类型  1-IP资产  2-网站资产
     */
    private String asset_type;
    /**
     * 处置-反馈类型
     */
    private String plan_back_type;
    /**
     * 处置反馈状态，包括处置修复、验证、复测，反馈修复、验证、复测对应的状态
     */
    private String plan_back_status;
    /**
     * 处置要求完成时间/反馈完成时间/关闭时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date test_date;
    /**
     * 特别说明
     */
    private String test_remark;
    /**
     * 附件ID
     */
    private String file_id;
    /**
     * 附件类型
     */
    private String file_type;
    /**
     * 漏洞ID，英文逗号隔开
     */
    private String hole_basic_ip_id;
    /**
     * 资产IP，英文逗号隔开
     */
    private String asset_ip;
    /**
     * 处置下发人/反馈人，存用户ID
     */
    private String back_test_user;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
