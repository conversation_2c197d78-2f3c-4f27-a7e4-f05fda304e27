package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 漏扫工具基础信息表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "tool_basic")
public class ToolBasic extends BaseEntity<ToolBasic> implements Serializable {

    private static final long serialVersionUID = 4972049943748391039L;

    /**
     * 主键
     */
    @TableId
    private String tool_basic_id;
    /**
     * 漏扫工具品牌
     */
    private String tool_make;
    /**
     * 漏扫工具名称
     */
    private String tool_name;
    /**
     * 协议
     */
    private String tool_protocol;
    /**
     * 地址
     */
    private String tool_address;
    /**
     * 端口
     */
    private String tool_port;
    /**
     * 账号
     */
    private String tool_account_num;
    /**
     * 密码
     */
    private String tool_password;
    /**
     * apikey
     */
    private String tool_apikey;
    /**
     * 应用地址
     */
    private String tool_weburl;
    /**
     * 单个主机任务最大目标数量
     */
    private int tool_target_num;
    /**
     * 扫面工具并发任务数量
     */
    private int tool_concurrent_num;
    /**
     * 备注
     */
    private String tool_remark;
    /**
     * 是否自动创建仓库 0-否 1-是
     */
    private String tool_iswarehouse;

    /**
     * 运行的任务数
     */
    private int tool_run_count;
    /**
     * 等待的任务数
     */
    private int tool_wait_count;
    /**
     * 最大任务数
     */
    private int tool_max_task;
    /**
     * 已存在任务数
     */
    private int tool_all_count;

    /**
     * 共享部门
     */
    private String tool_share_department;

    private String asset_scan_flag;

    private String hole_scan_flag;

    private String password_scan_flag;



    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
