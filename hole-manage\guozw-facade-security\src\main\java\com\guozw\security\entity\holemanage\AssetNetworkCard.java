package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资产管理-网卡信息
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "asset_network_card")
public class AssetNetworkCard extends BaseEntity<AssetNetworkCard> implements Serializable {

    private static final long serialVersionUID = 1367941743012910199L;
    @TableId
    private String network_card_id;
    private String relation_type;
    private String relation_id;
    private String network_card_ipv4;
    private String network_card_ipv6;
    private String network_card_gateway;
    private String network_card_name;
    private String network_card_mac;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
