<template>
    <div id="topDiv">
        <vxe-modal
            height="99%"
            width="60%"
            position="center"
            resize
            :title="modalInfo.title"
            v-model="modalInfo.show"
            @close="handleModalClose()"
            showFooter
        >
            <vxe-form
                ref="myForm"
                title-width="120"
                :data="modalForm"
                :rules="rules"
                title-align="right"
                prevent-submit
                span="24"
            >
                
                <vxe-form-item title="资产分组" field="departmentname">
                    <template v-slot="scope">
                        <vxe-pulldown ref="xDown1" transfer style="width: 100%" >
                            <template v-slot>
                                <vxe-input
                                    v-model.trim="modalForm.departmentname"
                                    placeholder="资产分组"
                                    clearable
                                    readonly
                                    @input="$refs.myForm.updateStatus(scope)"
                                    @focus="focusEvent1"
                                ></vxe-input>
                            </template>
                            <template v-slot:dropdown>
                                <div class="my-dropdown1">
                                     <DepartmentTree
                                        ref="departmentTree"
                                        :showActionButton="false"
                                        @department-tree-node-click="handleNodeClick"
                                    ></DepartmentTree>
                                </div>
                            </template>
                        </vxe-pulldown>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="设备类型" field="device_type">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.device_type"
                            readonly
                            clearable
                            filterable
                            placeholder="设备类型"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in deviceTypeEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="等级保护" field="weight_protection">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_protection"
                            readonly
                            clearable
                            filterable
                            placeholder="等级保护"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in protectionEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="涉密状态" field="weight_secret">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_secret"
                            readonly
                            clearable
                            filterable
                            placeholder="涉密状态"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in secretEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="重要程度"
                    field="weight_importance"
                    class="lastItem"
                >
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_importance"
                            readonly
                            clearable
                            filterable
                            placeholder="重要程度"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in importanceEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>


                <div class="bigTitle">
                    <span>联系人信息</span>

                    <el-button
                        size="mini"
                        type="danger"
                        class="delRow"
                        @click="delChecked(0)"
                        >删除选中</el-button
                    >
                    <el-button
                        size="mini"
                        type="primary"
                        class="addRow"
                        @click="addRow(0)"
                        >新增一行</el-button
                    >
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-table
                    :ref="table_data_type[0].tableRef"
                    border
                    resizable
                    auto-resize
                    show-overflow
                    keep-source
                    row-key
                    max-height="400"
                    :data="modalForm.sysContactList"
                    :edit-rules="validRules"
                    :edit-config="{
                        trigger: 'click',
                        mode: 'row',
                        activeMethod: activeRowMethod,
                        showStatus: true
                    }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                    ></vxe-table-column>
                    <vxe-table-column type="seq" width="60"></vxe-table-column>
                    <vxe-table-column
                        field="contact_name"
                        title="姓名"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '姓名'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="contact_phone"
                        title="电话"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '电话'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="contact_email"
                        title="邮箱"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '邮箱'
                        }"
                    ></vxe-table-column>
                </vxe-table>

                
            </vxe-form>
            <template v-slot:footer>
                <el-button type="" @click="handleDialogCancel">取消</el-button>
                <el-button type="primary" :loading="loading" @click="submitForm"
                    >确定</el-button
                >
            </template>
        </vxe-modal>
    </div>
</template>
<script>
import { listToTreeList } from "@/utils/guozw-core";
import { listDepartments } from "@/api/security/department.js";
import { clearProperty, copyProperty } from "@/utils/guozw-core.js";
import { batchEditAssetWebsite } from "@/api/holemanage/asset";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
export default {
    name: "AssetWebsiteBatchEdit",
    components: { DepartmentTree},
    props: {
        // 设备类型
        deviceTypeEnum: {
            type: Array,
            default: () => []
        },
        // 等级保护
        protectionEnum: {
            type: Array,
            default: () => []
        },

        // 重要程度
        importanceEnum: {
            type: Array,
            default: () => []
        },
        // 涉密状态
        secretEnum: {
            type: Array,
            default: () => []
        },

        modalInfo: {
            type: Object,
            default: () => {}
        },
        formDefaultData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            // 选中的部门
            selectedDepartment: {},
            departmentTreeConfig: {
                id: "departmentcode",
                parentId: "departmentparentcode",
                children: "children"
            },
            table_data_type: [
                {
                    label: "联系人信息",
                    tableRef: "myTable_contact",
                    record: {
                        contact_name: "",
                        contact_phone: "",
                        contact_email: ""
                    },
                    activeCell: "contact_name"
                }
            ],

            modalForm: {
                // 要批量修改的数据主键列表
                assetWebsiteIdList: [],
                // 批量修改以此查询条件查询出的数据
                queryCondition: '',

                // 隶属部门id
                asset_department_id: "",
                // 隶属部门名称
                departmentname: "",
                device_type: "",

                weight_protection: "",
                weight_secret: "",
                weight_importance: "",

                // 联系信息列表
                sysContactList: [],
            },
            rules: {
                departmentname: [{ required: false, message: "必填字段" }],
                device_type: [{ required: false, message: "必填字段" }],
                weight_protection: [{ required: false, message: "必填字段" }],
                weight_secret: [{ required: false, message: "必填字段" }],
                weight_importance: [{ required: false, message: "必填字段" }],
            },
            /* 表格中的字段校验 */
            validRules: {
                contact_name: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 10, message: "长度最大为 10 字符" }
                ],
                contact_phone: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 11, message: "长度最大为 11 字符" }
                ],
                contact_email: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],

            }
        };
    },

    watch: {
        formDefaultData(newVal, oldVal) {
            console.log('newVal', newVal)
            copyProperty(newVal, this.modalForm);
        },
        filterText(val) {
            this.$refs.departmentTree.filter(val);
        }
    },

    computed: {},
    created() {
        this.initData();
        this.loadDepartmentTree();
    },
    methods: {
        handleModalClose() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        handleDialogCancel() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        loadDepartmentTree() {
            listDepartments({ isenabled: 1 }).then(response => {
                const treeList = listToTreeList(
                    response.data,
                    this.departmentTreeConfig.id,
                    this.departmentTreeConfig.parentId
                );

                this.departmentTreeNodes = treeList;
            });
        },
        initData() {
            
        },
        focusEvent1() {
            this.$refs.xDown1.showPanel();
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.departmentname.indexOf(value) !== -1;
        },
        // 处理节点点击
        handleNodeClick(node) {
            console.log("点击树节点", node);
            this.selectedDepartment = node;
            this.modalForm.departmentname = node.departmentname;
            this.modalForm.asset_department_id = node.departmentcode;
            this.$refs.myForm.clearValidate("departmentname");
            this.$refs.xDown1.hidePanel();
        },
        activeRowMethod({ row, column }) {
            console.log("activeRowMethod", row, column);
            return true;
        },
        async addRow(index) {
            let record = this.table_data_type[index].record;
            const tableRef = this.table_data_type[index].tableRef;
            const activeCell = this.table_data_type[index].activeCell;
            let { row: newRow } = await this.$refs[tableRef].insertAt(
                record,
                -1
            );
            await this.$refs[tableRef].setActiveCell(newRow, activeCell);
        },

        /* 删除选中的数据 */
        async delChecked(index) {
            const tableRef = this.table_data_type[index].tableRef;
            const checkedRecords = this.$refs[tableRef].getCheckboxRecords();
            if (checkedRecords && checkedRecords.length > 0) {
                this.$refs[tableRef].remove(checkedRecords);
                this.$XModal.message({
                    status: "success",
                    message: "删除成功"
                });
            } else {
                this.$XModal.message({
                    status: "warning",
                    message: "请至少选中一条记录"
                });
            }
        },

        getQueryCondition() {
            console.log('this.modalForm', this.modalForm)
            let queryCondition = JSON.parse(JSON.stringify(this.modalForm));
            console.log('getQueryCondition1', queryCondition)

            // 获取新增的记录
            let insertRecords_contact = this.$refs[
                this.table_data_type[0].tableRef
            ].getInsertRecords();
            console.log('insertRecords_contact', insertRecords_contact)

            if (insertRecords_contact && insertRecords_contact.length > 0) {
                insertRecords_contact.forEach(item => {
                    if (queryCondition.sysContactList) {
                        queryCondition.sysContactList.push(item);
                    } else {
                        queryCondition.sysContactList = []
                        queryCondition.sysContactList.push(item);
                    }
                });
            }
            queryCondition.queryCondition = JSON.stringify(queryCondition.queryCondition)

            console.log("getQueryCondition", JSON.stringify(queryCondition));
            return queryCondition;
        },

        // 提交表单
        submitForm() {
            this.$refs.myForm
                .validate()
                .then(() => {
                    this.loading = true;
                    const queryCondition = this.getQueryCondition();

                    batchEditAssetWebsite(queryCondition)
                        .then(response => {
                            this.$XModal.message({
                                message: "批量修改成功",
                                status: "success"
                            });
                            this.$emit("refreshTable");
                            this.modalInfo.show = false;
                        })
                        .finally(() => {
                            this.loading = false;
                            clearProperty(this.modalForm);
                            this.modalInfo.show = false;
                        });
                })
                .catch(err => {
                    console.log(err);
                    return false;
                });
        }
    }
};
</script>
<style lang="scss" scoped>
#topDiv {
    .vxe-form /deep/ .vxe-form--item-inner {
        min-height: 30px !important;
    }
    .bigTitle {
        margin-top: 10px;
        span {
            font-size: 15px;
            font-weight: bolder;
        }
    }

    .lastItem {
        margin-bottom: 10px !important;
    }
    .el-divider--horizontal {
        margin: 5px 0;
    }
    .tip {
        color: red;
        font-size: 10px;
    }

    .my-dropdown1 {
        height: 200px;
        overflow: auto;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
    }
    .list-item1:hover {
        background-color: #f5f7fa;
    }
    .addRow {
        float: right;
        position: relative;
        top: -5px;
    }
    .delRow {
        float: right;
        margin-left: 5px;
        position: relative;
        top: -5px;
    }
}
</style>
