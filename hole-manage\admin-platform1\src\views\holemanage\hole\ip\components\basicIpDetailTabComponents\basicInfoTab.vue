<template>
  <div style="height: auto;">
    <div class="leftBox">
            <span class="contentTitle">
                漏洞说明
            </span>
      <div class="contentBox">
        {{ holeBasicIpInfo.hole_remark }}
      </div>
      <span class="contentTitle">
                修复建议
            </span>
      <div class="contentBox">
        {{ holeBasicIpInfo.hole_repair }}
      </div>
      <span class="contentTitle">
                参考信息
            </span>
      <div class="contentBox">
        {{ holeBasicIpInfo.hole_referenceinfo }}

      </div>

    </div>
    <div class="raightBox">

             <span class="rightSpanLabel">
                    漏洞编号
                </span>
      <hr>
      <span v-if="holeBasicIpInfo.hole_cve">CVE:{{ holeBasicIpInfo.hole_cve }}</span>
      <br v-if="holeBasicIpInfo.hole_cve">
      <span v-if="holeBasicIpInfo.hole_vkb">VKB:{{ holeBasicIpInfo.hole_vkb }}</span>
      <br v-if="holeBasicIpInfo.hole_vkb">
      <span v-if="holeBasicIpInfo.hole_cwe">CWE:{{ holeBasicIpInfo.hole_cwe }}</span>
      <br v-if="holeBasicIpInfo.hole_cwe">
      <span v-if="holeBasicIpInfo.hole_cnnvd">CNNVD:{{ holeBasicIpInfo.hole_cnnvd }}</span>
      <br/>
      <br/>

      <span class="rightSpanLabel">
                    CVSS V2
                </span>
      <hr>
      <span class="rightSpanTitle">CVSS分值:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_cvss }}</span>
      <br/>
      <br/>
      <span class="rightSpanTitle">CVSS向量:</span>
      <span class="rightSpanValue"></span>
      <br/>
      <br/>
      <span class="rightSpanTitle">攻击途径:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_attack_route }}</span>
      <br/>
      <br/>
      <span class="rightSpanTitle">攻击复杂度:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_attack_diff }}</span>
      <br/>
      <br/>
      <span class="rightSpanTitle">权限要求:</span>
      <span class="rightSpanValue"></span>
      <br/>
      <br/>
      <span class="rightSpanTitle">机密性影响:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_privacy }}</span>
      <br/>
      <br/>
      <span class="rightSpanTitle">完整性影响:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_integrity }}</span>
      <br/>
      <br/>
      <span class="rightSpanTitle">可用性影响:</span>
      <span class="rightSpanValue">{{ holeBasicIpInfo.hole_usability }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "basicInfoTab",
  data() {
    return {}
  },
  props: {
    holeBasicIpInfo: {
      type: Object,
      require: true
    }
  }
}
</script>

<style scoped>
.leftBox {
  display: inline-block;
  float: left;
  width: 60%;
}

.raightBox {
  border-left: solid 0.2px #DCDFE6;
  display: inline-block;
  float: right;
  width: 35%;
  padding-left: 20px;
}

.contentBox {
  width: 100%;
  height: 200px;
  border: solid 0.2px #909399;
  /*margin-top: 10px;*/
  margin: 10px 0px;
  overflow-y: auto
}

.contentTitle, .rightSpanLabel {
  font-size: 20px;
  font-weight: bold;
}

.rightSpanTitle {
  font-size: 16px;
  font-weight: bold;
}
</style>
