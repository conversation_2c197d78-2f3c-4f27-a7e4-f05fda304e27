package com.guozw.common.core.crypto;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

@Slf4j
public class JasyptUtils {

    public static final String SALT = "salt";
    public static final String ALGORITHM = "PBEWithMD5AndDES";

    private static PooledPBEStringEncryptor encryptor = null;


    static {
        encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(SALT);
        config.setAlgorithm(ALGORITHM);
        config.setPoolSize("1");
        encryptor.setConfig(config);
    }
    public static String encypt(String value) {
       return encryptor.encrypt(value);
    }

    public static String decypt(String value) {
        return encryptor.decrypt(value);
    }

    public static void main(String[] args) {
        String result = decypt("pe7yFoOGWa/RG8r1Nu1oXZEnLG9AN8aA");
        String result2 = encypt("123456");
        String s = "";
    }
}
