package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;


/**
 * 漏洞基础信息表（网站资产）
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "hole_basic_website")
public class HoleBasicWebsite extends BaseEntity<HoleBasicWebsite> implements Serializable {

    private static final long serialVersionUID = -1663293977749976707L;

    @TableId
    private String hole_basic_website_id;
    /**
     * web资产表ID
     */
    private String asset_website_id;
    /**
     * 漏洞对应的网站
     */
    private String asset_website_name;
    /**
     * 漏洞对应的URL
     */
    private String hole_url;
    /**
     * 漏洞名称
     */
    private String hole_name;
    /**
     * 漏洞级别
     */
    private String hole_level;
    /**
     * 漏洞类型
     */
    private String hole_type;
    /**
     * cve
     */
    private String hole_cve;
    /**
     * vkb
     */
    private String hole_vkb;
    /**
     * cwe
     */
    private String hole_cwe;
    /**
     * cnnvd
     */
    private String hole_cnnvd;
    /**
     * 是否poc 0-否 1-是
     */
    private String ispoc;
    /**
     * 风险值，根据风险值计算公式计算得出
     */
    private String hole_risk;
    /**
     * cvss分支
     */
    private String hole_cvss;
    /**
     * 攻击途径
     */
    private String hole_attack_route;
    /**
     * 攻击复杂度
     */
    private String hole_attack_diff;
    /**
     * 攻击认证
     */
    private String hole_attack_auth;
    /**
     * 机密性影响
     */
    private String hole_privacy;
    /**
     * 完整性影响
     */
    private String hole_integrity;
    /**
     * 可用性影响
     */
    private String hole_usability;
    /**
     * request
     */
    private String hole_request;
    /**
     * response
     */
    private String hole_response;
    /**
     * 检测时间
     */
    private Date hole_detection_time;
    /**
     * 公布时间
     */
    private Date hole_public_time;
    /**
     * 整改类型
     */
    private String hole_rectify_type;
    /**
     * 发现人
     */
    private String hole_finder;
    /**
     * 要求整改时间
     */
    private Date hole_reform_time;
    /**
     * 漏洞说明
     */
    private String hole_remark;
    /**
     * 修复建议
     */
    private String hole_repair;
    /**
     * 参考信息
     */
    private String hole_referenceinfo;
    /**
     * 附件ID
     */
    private String hole_file_id;
    /**
     * 附件类型
     */
    private String hole_file_type;
    /**
     * 处置状态
     */
    private String hole_take_status;
    /**
     * 漏洞发现工具，可以存多个id，英文分号隔开
     */
    private String hole_discovery_tool;
    /**
     * 发现方式
     */
    private String hole_discovery_method;
    /**
     * 发现状态
     */
    private String hole_discovery_status;
    /**
     * 验证状态
     */
    private String hole_test_status;
    /**
     * 复测状态
     */
    private String hole_retest_status;
    /**
     * 修复状态
     */
    private String hole_repair_status;
    /**
     * 关闭状态
     */
    private String hole_close_status;
    /**
     * 漏洞发现次数
     */
    private Integer hole_find_num;
    /**
     * 白名单状态  0-非白名单 1-是白名单
     */
    private String hole_white_status;
    /**
     * 是否误报  0-非误报 1-是误报
     */
    private String hole_false_status;



    /**
     * 漏洞发现人id
     */
    private String hole_discover_user_id;

    /**
     * 漏洞发现人部门id
     */
    private String hole_discover_department_id;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
