<template>
    <div class="hybrid-top-container" v-if="menuStyle === 'hybrid'" :style="{ background: sidebarBg.menuBg }">
        <logo v-if="showLogo" :collapse="false" />
        <div class="hybrid-avatar-wrap" v-if="!showNavHamburger_IN_hybrid">
            <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
                <div class="avatar-wrapper">
                    <img :src="require('../../../assets/images/user2.png')" class="user-avatar" />
                    <i class="el-icon-caret-bottom" />
                </div>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="logout">
                        <span style="display: block">退出登录</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
import Logo from '@/layout/components/Sidebar/Logo'
import { mapGetters } from 'vuex'
export default {
    name: "HybridMenuheader",
    components: { Logo },
    props: {},
    watch: {},
    computed: {
        ...mapGetters(['menuStyle', 'sidebarBg', 'showNavHamburger_IN_hybrid']),
        showLogo () {
            return this.$store.state.settings.sidebarLogo
        },
    },
    data () {
        return {}
    },
    created () { },
    mounted () { },
    methods: {
        async logout () {
            await this.$store.dispatch('user/logout')
            this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        }
    },
}
</script>

<style lang="scss" scoped>
</style>