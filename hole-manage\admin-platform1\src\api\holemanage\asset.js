import request from '@/utils/request'
import qs from 'qs'

export function recurseAssetIpCount(data) {
  return request({
    url: '/holemanage/asset/recurseAssetIpCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function recurseAssetWebsiteCount(data) {
  return request({
    url: '/holemanage/asset/recurseAssetWebsiteCount',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function recurseholeIpCount(data) {
  return request({
    url: '/holemanage/hole/recurseHoleIpCount/' + data,
    method: 'get',
    data: qs.stringify(data)
  })
}
export function recurseHoleWebsiteCount(data) {
  return request({
    url: '/holemanage/hole/recurseHoleWebsiteCount/' + data,
    method: 'get',
    data: qs.stringify(data)
  })
}
export function saveHoleIp(data) {
  return request({
    url: '/holemanage/hole/saveHoleBasicIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageAssetIp(data) {
  return request({
    url: '/holemanage/asset/pageAssetIp',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function pageAssetWebsite(data) {
  return request({
    url: '/holemanage/asset/pageAssetWebsite',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function saveAssetIp(data) {
  return request({
    url: '/holemanage/asset/saveAssetIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageHoleIpAgg(data) {
  return request({
    url: '/holemanage/hole/pageHoleIpAgg',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageHoleWebsiteAgg(data) {
  return request({
    url: '/holemanage/hole/pageHoleWebsiteAgg',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageHoleWebsite(data) {
  return request({
    url: '/holemanage/hole/pageHoleWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageHoleIp(data) {
  return request({
    url: '/holemanage/hole/pageHoleIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}

export function listHoleBasicIp(data) {
  return request({
    url: '/holemanage/hole/listHoleBasicIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function listHoleBasicWebsite(data) {
  return request({
    url: '/holemanage/hole/listHoleBasicWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function deleteHoleBasicIp(data) {
  return request({
    url: '/holemanage/hole/deleteHoleBasicIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}

export function saveAndSendEmail(data) {
  return request({
    url: '/holemanage/hole/saveAndSendEmail',
    method: 'post',
    headers: { 'Content-Type': "'multipart/form-data" },
    data: data
  })
}
export function saveHolePlanBackAndSendEmail(data) {
  return request({
    url: '/holemanage/hole/saveHolePlanBackAndSendEmail',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

export function saveAssetWebsite(data) {
  return request({
    url: '/holemanage/asset/saveAssetWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function deleteAssetIp(data) {
  return request({
    url: '/holemanage/asset/deleteAssetIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function deleteAssetWebsite(data) {
  return request({
    url: '/holemanage/asset/deleteAssetWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function deleteData(data) {
  return request({
    url: '/holemanage/asset/deleteData',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchEditAssetIp(data) {
  return request({
    url: '/holemanage/asset/batchEditAssetIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchEditAssetWebsite(data) {
  return request({
    url: '/holemanage/asset/batchEditAssetWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function getAssetByIp(data) {
  return request({
    url: '/holemanage/asset/getAssetByIp/' + data,
    method: 'get'
  })
}
export function batchDeleteAssetIp(data) {
  return request({
    url: '/holemanage/asset/batchDeleteAssetIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchDeleteAssetWebsite(data) {
  return request({
    url: '/holemanage/asset/batchDeleteAssetWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchDeleteHoleBasicIp(data) {
  return request({
    url: '/holemanage/hole/batchDeleteHoleBasicIp',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchDeleteHoleBasicWebsite(data) {
  return request({
    url: '/holemanage/hole/batchDeleteHoleBasicWebsite',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}

export function doReform(data) {
  return request({
    url: '/holemanage/hole/doReform',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

export function pageHoleReform(data) {
  return request({
    url: '/holemanage/hole/pageHoleReform',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageHoleLibrary(data) {
  return request({
    url: '/holemanage/holeLibrary/pageHoleLibrary',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function pageHolePath(data) {
  return request({
    url: '/holemanage/holeLibrary/pageHolePath',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function updateHolePath(data) {
  return request({
    url: '/holemanage/holeLibrary/updateHolePath',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function downloadHoleLibraryFile(data) {
  return request({
    url: '/holemanage/holeLibrary/downloadHoleLibraryFile',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function downloadFile(data) {
  return request({
    url: '/holemanage/holeLibrary/download',
    method: 'post',
    data: qs.stringify(data),
    responseType: 'blob'
  })
}

export function saveAndSendHolePath(data) {
  return request({
    url: '/holemanage/holeLibrary/saveAndSendHolePath',
    method: 'post',
    headers: { 'Content-Type': "'multipart/form-data" },
    data: data
  })
}
export function grenrationDownFile(data) {
  return request({
    url: '/holemanage/hole/grenrationDownFile',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function listHolePatch(data) {
  return request({
    url: '/holemanage/holeLibrary/listHolePatch',
    method: 'post',
    data
  })
}
