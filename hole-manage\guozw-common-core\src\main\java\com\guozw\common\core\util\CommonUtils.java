package com.guozw.common.core.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.bean.TaskSchedule;
import com.guozw.common.core.constant.CommonEnum;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.Cleanup;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.activation.MimetypesFileTypeMap;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.core.util.XmlUtil.*;

/**
 * Created by guozw on 2020/7/4.
 */
@Slf4j
public class CommonUtils {
    private static final String IP_UTILS_FLAG = ",";
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IP = "0:0:0:0:0:0:0:1";
    private static final String LOCALHOST_IP1 = "127.0.0.1";
    private static final String[] WEEKS = {"MON", "TUE", "WED", "THE", "FRI", "SAT", "SUN"};
    public static String getIpAddr(HttpServletRequest request){
        String ip = null;
        try {
            //以下两个获取在k8s中，将真实的客户端IP，放到了x-Original-Forwarded-For。而将WAF的回源地址放到了 x-Forwarded-For了。
            ip = request.getHeader("X-Original-Forwarded-For");
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Forwarded-For");
            }
            //获取nginx等代理的ip
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("x-forwarded-for");
            }
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (StringUtils.isEmpty(ip) || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            //兼容k8s集群获取ip
            if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                if (LOCALHOST_IP1.equalsIgnoreCase(ip) || LOCALHOST_IP.equalsIgnoreCase(ip)) {
                    //根据网卡取本机配置的IP
                    InetAddress iNet = null;
                    try {
                        iNet = InetAddress.getLocalHost();
                    } catch (UnknownHostException e) {
                        log.error("getClientIp error: ", e);
                    }
                    ip = iNet.getHostAddress();
                }
            }
        } catch (Exception e) {
            log.error("获取客户端ip错误【{}】", ExceptionUtil.stacktraceToString(e));
        }
        //使用代理，则获取第一个IP地址
        if (!StringUtils.isEmpty(ip) && ip.indexOf(IP_UTILS_FLAG) > 0) {
            ip = ip.substring(0, ip.indexOf(IP_UTILS_FLAG));
        }
        return ip;
    }

    public static String getIpAddress(ServerHttpRequest request) {
        // 根据 HttpHeaders 获取 请求 IP地址
        String ip = request.getHeaders().getFirst("X-Forwarded-For");
        if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("x-forwarded-for");
            if (ip != null && ip.length() != 0 && !UNKNOWN.equalsIgnoreCase(ip)) {
                // 多次反向代理后会有多个ip值，第一个ip才是真实ip
                if (ip.contains(IP_UTILS_FLAG)) {
                    ip = ip.split(IP_UTILS_FLAG)[0];
                }
            }
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("X-Real-IP");
        }
        //兼容k8s集群获取ip
        if (StringUtils.isEmpty(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
            if (LOCALHOST_IP1.equalsIgnoreCase(ip) || LOCALHOST_IP.equalsIgnoreCase(ip)) {
                //根据网卡取本机配置的IP
                InetAddress iNet = null;
                try {
                    iNet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    log.error("getClientIp error: ", e);
                }
                ip = iNet.getHostAddress();
            }
        }
        return ip;
    }
    /**
     * 文件下载
     *
     * @param file
     * @param response
     * @throws IOException
     */
    public static void download(File file, HttpServletResponse response) throws IOException {
        String fileName = file.getName();
        String type = new MimetypesFileTypeMap().getContentType(fileName);
        response.setHeader("Content-Length", String.valueOf(file.length()));
        response.setHeader("Content-type", type);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        @Cleanup FileInputStream fileInputStream = new FileInputStream(file);
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        StreamUtils.copy(fileInputStream, outputStream);
    }

    /**
     *  上传
     * @param filePath 文件存储路径
     * @param file 文件
     * @return
     * @throws IOException
     */
    public static Map upload(String filePath,MultipartFile file) throws IOException {

            // 获取文件名
            String fileName = file.getOriginalFilename();
            log.info("上传的文件名为：" + fileName);
            // 获取文件的后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            log.info("文件的后缀名为：" + suffixName);
            String fileNameNew=UUID.randomUUID()+ suffixName;
            String path = filePath +"/"+fileNameNew;
            File dest = new File(path);
            // 检测是否存在目录
            if (!dest.getParentFile().exists()) {
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            Map<String,Object> map=new HashMap<>();
            map.put("file_storage_name",fileNameNew);
            map.put("file_real_name",fileName);
            map.put("file_suffix",suffixName);
            map.put("file_type",suffixName.substring(1));
            return map;
    }

    /**
     * xml文件排除特殊符号
     * @param inputFile 输入文件
     * @param outFile 输出文件
     * @param labelList 查找的标签列表
     */
    public static void xmlUpload(File inputFile,File outFile,List<String> labelList) {
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader bReader = null;
        OutputStreamWriter osw = null;
        FileOutputStream fos = null;
        BufferedWriter bWriter = null;
        try {
            String line;
            StringBuffer stringBuffer;
            fis = new FileInputStream(inputFile);//定义输入文件
            fos = new FileOutputStream(outFile);//定义输出文件
            isr = new InputStreamReader(fis, "UTF-8");//读取输入文件
            osw = new OutputStreamWriter(fos, "UTF-8");//写入输入文件
            bReader = new BufferedReader(isr);//读取缓冲区
            bWriter = new BufferedWriter(osw);//写入缓存区
            while ((line = bReader.readLine()) != null) { //按行读取数据
                String group="";
                for (String label : labelList) {
                    Pattern pattern = Pattern.compile("<"+label+">.*?(&|<|>|'|\").*?</"+label+">");
                    Matcher matcher = pattern.matcher(line);
                    StringBuffer chapterContentLine = new StringBuffer();

                    if (matcher.find()) {

                        String descript = line.split("<"+label+">")[1].split("</"+label+">")[0];
                        String replace = descript.replace("&", "&amp;")
                                .replace("<", "&lt;")
                                .replace(">", "&gt;")
                                .replace("\"", "&quot;")
                                .replace("'", "&apos;");
                        chapterContentLine.append(replace);
                        chapterContentLine.insert(0, "<"+label+">");
                        chapterContentLine.append("</"+label+">");
                        group = chapterContentLine.toString();
                        break;
                    } else {
                        group=line;

                    }

                }
                bWriter.write(group);
                bWriter.newLine();

            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                bReader.close();//关闭读取缓冲区
                isr.close();//关闭读取文件内容
                fis.close();//关闭读取文件
                bWriter.close();//关闭写入缓存区
                osw.close();//关闭写入文件内容
                fos.close();//关闭写入文件
            } catch (IOException e) {

                throw new RuntimeException(e);
            }
        }
    }

    /**
     * xml文件读取
     * @param file
     * @return
     * @throws IOException
     */
    public static Document xmlTransformation(File file) throws IOException {

        //xml读取
        Document document = readXML(file);
        Element goalElement = getElementByXPath("//cnnvd",document);
        //获取match下所有unit标签
        List<Element> elementList = getElements(goalElement,"entry");
        for (Element element : elementList){
            String name1 = elementText(element, "name");
            System.out.println(name1);
        }
        return document;
    }
    public static Document readXml(InputStream xmlInputStream){
        return readXml(xmlInputStream, false);
    }
    public static Document readXml(InputStream xmlInputStream, boolean validate){  // 参考mybatis parsing模块
        try {
            DocumentBuilderFactory factory=DocumentBuilderFactory.newInstance();
            factory.setValidating(validate);

            factory.setNamespaceAware(false);
            factory.setIgnoringComments(true);
            factory.setIgnoringElementContentWhitespace(false);
            factory.setCoalescing(false);
            factory.setExpandEntityReferences(true);

            DocumentBuilder builder=factory.newDocumentBuilder();
            return builder.parse(xmlInputStream);
        } catch (ParserConfigurationException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (org.xml.sax.SAXException e) {
            throw new RuntimeException(e);
        }
    }
        /**
         * 文件下载，并且指定下载的文件名称
         *
         * @param file
         * @param response
         * @throws IOException
         */
    public static void download(File file, HttpServletResponse response, String fileName) throws IOException {
        String type = new MimetypesFileTypeMap().getContentType(fileName);
        response.setHeader("Content-Length", String.valueOf(file.length()));
        response.setHeader("Content-type", type);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        @Cleanup FileInputStream fileInputStream = new FileInputStream(file);
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        StreamUtils.copy(fileInputStream, outputStream);
    }

    @SneakyThrows
    public static void ftl2word(String dir, String ftl, File target, Map<String, Object> data) {
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        configuration.setDefaultEncoding(CharsetUtil.UTF_8);
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        configuration.setDirectoryForTemplateLoading(dirFile);
        Template template = configuration.getTemplate(ftl);
        @Cleanup FileOutputStream fileOutputStream = new FileOutputStream(target);
        @Cleanup OutputStreamWriter outputStreamWriter = new OutputStreamWriter(fileOutputStream, CharsetUtil.UTF_8);
        @Cleanup BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
        template.process(data, bufferedWriter);
    }

    /**
     * 获取IPV4 CIDR形式下所有的ipv4
     *
     * @param cidr IPV4的CIDR格式 （***********/24）
     */
    public static List<String> getIpsV4ByCidr(String cidr) {
        String ip = cidr.split("/")[0];
        int mask = Integer.parseInt(cidr.split("/")[1]);
        String ipFrom = Ipv4Util.getBeginIpStr(ip, mask);
        String ipTo = Ipv4Util.getEndIpStr(ip, mask);
        return Ipv4Util.list(ip, mask, true);
    }

    /**
     * 获取IPV4 区间范围下所有的ipv4
     *
     * @param ipFrom IPV4的起始ip
     * @param ipTo   IPV4的结束ip
     */
    public static List<String> getIpsV4ByRange(String ipFrom, String ipTo) {
        return Ipv4Util.list(ipFrom, ipTo);
    }

    /**
     * 创建cron表达式
     * @param taskSchedule
     * @return
     */
    public static String createCronExpression(TaskSchedule taskSchedule) {
        if (StrUtil.isNotBlank(taskSchedule.getTime())) {
            List<String> timeList = StrUtil.split(taskSchedule.getTime(), StrUtil.COLON);
            if (timeList.size() >= 2 && StrUtil.isNotBlank(taskSchedule.getScheduleType())) {
                String hour = timeList.get(0);
                String minute = timeList.get(1);
                String second = timeList.get(2);
                if (CommonEnum.TaskScheduleType.EVERY_DAY_EXECUTE.getValue().equals(taskSchedule.getScheduleType())) {
                    return StrUtil.format("{} {} {} * * ? *", second, minute, hour);
                } else if (CommonEnum.TaskScheduleType.EVERY_WEEK_EXECUTE.getValue().equals(taskSchedule.getScheduleType())) {
                    return StrUtil.format("{} {} {} ? * {}", second, minute, hour, taskSchedule.getWeek());
                } else if (CommonEnum.TaskScheduleType.EVERY_MONTH_EXECUTE.getValue().equals(taskSchedule.getScheduleType())) {
                    return StrUtil.format("{} {} {} {} * ? *", second, minute, hour, taskSchedule.getDayofmonth());
                }
            }
        }

        return StrUtil.EMPTY;
    }


    public static void main(String[] args) {
        TaskSchedule taskSchedule1 = new TaskSchedule();
        taskSchedule1.setScheduleType(CommonEnum.TaskScheduleType.TIMING_EXECUTE.getValue());
        taskSchedule1.setDate("2022-12-28");
        taskSchedule1.setTime("11:26:00");
        String cronExpression1 = createCronExpression(taskSchedule1);
        System.out.println(StrUtil.format("定时执行【{}】", cronExpression1));

        TaskSchedule taskSchedule2 = new TaskSchedule();
        taskSchedule2.setScheduleType(CommonEnum.TaskScheduleType.EVERY_DAY_EXECUTE.getValue());
        taskSchedule2.setTime("11:26");
        String cronExpression2 = createCronExpression(taskSchedule2);
        System.out.println(StrUtil.format("每天执行【{}】", cronExpression2));

        TaskSchedule taskSchedule3 = new TaskSchedule();
        taskSchedule3.setScheduleType(CommonEnum.TaskScheduleType.EVERY_WEEK_EXECUTE.getValue());
        taskSchedule3.setTime("11:26");
        taskSchedule3.setWeek("1");
        String cronExpression3 = createCronExpression(taskSchedule3);
        System.out.println(StrUtil.format("每周执行【{}】", cronExpression3));

        TaskSchedule taskSchedule4 = new TaskSchedule();
        taskSchedule4.setScheduleType(CommonEnum.TaskScheduleType.EVERY_MONTH_EXECUTE.getValue());
        taskSchedule4.setTime("11:26");
        taskSchedule4.setDayofmonth("1");
        String cronExpression4 = createCronExpression(taskSchedule4);
        System.out.println(StrUtil.format("每月执行【{}】", cronExpression4));

        System.out.println(Math.ceil(4 / 3));
        System.out.println(Math.floor(4 / 3));
        System.out.println(4 / 3.0);


        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.parseDate("2022-11-1"), DateUtil.parseDate("2022-11-3"), DateField.DAY_OF_MONTH);

        String s = "";
    }



}
