package com.guozw.security.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@NoArgsConstructor
@Accessors(chain = true)
@Data
@TableName(value = "PathAddress")
public class PathAddressDTO implements Serializable {

    private static final long serialVersionUID = 2011041593978426029L;
    /**
     * 地址
     */
    private String path;
    /**
     * 参数
     */
    private Object query;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
