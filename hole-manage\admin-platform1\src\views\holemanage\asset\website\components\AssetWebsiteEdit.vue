<template>
    <div id="topDiv">
        <vxe-modal
            height="99%"
            width="70%"
            position="center"
            resize
            :title="modalInfo.title"
            v-model="modalInfo.show"
            @close="handleModalClose()"
            showFooter
        >
            <vxe-form
                ref="myForm"
                title-width="100"
                :data="modalForm"
                :rules="rules"
                title-align="right"
                prevent-submit
                span="8"
            >
                <div class="bigTitle">
                    <span>基本信息</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item title="网站" field="asset_website_name">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.asset_website_name"
                            placeholder="网站"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="IPV4" field="asset_ipv4">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.asset_ipv4"
                            placeholder="IPV4"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="IPV6" field="asset_ipv6">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.asset_ipv6"
                            placeholder="IPV6"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="资产分组" field="departmentname">
                    <template v-slot="scope">
                        <vxe-pulldown ref="xDown1" transfer style="width: 100%">
                            <template v-slot>
                                <vxe-input
                                    v-model.trim="modalForm.departmentname"
                                    placeholder="资产分组"
                                    clearable
                                    readonly
                                    :disabled="modalInfo.isActionView"
                                    @input="$refs.myForm.updateStatus(scope)"
                                    @focus="focusEvent1"
                                ></vxe-input>
                            </template>
                            <template v-slot:dropdown>
                                <div class="my-dropdown1">
                                    <DepartmentTree
                                        ref="departmentTree"
                                        :showActionButton="false"
                                        @department-tree-node-click="
                                            handleNodeClick
                                        "
                                    ></DepartmentTree>
                                </div>
                            </template>
                        </vxe-pulldown>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="设备类型" field="device_type">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.device_type"
                            readonly
                            clearable
                            filterable
                            placeholder="设备类型"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in deviceTypeEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="主机名称" field="host_name">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.host_name"
                            placeholder="主机名称"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="系统类型" field="system_type">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.system_type"
                            readonly
                            clearable
                            filterable
                            placeholder="系统类型"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in systemTypeEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="资产状态" field="asset_status">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.asset_status"
                            readonly
                            clearable
                            filterable
                            placeholder="资产状态"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in assetStatusEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="备注信息" field="remark">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.remark"
                            placeholder="备注信息"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                        </vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="FQDN" field="fqdn">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.fqdn"
                            placeholder="FQDN"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="Web服务类型" field="webtype">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.webtype"
                            placeholder="Web服务类型"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="技术架构"
                    field="technical"
                    class="lastItem"
                >
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.technical"
                            placeholder="技术架构"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <div class="bigTitle">
                    <span>终端安全</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="内网IP" field="terminal_inip">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.terminal_inip"
                            placeholder="内网IP"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="外网IP" field="terminal_outip">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.terminal_outip"
                            placeholder="外网IP"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="内核版本" field="terminal_kernel">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.terminal_kernel"
                            placeholder="内核版本"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="业务组" field="terminal_business">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.terminal_business"
                            placeholder="业务组"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="系统启动时间" field="terminal_uptime">
                    <template v-slot="scope">
                        <vxe-input
                            type="datetime"
                            readonly
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            v-model.trim="modalForm.terminal_uptime"
                            placeholder="系统启动时间"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="安装时间" field="terminal_installtime">
                    <template v-slot="scope">
                        <vxe-input
                            type="datetime"
                            readonly
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            v-model.trim="modalForm.terminal_installtime"
                            placeholder="安装时间"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="最后上线时间" field="terminal_onlinetime">
                    <template v-slot="scope">
                        <vxe-input
                            type="datetime"
                            readonly
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            v-model.trim="modalForm.terminal_onlinetime"
                            placeholder="最后上线时间"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="最后下线时间"
                    field="terminal_offlinetime"
                >
                    <template v-slot="scope">
                        <vxe-input
                            type="datetime"
                            readonly
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            v-model.trim="modalForm.terminal_offlinetime"
                            placeholder="最后下线时间"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="" field="" class="lastItem">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>硬件配置</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="生产商" field="hardware_producer">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_producer"
                            placeholder="生产商"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="设备型号" field="hardware_equiptype">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_equiptype"
                            placeholder="设备型号"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="序列号" field="hardware_serial">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_serial"
                            placeholder="序列号"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="设备UUID" field="hardware_uuid">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_uuid"
                            placeholder="设备UUID"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="内存" field="hardware_ram">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_ram"
                            placeholder="内存"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="CPU" field="hardware_cpu">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_cpu"
                            placeholder="CPU"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="使用率" field="hardware_utilization">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.hardware_utilization"
                            placeholder="使用率"
                            clearable
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="系统负载" field="hardware_systemload">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.hardware_systemload"
                            readonly
                            clearable
                            filterable
                            placeholder="系统负载"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in systemLoadEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="" field="" class="lastItem">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>资产权重</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="等级保护" field="weight_protection">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_protection"
                            readonly
                            clearable
                            filterable
                            placeholder="等级保护"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in protectionEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="涉密状态" field="weight_secret">
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_secret"
                            readonly
                            clearable
                            filterable
                            placeholder="涉密状态"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in secretEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="重要程度"
                    field="weight_importance"
                    class="lastItem"
                >
                    <template v-slot="scope">
                        <el-select
                            v-model.trim="modalForm.weight_importance"
                            readonly
                            clearable
                            filterable
                            placeholder="重要程度"
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in importanceEnum"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <div class="bigTitle">
                    <span>组织架构</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="单位" field="organ_unit">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.organ_unit"
                            placeholder="单位"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="部门" field="organ_department">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.organ_department"
                            placeholder="部门"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="科室"
                    field="organ_office"
                    class="lastItem"
                >
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.organ_office"
                            placeholder="科室"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <div class="bigTitle">
                    <span>地理位置</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="国家" field="position_country">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.position_country"
                            placeholder="国家"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="省份" field="position_province">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.position_province"
                            placeholder="省份"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="地市" field="position_city">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.position_city"
                            placeholder="地市"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="区县" field="position_county">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.position_county"
                            placeholder="区县"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="其他" field="position_other">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.position_other"
                            placeholder="其他"
                            clearable
                            transfer
                            :disabled="modalInfo.isActionView"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="" field="" class="lastItem">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>联系人信息</span>

                    <el-button
                        size="mini"
                        type="danger"
                        class="delRow"
                        @click="delChecked(0)"
                        v-if="!modalInfo.isActionView"
                        >删除选中</el-button
                    >
                    <el-button
                        size="mini"
                        type="primary"
                        class="addRow"
                        @click="addRow(0)"
                        v-if="!modalInfo.isActionView"
                        >新增一行</el-button
                    >
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-table
                    :ref="table_data_type[0].tableRef"
                    border
                    resizable
                    auto-resize
                    show-overflow
                    keep-source
                    row-key
                    max-height="400"
                    :data="modalForm.sysContactList"
                    :edit-rules="validRules"
                    :edit-config="{
                        trigger: 'click',
                        mode: 'row',
                        activeMethod: activeRowMethod,
                        showStatus: true
                    }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                        :visible="!modalInfo.isActionView"
                    ></vxe-table-column>
                    <vxe-table-column type="seq" width="60"></vxe-table-column>
                    <vxe-table-column
                        field="contact_name"
                        title="姓名"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '姓名'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="contact_phone"
                        title="电话"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '电话'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="contact_email"
                        title="邮箱"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '邮箱'
                        }"
                    ></vxe-table-column>
                </vxe-table>

                <div class="bigTitle">
                    <span>网卡信息</span>

                    <el-button
                        size="mini"
                        type="danger"
                        class="delRow"
                        @click="delChecked(1)"
                        v-if="!modalInfo.isActionView"
                        >删除选中</el-button
                    >
                    <el-button
                        size="mini"
                        type="primary"
                        class="addRow"
                        @click="addRow(1)"
                        v-if="!modalInfo.isActionView"
                        >新增一行</el-button
                    >
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-table
                    :ref="table_data_type[1].tableRef"
                    border
                    resizable
                    auto-resize
                    show-overflow
                    keep-source
                    row-key
                    max-height="400"
                    :data="modalForm.assetNetworkCardList"
                    :edit-rules="validRules"
                    :edit-config="{
                        trigger: 'click',
                        mode: 'row',
                        activeMethod: activeRowMethod,
                        showStatus: true
                    }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                        :visible="!modalInfo.isActionView"
                    ></vxe-table-column>
                    <vxe-table-column type="seq" width="60"></vxe-table-column>
                    <vxe-table-column
                        field="network_card_ipv4"
                        title="IPV4"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: 'IPV4'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="network_card_ipv6"
                        title="IPV6"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: 'IPV6'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="network_card_gateway"
                        title="网关"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '网关'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="network_card_name"
                        title="网卡名称"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '网卡名称'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="network_card_mac"
                        title="Mac地址"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: 'Mac地址'
                        }"
                    ></vxe-table-column>
                </vxe-table>

                <div class="bigTitle">
                    <span>WEB应用</span>

                    <el-button
                        size="mini"
                        type="danger"
                        class="delRow"
                        @click="delChecked(2)"
                        v-if="!modalInfo.isActionView"
                        >删除选中</el-button
                    >
                    <el-button
                        size="mini"
                        type="primary"
                        class="addRow"
                        @click="addRow(2)"
                        v-if="!modalInfo.isActionView"
                        >新增一行</el-button
                    >
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-table
                    :ref="table_data_type[2].tableRef"
                    border
                    resizable
                    auto-resize
                    show-overflow
                    keep-source
                    row-key
                    max-height="400"
                    :data="modalForm.assetSoftWebList"
                    :edit-rules="validRules"
                    :edit-config="{
                        trigger: 'click',
                        mode: 'row',
                        activeMethod: activeRowMethod,
                        showStatus: true
                    }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                        :visible="!modalInfo.isActionView"
                    ></vxe-table-column>
                    <vxe-table-column type="seq" width="60"></vxe-table-column>
                    <vxe-table-column
                        field="asset_web_product"
                        title="产品"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '产品'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="asset_web_com"
                        title="厂商"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '厂商'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="asset_web_version"
                        title="版本"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '版本'
                        }"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="asset_web_edition"
                        title="版次"
                        :edit-render="{
                            name: 'input',
                            attrs: { type: 'text' },
                            placeholder: '版次'
                        }"
                    ></vxe-table-column>
                </vxe-table>
            </vxe-form>
            <template v-slot:footer>
                <el-button type="" @click="handleDialogCancel">取消</el-button>
                <el-button type="primary" :loading="loading" @click="submitForm"
                    >确定</el-button
                >
            </template>
        </vxe-modal>
    </div>
</template>
<script>
import { clearProperty, copyProperty, patterns } from "@/utils/guozw-core.js";
import { saveAssetWebsite, deleteData } from "@/api/holemanage/asset";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
export default {
    name: "AssetWebsiteEdit",
    components: { DepartmentTree },
    props: {
        // 设备类型
        deviceTypeEnum: {
            type: Array,
            default: () => []
        },
        // 系统类型
        systemTypeEnum: {
            type: Array,
            default: () => []
        },
        // 等级保护
        protectionEnum: {
            type: Array,
            default: () => []
        },

        // 重要程度
        importanceEnum: {
            type: Array,
            default: () => []
        },
        // 涉密状态
        secretEnum: {
            type: Array,
            default: () => []
        },

        // 资产状态枚举
        assetStatusEnum: {
            type: Array,
            default: () => []
        },
        // 系统负载枚举
        systemLoadEnum: {
            type: Array,
            default: () => []
        },

        modalInfo: {
            type: Object,
            default: () => {}
        },
        formDefaultData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            // 选中的部门
            selectedDepartment: {},

            table_data_type: [
                {
                    label: "联系人信息",
                    tableRef: "myTable_contact",
                    record: {
                        contact_id: "",
                        contact_name: "",
                        contact_phone: "",
                        contact_email: ""
                    },
                    activeCell: "contact_name",
                    dataType: 1
                },
                {
                    label: "网卡信息",
                    tableRef: "myTable_networkcard",
                    record: {
                        network_card_id: "",
                        network_card_ipv4: "",
                        network_card_ipv6: "",
                        network_card_gateway: "",
                        network_card_name: "",
                        network_card_mac: ""
                    },
                    activeCell: "network_card_ipv4",
                    dataType: 2
                },
                {
                    label: "软件信息",
                    tableRef: "myTable_softweb",
                    record: {
                        asset_web_id: "",
                        asset_web_product: "",
                        asset_web_com: "",
                        asset_web_version: "",
                        asset_web_edition: ""
                    },
                    activeCell: "asset_web_product",
                    dataType: 4
                }
            ],

            modalForm: {
                asset_website_id: "",
                asset_website_name: "",
                asset_ipv4: "",
                asset_ipv6: "",
                // 隶属部门id
                asset_department_id: "",
                // 隶属部门名称
                departmentname: "",
                device_type: "",
                host_name: "",
                system_type: "",
                asset_status: "",
                remark: "",
                fqdn: "",
                webtype: "",
                technical: "",

                terminal_inip: "",
                terminal_outip: "",
                terminal_kernel: "",
                terminal_business: "",
                terminal_uptime: "",
                terminal_installtime: "",
                terminal_onlinetime: "",
                terminal_offlinetime: "",

                hardware_producer: "",
                hardware_equiptype: "",
                hardware_serial: "",
                hardware_uuid: "",
                hardware_ram: "",
                hardware_cpu: "",
                hardware_utilization: "",
                hardware_systemload: "",

                weight_protection: "",
                weight_secret: "",
                weight_importance: "",

                organ_unit: "",
                organ_department: "",
                organ_office: "",

                position_country: "",
                position_province: "",
                position_city: "",
                position_county: "",
                position_other: "",

                // 联系信息列表
                sysContactList: [],
                // 网卡信息列表
                assetNetworkCardList: [],
                // 软件信息列表
                assetSoftWebList: [],

                // 关闭弹窗的时候是否需要刷新表格
                isRefreshTable: false
            },
            rules: {
                asset_website_name: [
                    { required: true, message: "必填字段" },
                    { pattern: patterns.url, message: "网站格式不正确" }
                ],
                departmentname: [{ required: true, message: "必填字段" }],
                host_name: [
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ]
            },
            /* 表格中的字段校验 */
            validRules: {
                contact_name: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 10, message: "长度最大为 10 字符" }
                ],
                contact_phone: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 11, message: "长度最大为 11 字符" }
                ],
                contact_email: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],

                network_card_ipv4: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                network_card_ipv6: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                network_card_gateway: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                network_card_name: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                network_card_mac: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],

                asset_web_product: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                asset_web_com: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                asset_web_version: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ],
                asset_web_edition: [
                    { required: false, message: "必填字段" },
                    { min: 0, max: 100, message: "长度最大为 100 字符" }
                ]
            }
        };
    },

    watch: {
        formDefaultData(newVal, oldVal) {
            console.log("newVal", newVal);
            copyProperty(newVal, this.modalForm);
            this.initData();
        }
    },

    computed: {},
    created() {
        this.initData();
    },
    methods: {
        handleModalClose() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
            if (this.isRefreshTable) {
                this.$emit("refreshTable");
            }
        },
        handleDialogCancel() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
            if (this.isRefreshTable) {
                this.$emit("refreshTable");
            }
        },
        initData() {},
        focusEvent1() {
            this.$refs.xDown1.showPanel();
        },
        // 处理节点点击
        handleNodeClick(node) {
            console.log("点击树节点", node);
            this.selectedDepartment = node;
            this.modalForm.departmentname = node.departmentname;
            this.modalForm.asset_department_id = node.departmentcode;
            this.$refs.myForm.clearValidate("departmentname");
            this.$refs.xDown1.hidePanel();
        },
        activeRowMethod({ row, column }) {
            console.log("activeRowMethod", row, column);
            return true;
        },
        async addRow(index) {
            let record = this.table_data_type[index].record;
            const tableRef = this.table_data_type[index].tableRef;
            const activeCell = this.table_data_type[index].activeCell;
            let { row: newRow } = await this.$refs[tableRef].insertAt(
                record,
                -1
            );
            await this.$refs[tableRef].setActiveCell(newRow, activeCell);
            // 插入一条数据并触发校验
            // const errMap = await this.$refs.myTable
            //     .validate(newRow)
            //     .catch(errMap => errMap);
        },

        /* 删除选中的数据 */
        async delChecked(index) {
            const tableRef = this.table_data_type[index].tableRef;
            const dataType = this.table_data_type[index].dataType;
            const checkedRecords = this.$refs[tableRef].getCheckboxRecords();
            // 临时新增的数据
            let tempRows = [];
            // 已经入库的数据
            let insertedIds = [];
            if (checkedRecords && checkedRecords.length > 0) {
                checkedRecords.forEach(item => {
                    // 主键
                    let pk = "";
                    if (index == 0) {
                        pk = item.contact_id;
                    } else if (index == 1) {
                        pk = item.network_card_id;
                    } else if (index == 2) {
                        pk = item.asset_web_id;
                    }
                    // 如果主键不为空，则是已经入库的数据
                    if (pk) {
                        insertedIds.push(pk);
                    } else {
                        tempRows.push(item);
                    }
                });

                if (insertedIds.length > 0) {
                    await deleteData({
                        pkList: insertedIds,
                        dataType
                    }).then(res => {
                        this.isRefreshTable = res.data;
                    });
                }
                this.$refs[tableRef].remove(checkedRecords);
                this.$XModal.message({
                    status: "success",
                    message: "删除成功"
                });
            } else {
                this.$XModal.message({
                    status: "warning",
                    message: "请至少选中一条记录"
                });
            }
        },

        getQueryCondition() {
            let queryCondition = JSON.parse(JSON.stringify(this.modalForm));

            // 获取新增的记录
            let insertRecords_contact = this.$refs[
                this.table_data_type[0].tableRef
            ].getInsertRecords();
            let insertRecords_networkCard = this.$refs[
                this.table_data_type[1].tableRef
            ].getInsertRecords();

            let insertRecords_softweb = this.$refs[
                this.table_data_type[2].tableRef
            ].getInsertRecords();

            if (insertRecords_contact && insertRecords_contact.length > 0) {
                insertRecords_contact.forEach(item => {
                    if (queryCondition.sysContactList) {
                        queryCondition.sysContactList.push(item);
                    } else {
                        queryCondition.sysContactList = [];
                        queryCondition.sysContactList.push(item);
                    }
                });
            }
            if (
                insertRecords_networkCard &&
                insertRecords_networkCard.length > 0
            ) {
                insertRecords_networkCard.forEach(item => {
                    if (queryCondition.assetNetworkCardList) {
                        queryCondition.assetNetworkCardList.push(item);
                    } else {
                        queryCondition.assetNetworkCardList = [];
                        queryCondition.assetNetworkCardList.push(item);
                    }
                });
            }

            if (insertRecords_softweb && insertRecords_softweb.length > 0) {
                insertRecords_softweb.forEach(item => {
                    if (queryCondition.assetSoftWebList) {
                        queryCondition.assetSoftWebList.push(item);
                    } else {
                        queryCondition.assetSoftWebList = [];
                        queryCondition.assetSoftWebList.push(item);
                    }
                });
            }

            console.log("queryCondition", JSON.stringify(queryCondition));
            return queryCondition;
        },

        // 提交表单
        submitForm() {
            this.$refs.myForm
                .validate()
                .then(() => {
                    this.loading = true;
                    const queryCondition = this.getQueryCondition();

                    saveAssetWebsite(queryCondition)
                        .then(res => {
                            if (res.data) {
                                this.$XModal.message({
                                    message: this.modalForm.asset_website_id
                                        ? "修改成功"
                                        : "新增成功",
                                    status: "success"
                                });
                                this.$emit("refreshTable");
                                this.modalInfo.show = false;
                            } else {
                                this.$XModal.message({
                                    message: res.message,
                                    status: "error"
                                });
                            }
                        })
                        .finally(() => {
                            this.loading = false;
                            clearProperty(this.modalForm);
                            this.modalInfo.show = false;
                        });
                })
                .catch(err => {
                    console.log(err);
                    return false;
                });
        }
    }
};
</script>
<style lang="scss" scoped>
#topDiv {
    .vxe-form /deep/ .vxe-form--item-inner {
        min-height: 30px !important;
    }
    .bigTitle {
        margin-top: 10px;
        span {
            font-size: 15px;
            font-weight: bolder;
        }
    }

    .lastItem {
        margin-bottom: 10px !important;
    }
    .el-divider--horizontal {
        margin: 5px 0;
    }
    .tip {
        color: red;
        font-size: 10px;
    }

    .my-dropdown1 {
        height: 200px;
        overflow: auto;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
    }
    .list-item1:hover {
        background-color: #f5f7fa;
    }
    .addRow {
        float: right;
        position: relative;
        top: -5px;
    }
    .delRow {
        float: right;
        margin-left: 5px;
        position: relative;
        top: -5px;
    }
}
</style>
