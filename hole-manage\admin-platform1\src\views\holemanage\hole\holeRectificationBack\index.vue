<template>
  <el-container>

    <el-main>

      <!-- region 查询条件表单-->
      <vxe-form
        ref="queryConditionForm"
        title-width="100"
        title-align="right"
        span="8"
        :data="queryParams"
        @submit="handleQueryConditionFormSubmit"
        @toggle-collapse="handleQueryConditionFormToggleCollapse"
      >
        <vxe-form-item field="hole_reform_id" title="整改编号">
          <vxe-input
            clearable
            placeholder="整改编号"
            v-model="queryParams.hole_reform_id"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="holeName" title="漏洞名称">
          <vxe-input
            clearable
            placeholder="漏洞名称"
            v-model="queryParams.holeName"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="reformStatusList" title="目前状态">
          <el-select
            v-model="queryParams.reformStatusList"
            clearable
            filterable
            placeholder="目前状态"
            style="width:100%"
            multiple
            @change="loadTable"
          >
            <el-option
              v-for="item in holeReformStatusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="reformDateGt" title="下发时间开始">
          <vxe-input
            clearable
            placeholder="下发时间开始"
            type="date"
            v-model="queryParams.reformDateGt"
            @change="reformDateGtChange"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="reformDateLt" title="下发时间结束">
        <vxe-input
          clearable
          placeholder="下发时间结束"
          type="date"
          v-model="queryParams.reformDateLt"
          @change="reformDateLtChange"
        ></vxe-input>
      </vxe-form-item>
        <vxe-form-item align="right" >
          <vxe-button
            type="submit"
            status="primary"
            icon="fa fa-search"
          >查询
          </vxe-button
          >
          <vxe-button type="reset" icon="fa fa-refresh"
          >重置
          </vxe-button
          >
        </vxe-form-item>

      </vxe-form>

      <!-- region 表格 -->
      <div  v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="myTable"
          ref="myTable"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          auto-resize
          resizable
          height="auto"
          show-overflow
          :sort-config="{ trigger: 'cell', remote: true }"
          :tooltip-config="{contentMethod: showTooltipMethod}"
          :custom-config="{ storage: true }"
          :data="page.records"
          :checkbox-config="{ trigger: 'row' }"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="hole_reform_id"
            title="整改编号"
            min-width="150"
          ></vxe-table-column>
          <vxe-table-column
            field="holeName"
            title="漏洞名称"
            min-width="300"

          >
          </vxe-table-column>
          <vxe-table-column
            field="request_success_time"
            title="要求完成时间"
            min-width="100"

          >
          </vxe-table-column>
          <vxe-table-column

            title="IP地址/网站名称"
            min-width="100"
          >
            <template v-slot="{ row }">
              {{row.hole_type === '1' ? row.holeBasicIp.asset_ip : row.holeBasicWebsite.asset_website_name }}
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="departmentname"
            title="资产单位"
            min-width="100"

          >
          </vxe-table-column>
          <vxe-table-column
            field="reform_staus"
            title="目前状态"
            min-width="100"
            :formatter="['formatSelect', holeReformStatusEnum]"
          >
          </vxe-table-column>
          <vxe-table-column
            title="探针品牌"
            width="150"

          >
            <template v-slot="{ row }">
              {{row.hole_type === '1' ? formatter(row.holeBasicIp.hole_discovery_tool,toolBasicInfoEnum) : formatter(row.holeBasicWebsite.hole_discovery_tool,toolBasicInfoEnum) }}
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="createdate"
            title="下发时间"
            min-width="100"

          >
          </vxe-table-column>
          <vxe-table-column
            field="special_remark"
            title="整改说明"
            width="150"
          >

          </vxe-table-column>
          <vxe-table-column
            field=""
            title="附件"
            min-width="100"
          >
            <template v-slot="{ row }">
              <el-tag
                type="success"
                v-if="row.fileReform"
                @click="handleDownloadFile(row.fileReform)"
              >
                {{row.fileReform.file_real_name}}
              </el-tag>
              <span v-else>无</span>
            </template>

          </vxe-table-column>
          <vxe-table-column
            field=""
            title="操作"
            fixed="right"
            min-width="100"
          >
            <template v-slot="{ row }">
              <el-button
                type="primary"
                @click="handleRectification(row)"
              >
                <svg-icon icon-class="freeback" style="color:white;font-size: 16px"/>
                反馈
              </el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager
        ref="pager"
        :current-page="queryParams.pagenumber"
        :page-size="queryParams.pagesize"
        :total="page.total"
        @page-change="handlePageChange"
      >
      </vxe-pager>
      <!-- endregion -->
      <hole-rectification-modal ref="holeRectificationModal"
                                :modal-show="rectificationReformModalShow"
                                :hole-reform-data="rectificationReformModalHoleReform"
                                :email-template="emailTemplate"
                                :user-list="userList"
                                :close-status-enum="closeStatusEnum"
                                :repair-status-enum="repairStatusEnum"
                                :retest-status-enum="retestStatusEnum"
                                :test-status-enum="testStatusEnum"
                                :hole-discovery-method-enum="holeDiscoveryMethodEnum"
                                :hole-level-enum="holeLevelEnum"
                                :hole-type-enum="holeTypeEnum"
                                :disable-form="modalDisable"
                                @close="rectificationReformModalShow = false"
                                @refreshTable="refreshTable"
      />
    </el-main>
  </el-container>
</template>

<script>
import {
  pageHoleReform
} from "@/api/holemanage/asset";
import holeRectificationModal from "@/views/holemanage/hole/holeRectificationBack/components/holeRectificationModal";
import {listDictionarys} from "@/api/security/dictionary";
import {mapGetters} from "vuex";
import {listConfigEmailTemplate} from "@/api/config/email";
import {listUser,pageUsers} from "@/api/security/user";
import {downloadFile} from "@/utils/file";
import {listToolBasic} from "@/api/testManage";

export default {
  name: "index",
  components: {holeRectificationModal},
  data() {
    return {
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      loading: true,
      page: {
        total: 0,
        records: []
      },
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        reformDateGt: null,
        reformDateLt: null,
        reformStatusList: ['0','2'],
        holeName: '',
        hole_reform_id: ''
      },
      holeReformStatusEnum:[],
      rectificationReformModalShow: false,
      rectificationReformModalHoleReform: {},

      emailTemplate:[],
      userList:[],
      holeLevelEnum: [],
      holeTypeEnum: [],
      holeDiscoveryMethodEnum: [],
      testStatusEnum: [],
      retestStatusEnum: [],
      toolBasicInfoEnum:[],
      closeStatusEnum: [],
      repairStatusEnum: [],
      planBackTypeEnum:[],
      modalDisable: false
    };
  },
  created() {
    this.initData();
    this.loadTable();
  },

  mounted() {

    this.setQueryConditionFormHeight();
    this.setTableHeight();
    this.windowResize();
  },
  methods: {
    initData() {
      // 加载字典
      listDictionarys().then(res => {
        res.data.forEach(item => {
          const dict = {
            label: item.dictionaryname,
            value: item.dictionaryvalue
          };
          // 漏洞下发 状态
          if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeReformStatus
              .dictionarytypecode
          ) {
            this.holeReformStatusEnum.push(dict);
          }

          // 漏洞类型
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeType
              .dictionarytypecode
          ) {
            this.holeTypeEnum.push(dict);
          }
          //漏洞级别
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeLevel
              .dictionarytypecode
          ) {
            this.holeLevelEnum.push(dict)
          }
          //验证状态
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeTestStatus
              .dictionarytypecode
          ) {
            this.testStatusEnum.push(dict)
          }
          //修复
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRepairStatus
              .dictionarytypecode
          ) {
            this.repairStatusEnum.push(dict)
          }
          //复测
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeRetestStatus
              .dictionarytypecode
          ) {
            this.retestStatusEnum.push(dict)
          }
          //关闭
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeCloseStatus
              .dictionarytypecode
          ) {
            this.closeStatusEnum.push(dict)
          }
          //关闭
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictTypeData.holeDiscoveryMethod
              .dictionarytypecode
          ) {
            this.holeDiscoveryMethodEnum.push(dict)
          }

        });
      });
      listConfigEmailTemplate({}).then(res => {
        this.emailTemplate = res.data;
      });
      listToolBasic().then(res => {
        this.toolBasicEnum = res.data;
        this.toolBasicInfoEnum =  res.data.map(e => {
          return {
            label: e.tool_name,
            value: e.tool_basic_id,

          }
        })
      })
      pageUsers({pagenumber: 1,
        pagesize: 1000,queryAll: true}).then(res => {
        this.userList = res.data.records;
      });
    },

    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.pager.$el.offsetHeight;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this;
      window.onresize = () => {
        return (() => {
          that.setTableHeight();
          console
            .log
            // "窗口resize-----------------" + that.tableHeight
            ();
        })();
      };
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({data}) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },

    // 处理页码变化
    handlePageChange({currentPage, pageSize}) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },
    // 查询条件
    getQueryCondition() {
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
      quryCondition.selectType = '1';
      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      pageHoleReform(this.getQueryCondition())
        .then(response => {
          this.page.records = response.data.records;
          this.page.total = response.data.total;
          this.loading = false;
        })
        .catch(error => {
          this.loading = false;
        });
    },
    refreshTable() {
      this.loadTable();
    },
    //处理整改按钮
    handleRectification(value){
      this.rectificationReformModalShow = true;
      this.rectificationReformModalHoleReform = value;
      if (value.reform_staus != '0' && value.reform_staus != '2'){
        console.log('giao')
        this.modalDisable = true;
      }else {
        this.modalDisable = false;
      }
      this.$refs.holeRectificationModal.initData();
    },
    //处理下载文件
    handleDownloadFile(value){
      downloadFile(value);
    },
    reformDateLtChange(value){
      if (value.value) this.queryParams.reformDateLt = value.value + " 23:59:59";
    },
    reformDateGtChange(value){
      if (value.value) this.queryParams.reformDateGt = value.value + " 00:00:00";
    },
    showTooltipMethod({ type, column, row, items, _columnIndex }){
      const {property} = column;
      if (property === 'reform_staus'){
        if (row.reform_staus === '2'){
          return '退回原因：' + row.reform_staus_reson;
        }
      }
    }
  },
  computed: {
    ...mapGetters(["extraHeight"]),
    formatter: function (){
      return (value,list)=>{
        const item = list.find(item => item.value == value);
        return item ? item.label : "";
      }
    }
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      console.log("监听查询条件表单高度变化--" + val);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.pager.$el.offsetHeight -
        val;
    }
  }
};
</script>
<style scoped>

</style>
