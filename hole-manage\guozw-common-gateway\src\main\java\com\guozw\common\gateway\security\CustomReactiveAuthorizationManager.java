package com.guozw.common.gateway.security;

import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.core.constant.JwtConstant;
import com.guozw.common.core.util.RedisUtils;
import com.guozw.common.gateway.exception.CustomAccessDeniedException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * 鉴权管理器
 */
@Slf4j
@Component
public class CustomReactiveAuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    @SneakyThrows
    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext authorizationContext) {
        ServerWebExchange exchange = authorizationContext.getExchange();
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 从请求头中获取token
        String token = request.getHeaders().getFirst(JwtConstant.JWT_TOKEN_HEADER);
        if (StrUtil.isNotBlank(token)) {
            token = StrUtil.replace(token, JwtConstant.JWT_TOKEN_PREFIX, StrUtil.EMPTY);

            if (isvalid(token)) {
//            UserVO userDetailInfo = JacksonUtils.getInstance().readValue(token, UserVO.class);
                return Mono.just(new AuthorizationDecision(true));
            }

        }

        return Mono.error(new CustomAccessDeniedException(ErrorCodeEnum.TOKEN_EXPIRED));


//        return authentication.map(auth -> {
//            ServerWebExchange exchange = authorizationContext.getExchange();
//            ServerHttpRequest request = exchange.getRequest();
//
//            Collection<? extends GrantedAuthority> authorities = auth.getAuthorities();
//            for (GrantedAuthority authority : authorities) {
//                String authorityAuthority = authority.getAuthority();
//                String path = request.getURI().getPath();
//                if (antPathMatcher.match(authorityAuthority, path)) {
//                    log.info(String.format("用户请求API校验通过，GrantedAuthority:{%s}  Path:{%s} ", authorityAuthority, path));
//                    return new AuthorizationDecision(true);
//                }
//            }
//            return new AuthorizationDecision(false);
//        }).defaultIfEmpty(new AuthorizationDecision(false));
//

    }


    @Override
    public Mono<Void> verify(Mono<Authentication> authentication, AuthorizationContext object) {
        return ReactiveAuthorizationManager.super.verify(authentication, object);
    }

    /**
     * token是否有效
     * @param token
     * @return
     */
    private Boolean isvalid(String token) {
        // 如果redis中存在
        if (RedisUtils.hasKey(token)) {

            // 如果剩余过期时间小于指定值，则刷新token的过期时间
            Long expire = RedisUtils.getExpire(token, TimeUnit.SECONDS);
            if (expire < Long.parseLong(JwtConstant.JWT_TOKEN_REMAINING)) {
                RedisUtils.expire(token, Long.parseLong(JwtConstant.JWT_TOKEN_EXPIRATION), TimeUnit.SECONDS);
            }
            return true;
        }

        return false;
    }
}
