<template>
    <el-container>
        <el-main>
            <!-- region 工具栏 -->
            <vxe-toolbar
                ref="toolbar"
                :refresh="{ query: loadDepartmentList }"
                custom
            >
                <template v-slot:buttons>
                    <el-button-group>
                        <el-button
                            type="primary"
                            icon="fa fa-plus"
                            @click="handleDepartmentAdd"
                            v-btnpermission="'security:department:add'"
                        >
                            新增</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-edit"
                            @click="handleDepartmentModify"
                            v-btnpermission="'security:department:modify'"
                        >
                            修改</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-trash"
                            @click="handleDepartmentDelete"
                            v-btnpermission="'security:department:del'"
                        >
                            删除</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-list-alt"
                            @click="handleDepartmentLook"
                        >
                            查看</el-button
                        >
                    </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->

            <!-- region 表格-->
            <vxe-table
                id="departmentTable"
                ref="departmentTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                auto-resize
                resizable
                :custom-config="{ storage: true }"
                :data="departments"
                :checkbox-config="{
                    labelField: 'departmentname',
                    checkStrictly: true,
                    trigger: 'row'
                }"
                :tree-config="{ expandAll: true }"
            >
                <vxe-table-column
                    type="checkbox"
                    title="部门名称"
                    min-width="10%"
                    tree-node
                    align="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="departmentshortname"
                    title="部门简称"
                ></vxe-table-column>
                <vxe-table-column
                    field="departmentenname"
                    title="部门英文名称"
                ></vxe-table-column>
                <vxe-table-column field="departmenttypecode" title="类型类型">
                    <template v-slot="{ row }">
                        <el-tag
                            effect="dark"
                            size="mini"
                            v-if="row.departmenttypecode == 1"
                            >省级</el-tag
                        >
                        <el-tag
                            effect="dark"
                            type="success"
                            size="mini"
                            v-else-if="row.departmenttypecode == 2"
                            >市级</el-tag
                        >
                        <el-tag
                            effect="dark"
                            type="info"
                            size="mini"
                            v-else-if="row.departmenttypecode == 3"
                            >县级</el-tag
                        >
                    </template>
                </vxe-table-column>
                <vxe-table-column
                    field="departmentcontacter"
                    title="部门联系人"
                ></vxe-table-column>
                <vxe-table-column
                    field="departmentphone"
                    title="部门电话"
                ></vxe-table-column>
                <vxe-table-column
                    field="departmentnote"
                    title="部门描述"
                ></vxe-table-column>
                <vxe-table-column
                    field="createdate"
                    title="创建时间"
                ></vxe-table-column>
                <vxe-table-column
                    field="ordernumber"
                    title="排序"
                ></vxe-table-column>
            </vxe-table>
            <!-- endregion -->

            <!-- region 弹窗 部门新增|修改|查看-->
            <vxe-modal
                ref="departmentFormModal"
                height="99%"
                width="600"
                position="center"
                resize
                :title="departmentFormModalTitle"
                :showFooter="!disabled"
            >
                <vxe-form
                    ref="departmentForm"
                    title-align="right"
                    title-width="120"
                    :data="departmentInfo"
                    :rules="departmentFormRules"
                    prevent-submit
                >
                    <vxe-form-item
                        title="父部门名称"
                        field="departmentparentname"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentparentname"
                                placeholder=""
                                readonly
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门名称"
                        field="departmentname"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentname"
                                placeholder="请输入部门名称"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门类型"
                        field="departmenttypecode"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-select
                                v-model="departmentInfo.departmenttypecode"
                                placeholder="请输入部门类型"
                                clearable
                                :disabled="disabled"
                                @change="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            >
                                <vxe-option
                                    v-for="departmenttype in departmenttypes"
                                    :key="departmenttype.departmenttypecode"
                                    :value="departmenttype.departmenttypecode"
                                    :label="departmenttype.departmenttypename"
                                ></vxe-option>
                            </vxe-select>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门简称"
                        field="departmentshortname"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentshortname"
                                placeholder="请输入部门简称"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门英文名称"
                        field="departmentenname"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentenname"
                                placeholder="请输入部门英文名称"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门联系人"
                        field="departmentcontacter"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentcontacter"
                                placeholder="请输入部门联系人"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门联系电话"
                        field="departmentphone"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentphone"
                                placeholder="请输入部门联系电话"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门传真"
                        field="departmentfax"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentfax"
                                placeholder="请输入部门传真"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="部门描述"
                        field="departmentnote"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.departmentnote"
                                placeholder="请输入部门描述"
                                clearable
                                :disabled="disabled"
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>

                    <vxe-form-item
                        title="部门排序"
                        field="ordernumber"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="departmentInfo.ordernumber"
                                type="number"
                                placeholder="请输入部门排序"
                                clearable
                                @input="
                                    $refs.departmentForm.updateStatus(scope)
                                "
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                </vxe-form>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.departmentFormModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleDepartmentFormModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import {
    deleteDepartment,
    listDepartments,
    listDepartmentTypes,
    modifyDepartment2,
    oneDepartment,
    saveDepartment2,
    hasChildren
} from "@/api/security/department";

import { listToTreeList } from "@/utils/guozw-core";

export default {
    name: "index",
    data() {
        return {
            loading: true,
            departments: [],
            departmentTreeConfig: {
                id: "departmentcode",
                parentId: "departmentparentcode",
                children: "children"
            },
            departmentInfo: {
                departmentcode: null,
                departmentname: null,
                departmentparentcode: null,
                departmentparentname: null,
                departmentshortname: null,
                departmentenname: null,
                departmentcontacter: null,
                departmentphone: null,
                departmentfax: null,
                departmentnote: null,
                departmenttypecode: null,
                ordernumber: null
            },
            departmentFormRules: {
                departmentname: [
                    { required: true, message: "请输入部门名称" },
                    { min: 1, max: 30, message: "只能输入 1 到 30 个字符" }
                ],
                departmenttypecode: [
                    { required: false, message: "请输入部门类型" }
                ],
                departmentshortname: [
                    { required: false, message: "请输入部门简称" },
                    { min: 1, max: 30, message: "只能输入 1 到 30 个字符" }
                ],
                departmentenname: [
                    { required: false, message: "请输入部门英文名称" },
                    {
                        pattern: "^[A-Z]{1,30}$",
                        message: "只能输入 1 到 30 个大写英文字母"
                    }
                ],
                departmentcontacter: [
                    { required: false, message: "请输入部门联系人" },
                    { min: 1, max: 30, message: "只能输入 1 到 30 个字符" }
                ],
                departmentphone: [
                    { required: false, message: "请输入部门联系电话" },
                    {
                        pattern: "^((0\\d{2,3}-\\d{7,8})|(1[\\d]\\d{9}))$",
                        message: "只能输入固话|手机号码"
                    }
                ],
                departmentfax: [
                    { required: false, message: "请输入部门传真" },
                    {
                        pattern: "^(0\\d{2,3}-\\d{7,8})$",
                        message: "传真格式错误"
                    }
                ],
                ordernumber: [{ required: false, message: "请输入资源排序" }]
            },
            departmentFormModalTitle: null,
            disabled: false, // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
            departmenttypes: []
        };
    },

    created() {
        this.loadDepartmentList();
        this.loadDepartmentTypes();
    },
    methods: {
        // 加载部门列表
        loadDepartmentList() {
            this.loading = true;
            listDepartments()
                .then(response => {
                    this.departments = listToTreeList(
                        response.data,
                        this.departmentTreeConfig.id,
                        this.departmentTreeConfig.parentId
                    );
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        loadDepartmentTypes() {
            listDepartmentTypes().then(response => {
                this.departmenttypes = response.data;
            });
        },
        handleDepartmentAdd() {
            this.disabled = false;
            Object.keys(this.departmentInfo).forEach(
                key => (this.departmentInfo[key] = "")
            );

            // 获取表格选中的记录
            const checkedRecords = this.$refs.departmentTable.getCheckboxRecords();
            console.log('checkedRecords', checkedRecords)
            if (checkedRecords.length == 1) {
                this.departmentInfo.departmentparentcode =
                    checkedRecords[0].departmentcode;
                this.departmentInfo.departmentparentname =
                    checkedRecords[0].departmentname;
            } else if (checkedRecords.length > 1) {
                this.$XModal.message({
                    message: "请至多选择一条记录",
                    status: "warning"
                });
                return;
            } else if (checkedRecords.length == 0) {
                this.$XModal.message({
                    message: "请选择上级部门",
                    status: "warning"
                });
                return;
            }
            this.departmentFormModalTitle = "新增部门";
            this.$refs.departmentFormModal.open();
            this.$nextTick(() => {
                this.$refs.departmentForm.clearValidate();
            });
        },
        handleDepartmentModify() {
            this.disabled = false;
            // 获取表格选中的记录
            const checkedRecords = this.$refs.departmentTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                oneDepartment({
                    departmentcode: checkedRecords[0].departmentcode
                }).then(response => {
                    const departmentInfo = response.data;
                    Object.keys(this.departmentInfo).forEach(
                        key => (this.departmentInfo[key] = departmentInfo[key])
                    );
                    this.departmentFormModalTitle = "修改部门";
                    this.$refs.departmentFormModal.open();
                    this.$nextTick(() => {
                        this.$refs.departmentForm.clearValidate();
                    });
                });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        handleDepartmentDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.departmentTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                this.$XModal
                    .confirm({
                        message: "确定要删除吗？",
                        position: "center",
                        status: "warning"
                    })
                    .then(type => {
                        if (type === "confirm") {
                            // 有子部门不能删除
                            hasChildren({
                                departmentcode: checkedRecords[0].departmentcode
                            })
                                .then(response => {
                                    return response.data;
                                })
                                .then(response => {
                                    if (response) {
                                        this.$XModal.message({
                                            message:
                                                "该部门存在子部门，不能删除",
                                            status: "warning"
                                        });
                                    } else {
                                        deleteDepartment({
                                            departmentcode:
                                                checkedRecords[0].departmentcode
                                        }).then(response => {
                                            this.$XModal.message({
                                                message: "删除成功",
                                                status: "success"
                                            });
                                            this.loadDepartmentList();
                                        });
                                    }
                                });
                        }
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        handleDepartmentLook() {
            this.disabled = true;
            // 获取表格选中的记录
            const checkedRecords = this.$refs.departmentTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                oneDepartment({
                    departmentcode: checkedRecords[0].departmentcode
                }).then(response => {
                    this.departmentInfo = response.data;
                    this.departmentFormModalTitle = "查看部门";
                    this.$refs.departmentFormModal.open();
                    this.$nextTick(() => {
                        this.$refs.departmentForm.clearValidate();
                    });
                });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        handleDepartmentFormModalConfirm() {
            this.$refs.departmentForm
                .validate()
                .then(() => {
                    if (this.departmentInfo.departmentcode) {
                        // 修改部门
                        modifyDepartment2(this.departmentInfo).then(
                            response => {
                                this.$refs.departmentFormModal.close();
                                this.$XModal.message({
                                    message: "修改成功",
                                    status: "success"
                                });
                                this.loadDepartmentList();
                            }
                        );
                    } else {
                        // 新增部门
                        // 如果部门父编码为空 则该节点为顶级节点 默认顶级节点的父编码为0
                        if (!this.departmentInfo.departmentparentcode) {
                            this.departmentInfo.departmentparentcode = this.$guozwSettings.rootId;
                        }
                        saveDepartment2(this.departmentInfo).then(response => {
                            this.$refs.departmentFormModal.close();
                            this.$XModal.message({
                                message: "新增成功",
                                status: "success"
                            });
                            this.loadDepartmentList();
                        });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        }
    }
};
</script>

<style scoped></style>
