package com.guozw.common.core.constant;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 枚举常量
 */
public class CommonEnum {

    /**
     * 部门类型
     */
    @AllArgsConstructor
    @Getter
    public enum DepartmentType{
        PROVINCE("省级", "1"),
        CITY("市级", "2"),
        COUNTY("县级", "3");

        private String label;
        private String value;

        public static DepartmentType ofValue(String value) {
            for (DepartmentType item : DepartmentType.values()) {
                if (item.getValue().equals(value)) {
                    return item;
                }
            }
            return null;
        }
    }
    /**
     * 性别
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum Sex {
        MALE("男", "1"),
        FEMALE("女", "2");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.Sex item : CommonEnum.Sex.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }

    /**
     * 字典类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum DictionaryType {
        DEVICE_TYPE("设备类型", "1583638119824404482"),
        HOLE_TYPE("漏洞类别", "1592752586292015106"),
        SYSTEM_TYPE("系统类型", "1583662568103653378");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.DictionaryType item : CommonEnum.DictionaryType.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }

    /**
     * 资源类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum ResourceType {
        MENU("菜单", "1"),
        BUTTON("按钮", "2");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.ResourceType item : CommonEnum.ResourceType.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }

    /**
     * 任务调度类型
     */
    @AllArgsConstructor
    @Getter
    public enum TaskScheduleType {
        IMMEDIATE_EXECUTE("立刻执行", "1"),
        TIMING_EXECUTE("定时执行", "2"),
        EVERY_DAY_EXECUTE("每天执行", "3"),
        EVERY_WEEK_EXECUTE("每周执行", "4"),
        EVERY_MONTH_EXECUTE("每月执行", "5");

        private String label;
        private String value;

    }
    @AllArgsConstructor
    @Getter
    public enum QuartzJobState {
        NONE("不存在", "NONE"),
        NORMAL("启动", "NORMAL"),
        PAUSED("暂停", "PAUSED"),
        ERROR("错误", "ERROR"),
        BLOCKED("阻塞", "BLOCKED");

        private String label;
        private String value;

    }

    @AllArgsConstructor
    @Getter
    public enum TaskStatus {
        NOT_RUNNING("未运行", "0"),
        RUNNING("运行中", "1"),
        STOPPED("已停止", "2"),
        SUSPEND("已暂停", "3"),
        COMPLETED("已完成", "4"),
        FAILED("失败", "5");


        private String label;
        private String value;

    }

    /**
     * 资产状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum AssetStatus {
        UNDEFINED("未定义", "0"),
        ONLINE("在线", "1"),
        SLIENCE("沉默", "2");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.AssetStatus item : CommonEnum.AssetStatus.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }
    /**
     * 资产类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum RelationType {
        IP("IP资产", "1", TaskType.ASSET.getValue()),
        WEBSITE("网站资产", "2", TaskType.WEB.getValue());

        private String label;
        private String value;
        /**
         * 不同资产下发不同的任务类型
         */
        private String taskType;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.RelationType item : CommonEnum.RelationType.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }

        public static RelationType ofValue(String value) {
            for (RelationType item : RelationType.values()) {
                if (item.getValue().equals(value)) {
                    return item;
                }
            }
            return null;
        }
    }

    /**
     * 任务目标类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum TaskTargetType {
        GROUP("分组", "1"),
        ASSET("资产", "2");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.TaskTargetType item : CommonEnum.TaskTargetType.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    @AllArgsConstructor
    public enum TaskType{
        SYS("系统扫描", "0"),
        CRACK("弱口令扫描", "1"),
        ASSET("资产探测", "2"),
        WEB("WEB扫描", "3");
        private String label;
        private String value;
    }
    /**
     * 数据类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum DataType {
        SYS_CONTACT("联系人信息", "1"),
        NETWORKCARD("网卡信息", "2"),
        SERVICEPORT("服务端口信息", "3"),
        SOFTWEB("软件信息", "4");

        private String label;
        private String value;

        public static List<Dict> toList() {
            List<Dict> list = new ArrayList<>();
            for (CommonEnum.DataType item : CommonEnum.DataType.values()) {
                list.add(new Dict().set("label", item.getLabel()).set("value", item.getValue()));
            }
            return list;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum ToolMapping {
        SHENGBANG_ASSET_SCAN("1", "WEBRAYSCAN"),
        SHENGBANG_HOLE_SCAN("2", StrUtil.EMPTY),
        XRAY_SCAN("3", StrUtil.EMPTY),
        XRAY_SCAN_NANCHANG("4", StrUtil.EMPTY),
        XRAY_SCAN_GANZHOU("5", StrUtil.EMPTY),
        XRAY_SCAN_JIUJIANG("6", StrUtil.EMPTY),
        XRAY_SCAN_YICHUN("7", StrUtil.EMPTY),
        XRAY_SCAN_SHANGRAO("8", StrUtil.EMPTY),
        XRAY_SCAN_JIAN("9", StrUtil.EMPTY),
        XRAY_SCAN_FUZHOU("10", StrUtil.EMPTY),
        XRAY_SCAN_XINYU("11", StrUtil.EMPTY),
        XRAY_SCAN_YINTAN("12", StrUtil.EMPTY),
        XRAY_SCAN_PINGXIANG("13", StrUtil.EMPTY),
        XRAY_SCAN_JINGDEZHEN("14", StrUtil.EMPTY),
        FOEYE_SCAN("15", StrUtil.EMPTY),
        LVMENG_SCAN("16", StrUtil.EMPTY);

        private String tool_basic_id;
        private String syslog_keyword;


        public static ToolMapping ofSyslogKeyword(String syslog_keyword) {
            for (ToolMapping item : ToolMapping.values()) {
                if (item.getSyslog_keyword().equals(syslog_keyword)) {
                    return item;
                }
            }
            return null;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum FileType {

        EMAIL("邮件附件", "1"),
        HOLE_PLAN_BACK("漏洞处置/下发单附件","2"),
        REFORM("整改下发","2");

        private String label;
        private String value;
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum BoolEnum {

        YES("是", "1"),
        NO("否","0");

        private String label;
        private String value;
    }

    /**
     * 处置反馈单类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum PlanBackType{
        PLAN_REPAIR("处置修复", "0"),
        PLAN_TEST("处置验证", "1"),
        PLAN_RETEST("处置复测", "2"),
        BACK_REPAIR("回执修复", "3"),
        BACK_TEST("回执验证", "4"),
        BACK_RETEST("回执复测", "5"),
        BACK_CLOSE("回执关闭", "6"),
        WHITE("白名单","7"),
        FALSE("误报","8"),;
        private String label;
        private String value;

    }

    /**
     * 漏洞发现状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleDiscoveryStatusEnum{
        NEW("新增", "0"),
        REFIND("重现", "1");
        private String label;
        private String value;

    }
    /**
     * 漏洞验证状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleTestStatus{
        WAIT("待验证", "0"),
        SUCCESS("验证成功", "1"),
        FAIL("验证失败", "2");
        private String label;
        private String value;
    }
    /**
     * 漏洞复测状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleRetestStatus{
        WAIT("待复测", "0"),
        SUCCESS("复测通过", "1"),
        FAIL("复测失败", "2");
        private String label;
        private String value;
    }
    /**
     * 漏洞修复状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleRepairStatus{
        WAIT("待修复", "0"),
        WAIT_TIMEOUT("超期待修复", "1"),
        FAIL("无法修复", "2"),
        SUCCESS("修复完成", "3");
        private String label;
        private String value;
    }
    /**
     * 漏洞关闭状态
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleCloseStatus{
        SUCCESS("已修复", "0"),
        WAIT("缓解修复", "1");

        private String label;
        private String value;
    }
    /**
     * 漏洞类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleTypeBig{
        SYS("系统漏洞", "1"),
        WEB("WEB漏洞", "2"),
        CRACK("弱口令漏洞", "3");

        private String label;
        private String value;
    }
    /**
     * 整改下发 操作类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleReformOperationType{
        REFORM("下发", "0"),
        RECTIFICATION("整改", "1"),
        AUDIT("审核", "2");

        private String label;
        private String value;
    }
    /**
     * 整改下发 查询类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleReformSelectType{
        REFORM("下发", "0"),
        RECTIFICATION("整改", "1");

        private String label;
        private String value;
    }
    /**
     * 整改下发 查询类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleReformStatus{
        WAIT_BACK("待反馈", "0"),
        BACK("已反馈", "1"),
        RETURN("已退回", "2"),
        PASS("审核通过", "3");

        private String label;
        private String value;
    }

  /**
     * 漏洞级别
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum HoleLevel{
        INFO("信息", "0"),
        LOW("低危", "1"),
        MEDIUM("中危", "2"),
        HIGH("高危", "3"),
        SUPER("超危", "4"),
        UNDEFINED("未定义", "5");

        private String label;
        private String value;
    }

    /**
     * ip类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum IpVersion{
        IPV4("IPV4", "1"),
        IPV6("IPV6", "2"),
        DOMAIN("DOMAIN", "3");

        private String label;
        private String value;
    }
    /**
     * 调用taskBasicService的stop或start 标识来源
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @AllArgsConstructor
    @Getter
    public enum Source{
        FRONT_END("前端","0"),
        BACK_END("后端", "1");

        private String label;
        private String value;
    }


}

