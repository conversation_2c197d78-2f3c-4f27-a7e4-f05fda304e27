<template>
  <el-container>
    <el-main>
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <el-button
            type="primary"
            icon="fa fa-plus"
            @click="handleAdd"
          >
            新增
          </el-button
          >
          <el-button
            type="danger"
            icon="fa fa-trash"
            @click="handleBatchDelete"
          >
            删除
          </el-button
          >
        </template>
      </vxe-toolbar>
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="myTable"
          ref="myTable"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          auto-resize
          resizable
          height="auto"
          show-overflow
          :sort-config="{ trigger: 'cell', remote: true }"
          :custom-config="{ storage: true }"
          :data="page.records"
          :checkbox-config="{ trigger: 'row' }"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="template_name"
            title="模板名称"
            sortable
          ></vxe-table-column>
          <vxe-table-column
            field="template_theme"
            title="邮件主题"
          ></vxe-table-column>
          <vxe-table-column
            field="createdate"
            title="创建时间"
          >
          </vxe-table-column>
          <vxe-table-column
            field=""
            title="操作"
            fixed="right"
            width=""
          >
            <template v-slot="{ row }">
              <i
                class="el-icon-edit"
                style="font-size: 18px; color: #409EFF"
                @click="handleEdit(row)"
              ></i>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager
        ref="pager"
        :current-page="queryParams.pagenumber"
        :page-size="queryParams.pagesize"
        :total="page.total"
        @page-change="handlePageChange"
      >
      </vxe-pager>
      <email-template-edit-modal
        ref="emailEditModal"
        :modal-info="modalInfo"
        :form-default-data="formDefaultData"
        @refreshTable="loadTable"/>
    </el-main>
  </el-container>
</template>

<script>
import {pageConfigEmailTemplate, deleteBatchConfigEmailTemplate} from '@/api/config/email'
import emailTemplateEditModal from "@/views/holemanage/config/email/components/emailTemplateEditModal";
import {mapGetters} from "vuex";


export default {
  name: "emailTemplate",
  components: {emailTemplateEditModal},
  data() {
    return {
      page: {
        total: 0,
        records: []
      },
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
      },
      loading: false,
      tableHeight: 0, // 表格高度
      formDefaultData: {},
      modalInfo: {}
    }
  },
  created() {
    this.loadTable();
  },
  mounted() {
    this.setTableHeight();
    this.windowResize();
  },

  methods: {

    loadTable() {
      this.loading = true;
      pageConfigEmailTemplate(this.queryParams).then(res => {
        this.page.records = res.data.records;
        this.page.total = res.data.total;
      }).finally(e => {
        this.loading = false;
      })
    },
    // 处理页码变化
    handlePageChange({currentPage, pageSize}) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },
    // 设置表格的高度
    setTableHeight() {
      console.log("extraHeight=", this.extraHeight);
      console.log("window.innerHeight=", window.innerHeight);
      console.log("this.$refs.toolbar.$el.offsetHeight=", this.$refs.toolbar.$el.offsetHeight);
      console.log("this.$refs.pager.$el.offsetHeight=", this.$refs.pager.$el.offsetHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight - 54;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this;
      window.onresize = () => {
        return (() => {
          that.setTableHeight();
          console
            .log
            // "窗口resize-----------------" + that.tableHeight
            ();
        })();
      };
    },
    handleEdit(row) {
      this.$refs.emailEditModal.modalForm = JSON.parse(JSON.stringify(row));

      this.modalInfo = {
        title: "修改邮件模板",
        show: true
      };
    },
    handleAdd() {
      this.$refs.emailEditModal.modalForm = {};
      this.modalInfo = {
        title: "新增邮件模板",
        show: true
      };
    },
    handleBatchDelete() {
      // 获取表格选中的记录
      const checkedRecords = this.$refs.myTable.getCheckboxRecords();
      let queryCondition = null
      let emailTemplateIdList = []
      let content = ''
      if (!checkedRecords || checkedRecords.length == 0) {
        content = '确定要批量删除当前查询条件下的所有记录吗？'
        queryCondition = this.getQueryCondition()

      } else {
        content = '确定要批量删除当前选中的记录吗？'
        checkedRecords.forEach(item => {
          emailTemplateIdList.push(item.config_email_template_id)
        })
      }
      this.$XModal
        .confirm({
          message: content,
          position: "center",
          status: "warning"
        })
        .then(type => {
          if (type === "confirm") {
            deleteBatchConfigEmailTemplate(emailTemplateIdList).then(res => {
              this.$XModal.message({
                message: "批量删除成功",
                status: "success"
              });
              this.loadTable()
            })
          }
        });

    }
  },
  computed: {
    ...mapGetters(["extraHeight"])
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    }

  }
}
</script>

<style scoped>

</style>
