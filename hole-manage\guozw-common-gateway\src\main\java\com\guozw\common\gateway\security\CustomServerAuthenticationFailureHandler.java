package com.guozw.common.gateway.security;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.core.util.RedisUtils;
import com.guozw.common.gateway.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.server.WebFilterExchange;
import org.springframework.security.web.server.authentication.ServerAuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * 登录失败处理
 */
@Slf4j
@Component
public class CustomServerAuthenticationFailureHandler implements ServerAuthenticationFailureHandler {


    @Override
    public Mono<Void> onAuthenticationFailure(WebFilterExchange webFilterExchange, AuthenticationException exception) {
        log.error("登录失败【{}】", exception.getMessage());
        ServerHttpResponse response = webFilterExchange.getExchange().getResponse();
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        return webFilterExchange.getExchange().getFormData()
                .flatMap(formData -> {
                    String userName;
                    if (formData.containsKey(CommonConstant.USERNAME_PARAMETER)) {
                        userName = formData.getFirst(CommonConstant.USERNAME_PARAMETER);
                    } else {
                        return CommonUtils.writeErrorMessage(response, ErrorCodeEnum.USERNAME_PASSWORD_ERROR);
                    }

                    if (exception instanceof UsernameNotFoundException) {
                        return CommonUtils.writeErrorMessage(response, ErrorCodeEnum.USER_NOT_EXISTS);
                    } else if (exception instanceof BadCredentialsException) {
                        String message = addLoginTimes(userName);
                        return CommonUtils.writeErrorMessage(response, message);
                    } else if (exception instanceof LockedException) {
                        return CommonUtils.writeErrorMessage(response, ErrorCodeEnum.USER_LOCKED);
                    } else {
                        return CommonUtils.writeErrorMessage(response, exception.getMessage());
                    }
                });
    }

    private String addLoginTimes(String userName) {
        Integer loginTimes = (Integer) RedisUtils.get(userName + CommonConstant.REDIS_KEY);
        if (loginTimes == null || loginTimes < CommonConstant.LOGIN_TIMES) {
            Long remainingChances = RedisUtils.incrWithEx(userName + CommonConstant.REDIS_KEY, 1, CommonConstant.LOGIN_TIME_OUT_SECONDS);
            if (remainingChances >= CommonConstant.LOGIN_TIMES) {
                return ErrorCodeEnum.USERNAME_PASSWORD_ERROR.getMessage() + "。该账户已被冻结，请于5分钟后重试！";
            }
            return ErrorCodeEnum.USERNAME_PASSWORD_ERROR.getMessage() + "。你还有" + (CommonConstant.LOGIN_TIMES - remainingChances) + "次机会！";
        } else {
            Long expire = RedisUtils.getExpire(userName + CommonConstant.REDIS_KEY, TimeUnit.SECONDS);
            String s = DateUtil.formatBetween(expire, BetweenFormatter.Level.MINUTE);
            return ErrorCodeEnum.USERNAME_PASSWORD_ERROR.getMessage() + "。该账户已被冻结请于" + s + "后重试！";
        }
    }
}
