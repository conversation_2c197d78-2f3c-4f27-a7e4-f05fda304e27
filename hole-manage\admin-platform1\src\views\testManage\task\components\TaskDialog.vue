<template>
    <div
        id="topDiv"
        v-loading.fullscreen="loading"
        element-loading-text="拼命加载中"
    >
        <vxe-modal
            height="99%"
            width="800"
            position="center"
            resize
            :title="modalInfo.title"
            v-model="modalInfo.show"
            @close="handleModalClose()"
            @show="onModalShow"
            showFooter
        >
            <vxe-form
                ref="myForm"
                title-width="100"
                :data="modalForm"
                :rules="rules"
                title-align="right"
                prevent-submit
                span="12"
            >
                <div class="bigTitle">
                    <span>基本信息</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item field="task_name" title="任务名称" class="">
                    <template v-slot="scope">
                        <vxe-input
                            clearable
                            placeholder="任务名称"
                            v-model="modalForm.task_name"
                            @input="$refs.myForm.updateStatus(scope)"
                            :disabled="modalInfo.type == 'edit'"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item
                    title="漏洞复测"
                    field="task_repeat"
                    class="lastItem"
                >
                    <vxe-radio-group v-model="modalForm.task_repeat">
                        <vxe-radio label="0" content="否"></vxe-radio>
                        <vxe-radio label="1" content="是"></vxe-radio>
                    </vxe-radio-group>
                </vxe-form-item>
              <vxe-form-item
                title="任务类型"
                field="task_type"
              >
                <el-select
                  v-model="modalForm.task_type"
                  filterable
                  placeholder="任务类型"
                  style="width:100%"
                  @change="modalForm.tool_basic_id = ''"
                  :disabled="modalInfo.type == 'edit'"
                >
                  <el-option
                    :key="'0'"
                    :label="'漏洞扫描'"
                    :value="'0'"
                  >
                  </el-option>

                  <el-option
                    :key="'1'"
                    :label="'弱口令扫描'"
                    :value="'1'"
                  >
                  </el-option>
                  <el-option
                    :key="'2'"
                    :label="'资产扫描'"
                    :value="'2'"
                  >
                  </el-option>
                </el-select>

              </vxe-form-item>
              <vxe-form-item title="" field="" class="lastItem">
              </vxe-form-item>

                <vxe-form-item
                    title="共享用户"
                    field="userIdList"
                    class=""
                    v-if="false"
                >
                    <el-select
                        v-model="modalForm.userIdList"
                        clearable
                        filterable
                        multiple
                        placeholder="共享用户"
                        style="width:100%"
                    >
                        <el-option
                            v-for="item in userData"
                            :key="item.value"
                            :label="item.nickname"
                            :value="item.usercode"
                        >
                            <span style="float: left">{{ item.nickname }}</span>
                            <span
                                style="float: right; color: #8492a6; font-size: 13px;margin-left:10px"
                                >{{ item.departmentname }}</span
                            >
                        </el-option>
                    </el-select>
                </vxe-form-item>
                <vxe-form-item title="" field="" class="lastItem" v-if="false">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>检测目标</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item title="检测类型" field="testType" class="">
                    <el-select
                        v-model="modalForm.testType"
                        filterable
                        placeholder="检测类型"
                        style="width:100%"
                        :disabled="modalInfo.type == 'edit'"
                    >
                        <el-option
                            v-for="item in assetTypeEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>

                <vxe-form-item title="目标类型" field="targetType" class="">
                    <el-select
                        v-model="modalForm.targetType"
                        filterable
                        placeholder="目标类型"
                        style="width:100%"
                        :disabled="modalInfo.type == 'edit'"
                    >
                        <el-option
                            v-for="item in testTargetEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>

                <vxe-form-item
                    title="隶属关系"
                    field="asset_department_id"
                    v-if="targetTypeFlag"
                    class=""
                >
                    <el-select
                        v-model.trim="modalForm.asset_department_id"
                        placeholder="隶属关系"
                        clearable
                        filterable
                        style="width:100%"
                        :disabled="modalInfo.type == 'edit'"
                    >
                        <el-option value style="height: auto">
                            <el-tree
                                :data="subordinationEnum"
                                default-expand-all
                                :check-strictly="true"
                                :selectedNode="false"
                                :expand-on-click-node="false"
                                node-key="departmentcode"
                                ref="subordinationTree"
                                :props="defaultProps"
                                @node-click="handleSubordinationCheckChange"
                            ></el-tree>
                        </el-option>
                    </el-select>
                </vxe-form-item>
                <vxe-form-item
                    title=""
                    field=""
                    class="lastItem"
                    v-if="
                        modalForm.testType == 2 && modalForm.targetType == 2
                            ? true
                            : false
                    "
                >
                </vxe-form-item>

                <vxe-form-item
                    title="IP类型"
                    field="asset_ip_type"
                    v-if="testTypeFlag"
                    class=""
                >
                    <el-select
                        v-model="modalForm.asset_ip_type"
                        filterable
                        placeholder="IP类型"
                        style="width:100%"
                        :disabled="modalInfo.type == 'edit'"
                    >
                        <el-option
                            v-for="item in asset_ip_typeEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>

                <vxe-form-item
                    field="testTarget"
                    title="检测目标"
                    v-if="targetTypeFlag"
                    span="24"
                    class="lastItem"
                >
                    <vxe-textarea
                        v-model="modalForm.testTarget"
                        :placeholder="
                            modalForm.testType == 1
                                ? `格式：192.168.x.x 或 192.168.x.x/12 或 192.168.x.x-192.168.x.x 如果有多个，请使用英文逗号(,)分割`
                                : `请输入域名，如果有多个，请使用英文逗号(,)分割`
                        "
                        clearable
                        :disabled="modalInfo.type === 'edit'"
                    ></vxe-textarea>
                </vxe-form-item>

                <vxe-form-item
                    field="departmentids"
                    title="分组选项"
                    v-if="!targetTypeFlag"
                    class="  lastItem"
                >
                    <template v-slot="scope">
                        <el-select
                            popper-class="selectTreeWrap"
                            v-model="modalForm.departmentids"
                            placeholder="分组选项"
                            multiple
                            filterable
                            clearable
                            style="width:100%"
                            @clear="departmentidsSelectClear"
                            @change="$refs.myForm.updateStatus(scope)"
                            :disabled="modalInfo.type == 'edit'"
                        >
                            <el-option value style="height: auto">
                                <el-tree
                                    :data="subordinationEnum"
                                    show-checkbox
                                    default-expand-all
                                    node-key="departmentcode"
                                    ref="departmentidsTree"
                                    :props="defaultProps"
                                    :check-strictly="true"
                                    @check="handleDepartmentidsCheckChange"
                                ></el-tree>
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title=""
                    field=""
                    class="lastItem"
                    v-if="
                        modalForm.testType == 2 && modalForm.targetType == 1
                            ? true
                            : false
                    "
                >
                </vxe-form-item>

                <div class="bigTitle">
                    <span>工具配置</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item
                    title="任务执行工具"
                    field="tool_basic_id"
                    class=" lastItem"
                >
                    <template v-slot="scope">
                        <el-select
                            v-model="modalForm.tool_basic_id"
                            clearable
                            filterable
                            placeholder="任务执行工具"
                            style="width:100%"
                            @change="$refs.myForm.updateStatus(scope)"
                            :disabled="modalInfo.type == 'edit'"
                        >
                            <el-option
                                v-for="item in scanOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                              <span style="float: left">{{ item.label }}</span>
                              <span style="float: right;margin-left: 15px">运行中任务数:{{item.runCount}}&nbsp;/&nbsp;等待任务数:{{item.waitCount}}</span>
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="" field="" class="lastItem">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>任务计划</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item
                    field="task_plan_type"
                    title="任务计划类型"
                    class=""
                    span="24"
                >
                    <el-radio-group
                        v-model="modalForm.task_plan_type"
                        size="mini"
                    >
                        <el-radio-button label="1">立即执行</el-radio-button>
                        <el-radio-button label="2">定时执行</el-radio-button>
                        <el-radio-button label="3">每天执行</el-radio-button>
                        <el-radio-button label="4">每周执行</el-radio-button>
                        <el-radio-button label="5">每月执行</el-radio-button>
                    </el-radio-group>
                </vxe-form-item>

                <vxe-form-item
                    field="timingdate"
                    title="日期"
                    class=""
                    v-if="modalForm.task_plan_type == 2"
                >
                    <template v-slot="scope">
                        <vxe-input
                            type="date"
                            transfer
                            v-model="modalForm.timingdate"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item
                    field="dayofmonth"
                    title="每月第几天"
                    class=""
                    v-if="modalForm.task_plan_type == 5"
                >
                    <template v-slot="scope">
                        <el-select
                            v-model="modalForm.dayofmonth"
                            clearable
                            filterable
                            style="width:100%"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                            <el-option
                                v-for="item in dayList"
                                :key="item"
                                :label="item"
                                :value="item"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item
                    field="week"
                    title="星期几"
                    class=""
                    v-if="modalForm.task_plan_type == 4"
                >
                    <template v-slot="scope">
                        <el-select
                            v-model="modalForm.week"
                            clearable
                            filterable
                            style="width:100%"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                            <el-option
                                v-for="item in weekList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item
                    field="task_plan_start"
                    title="时间"
                    class=""
                    v-if="modalForm.task_plan_type != 1"
                >
                    <template v-slot="scope">
                        <vxe-input
                            type="time"
                            transfer
                            v-model="modalForm.task_plan_start"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
            </vxe-form>
            <template v-slot:footer>
                <el-button type @click="handleDialogCancel">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </template>
        </vxe-modal>
    </div>
</template>

<script>
import { cloneDeep } from "lodash-es";
import {
    addTaskBasic,
    modifyTaskBasic,
    oneTaskBasic,
    getTableData
} from "@/api/testManage";
import { clearProperty, copyProperty } from "@/utils/guozw-core.js";
export default {
    name: "TaskDialog",
    components: {},
    props: {
        assetTypeEnum: {
            type: Array,
            default: () => []
        },
        statusEnum: {
            type: Array,
            default: () => []
        },
        asset_ip_typeEnum: {
            type: Array,
            default: () => []
        },
        testScenarioEnum: {
            type: Array,
            default: () => []
        },
        testTargetEnum: {
            type: Array,
            default: () => []
        },
        subordinationEnum: {
            type: Array,
            default: () => []
        },
        userData: {
            type: Array,
            default: () => []
        },
        modalInfo: {
            type: Object,
            default: () => {}
        },
        formDefaultData: {
            type: Object,
            default: () => {}
        }
    },
    computed: {
        testTypeFlag() {
            return this.modalForm.testType == "1";
        },
        targetTypeFlag() {
            return this.modalForm.targetType == "2";
        },
        scanOptions() {
          if (this.modalForm.task_type == "0") {
            return this.taskNameEnums.filter(e => e.hole_scan_flag == '1');
          } else if(this.modalForm.task_type == "1"){
            return this.taskNameEnums.filter(e => e.password_scan_flag == '1');
          } else if (this.modalForm.task_type == "2") {
            return this.taskNameEnums.filter(e => e.asset_scan_flag == '1');
          }
          return [];
        }
    },
    data() {
        const that = this;
        return {
            loading: false,
            modalForm: {
                // 任务名称
                task_name: "",
                task_type: "0",
                // 是否漏洞复测
                task_repeat: "0",
                // 共享用户id列表
                userIdList: [],
                // 备注
                task_remark: "",
                // 检测类型
                testType: "1",
                // 目标类型
                targetType: "1",
                // ip类型
                asset_ip_type: "1",
                // 检测目标
                testTarget: "",
                //隶属关系
                asset_department_id: null,
                //分组选项
                departmentids: [],
                // 工具id
                tool_basic_id: "",
                // 任务计划类型
                task_plan_type: "2",
                // 任务启动时间
                task_plan_start: "",
                // 任务计划-定时执行日期 yyyy-mm-dd
                timingdate: "",
                // 任务计划 每月第几天
                dayofmonth: "",
                // 任务计划 每个星期几
                week: ""
            },
            dayList: [
                1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
                9,
                10,
                11,
                12,
                13,
                14,
                15,
                16,
                17,
                18,
                19,
                20,
                21,
                22,
                23,
                24,
                25,
                26,
                27,
                28,
                29,
                30,
                31
            ],
            weekList: [
                { label: "星期一", value: 1 },
                { label: "星期二", value: 2 },
                { label: "星期三", value: 3 },
                { label: "星期四", value: 4 },
                { label: "星期五", value: 5 },
                { label: "星期六", value: 6 },
                { label: "星期天", value: 7 }
            ],
            //   工具组配置枚举
            taskNameEnums: [],

            defaultProps: {
                children: "children",
                label: "departmentname"
            },

            rules: {
                task_name: [
                    { required: true, message: "必填字段" },
                    { min: 7, max: "50", message: "长度 7-50 个字符" }
                ],
                testType: [{ required: true, message: "必填字段" }],
                targetType: [{ required: true, message: "必填字段" }],
                asset_ip_type: [{ required: true, message: "必填字段" }],
                departmentids: [
                    { required: true, message: "必填字段" },
                    { validator: that.checkDepartmentids }
                ],
                tool_basic_id: [{ required: true, message: "必填字段" }],
                task_plan_type: [{ required: true, message: "必填字段" }],
                task_plan_start: [{ required: true, message: "必填字段" }],
                timingdate: [{ required: true, message: "必填字段" }],
                dayofmonth: [{ required: true, message: "必填字段" }],
                week: [{ required: true, message: "必填字段" }],
                // 隶属关系
                asset_department_id: [
                    { required: true, message: "必填字段" },
                    { validator: that.checkAssetDepartmentId }
                ]
            },

            cronPopover: false,
            // 编辑时 需要的id参数
            task_basic_id: ""
        };
    },
    watch: {
        "modalForm.departmentids": {
            handler(newValue, oldValue) {
                console.log("监听分组选项字段", newValue);
                if (newValue) {
                    // 清除校验信息
                    this.$nextTick(() => {
                        this.$refs.myForm.clearValidate("departmentids");
                    });
                }
            }
        },
        "modalInfo.show": {
            handler(n) {
                n && this.getTaskNameEnumList();
            }
        },
        "modalInfo.type": {
            handler(n) {}
        },

        // 切换检测类型 当为Ip地址时，设置IP类型默认值
        testTypeFlag(n) {
            if (n) this.modalForm.asset_ip_type = "1";
        }
    },
    created() {},
    mounted() {},
    methods: {
        // 校验分组选项
        checkDepartmentids({ itemValue }) {
            console.log("校验校验分组选项字段", itemValue);
            if (!itemValue || itemValue.length == 0) {
                return new Error("必填字段");
            }
        },
        // 校验隶属关系字段
        checkAssetDepartmentId({ itemValue }) {
            console.log("校验隶属关系字段", itemValue);
            if (!itemValue) {
                return new Error("必填字段");
            }
        },
        // 编辑状态时 调用接口回显数据
        onModalShow() {
            if (this.modalInfo.type != "edit") {
                return;
            }
            if (this.modalInfo.type == "add") {
                this.resetForm();
            }
            const row = this.modalInfo.data;
            this.task_basic_id = row.task_basic_id;
            oneTaskBasic({ task_basic_id: row.task_basic_id }).then(
                ({ code, data }) => {
                    this.modalForm.task_name = data.task_name;
                    this.modalForm.task_repeat = data.task_repeat;
                    if (data.taskUserList && data.taskUserList.length > 0) {
                        //共享用户
                        this.modalForm.userIdList = data.taskUserList.map(
                            i => i.usercode
                        );
                    }

                    this.modalForm.task_remark = data.task_remark;

                    // 检测类型
                    this.modalForm.testType = data.task_asset_type;
                    // 目标类型-
                    this.modalForm.targetType = data.task_target_type;
                    // 目标类型是资产
                    if (data.task_target_type === "2") {
                        // 检测类型是IP地址时
                        if (data.task_asset_type === "1") {
                            //   隶属关系
                            let item;
                            this.subordinationEnum.forEach(i => {
                                if (
                                    i.departmentcode ===
                                    data.taskTargetIp.asset_department_id
                                ) {
                                    item = i;
                                } else {
                                    i.children.forEach(child => {
                                        if (
                                            child.departmentcode ===
                                            data.taskTargetIp
                                                .asset_department_id
                                        ) {
                                            item = child;
                                        }
                                    });
                                }
                            });
                            this.modalForm.asset_department_id =
                                item.departmentname;
                            this.$nextTick(() => {
                                this.$refs.subordinationTree.setCheckedNodes([
                                    item
                                ]);
                            });

                            //   ip类型
                            this.modalForm.asset_ip_type =
                                data.taskTargetIp.asset_ip_type;
                            //   检测目标
                            this.modalForm.testTarget =
                                data.taskTargetIp.asset_ip_list;
                        }
                        // 检测类型是网站时
                        if (data.task_asset_type === "2") {
                            //   隶属关系
                            let item;
                            this.subordinationEnum.forEach(i => {
                                if (
                                    i.departmentcode ===
                                    data.taskTargetWebsite.asset_department_id
                                ) {
                                    item = i;
                                } else {
                                    i.children.forEach(child => {
                                        if (
                                            child.departmentcode ===
                                            data.taskTargetWebsite
                                                .asset_department_id
                                        ) {
                                            item = child;
                                        }
                                    });
                                }
                            });
                            this.modalForm.asset_department_id =
                                item.departmentname;
                            this.$nextTick(() => {
                                this.$refs.subordinationTree.setCheckedNodes([
                                    item
                                ]);
                            });

                            //   检测目标
                            this.modalForm.testTarget =
                                data.taskTargetWebsite.asset_website_list;
                        }
                    }
                    // 目标类型是分组
                    else if (data.task_target_type == "1") {
                        this.$refs.departmentidsTree.setCheckedKeys(
                            data.taskTargetGroup.departmentids.split(",")
                        );
                        const selectedNodeList = this.$refs.departmentidsTree.getCheckedNodes();
                        this.modalForm.departmentids = selectedNodeList.map(
                            item => item.departmentname
                        );
                    }

                    // 工具组配置
                    this.modalForm.tool_basic_id = data.tool_basic_id;
                    //   任务计划
                    this.modalForm.timingdate = data.taskPlan.timingdate;
                    this.modalForm.task_plan_start =
                        data.taskPlan.task_plan_start;
                    this.modalForm.dayofmonth = data.taskPlan.dayofmonth;
                    this.modalForm.week = data.taskPlan.week;
                    this.modalForm.task_plan_type =
                        data.taskPlan.task_plan_type;
                }
            );
        },

        // 获取工具组配置枚举
        getTaskNameEnumList() {
            getTableData({
                pagenumber: 1,
                pagesize: 1000
            }).then(({ code, data }) => {
                if (code === 20000) {
                    const arr = data.records.map(i => {
                        return {
                            label: i.tool_name,
                            value: i.tool_basic_id,
                          asset_scan_flag: i.asset_scan_flag,
                          password_scan_flag: i.password_scan_flag,
                          hole_scan_flag: i.hole_scan_flag,
                          runCount: i.tool_run_count,
                          waitCount: i.tool_wait_count,
                          maxCount: i.tool_max_task,
                          haveCount: i.tool_all_count
                        };
                    });
                    this.taskNameEnums = arr;
                }
            });
        },
        handleSubordinationCheckChange(selectedNode) {
            console.log("当前选中的部门", selectedNode);
            this.modalForm.asset_department_id = selectedNode.departmentname;
        },
        // 分组选项
        departmentidsSelectClear() {
            this.modalForm.departmentids = [];
            this.$nextTick(() => {
                this.$refs.departmentidsTree.setCheckedNodes([]);
            });
        },
        handleDepartmentidsCheckChange({ departmentname }) {
            console.log("选中节点的部门名称", departmentname);
            let res = Array.from(
                this.$refs.departmentidsTree.getCheckedNodes()
            );
            console.log("当前选中的节点", res);

            this.modalForm.departmentids = res.map(i => i.departmentname);
        },
        resetForm() {
            this.departmentidsSelectClear();
            this.modalForm.task_repeat = "0";
            this.modalForm.testType = "1";
            this.modalForm.targetType = "1";
            this.modalForm.asset_ip_type = "1";
            this.modalForm.task_name = "";
            this.modalForm.tool_basic_id = "";
            this.modalForm.task_plan_type = "1";
            this.modalForm.task_plan_start = "";
            this.modalForm.timingdate = "";
            this.modalForm.dayofmonth = "";
            this.modalForm.week = "";
        },
        handleModalClose() {
            this.resetForm();
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        handleDialogCancel() {
            this.resetForm();
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        getQueryCondition_add() {
            let queryCondition = cloneDeep(this.modalForm);
            //   let queryCondition = JSON.parse(JSON.stringify(this.modalForm));
            const obj = {
                task_name: queryCondition.task_name,
                task_asset_type: queryCondition.testType,
                task_target_type: queryCondition.targetType,
                task_remark: queryCondition.task_remark,
                task_repeat: queryCondition.task_repeat,
                tool_basic_id: queryCondition.tool_basic_id,
                userIdList: queryCondition.userIdList,
                task_type: queryCondition.task_type,
                taskPlan: this.getQueryCondition_taskplan()
            };
            //   目标类型是资产
            if (queryCondition.targetType == "2") {
                // 检测类型是IP地址
                if (queryCondition.testType == "1") {
                    obj.taskTargetIp = {
                        asset_department_id: this.$refs.subordinationTree.getCurrentNode()
                            .departmentcode,
                        asset_ip_type: queryCondition.asset_ip_type,
                        asset_ip_list: queryCondition.testTarget
                    };
                    obj.taskTargetWebsite = {};
                    obj.taskTargetGroup = {};
                }

                //   检测类型是网站
                if (queryCondition.testType == "2") {
                    obj.taskTargetWebsite = {
                        asset_department_id: this.$refs.subordinationTree.getCurrentNode()
                            .departmentcode,
                        asset_website_list: queryCondition.testTarget
                    };
                    obj.taskTargetIp = {};
                    obj.taskTargetGroup = {};
                }
            }
            //   目标类型是分组
            if (queryCondition.targetType == "1") {
                const selectedNodeList = this.$refs.departmentidsTree.getCheckedNodes();
                obj.taskTargetGroup = {
                    departmentids: selectedNodeList
                        .map(item => item.departmentcode)
                        .join(","),
                    asset_ip_type: queryCondition.asset_ip_type
                };
            }

            return obj;
        },
        getQueryCondition_edit(task_basic_id) {
            let queryCondition = cloneDeep(this.modalForm);
            const obj = {
                task_basic_id: task_basic_id,
                task_repeat: queryCondition.task_repeat,
                userIdList: queryCondition.userIdList,
                taskPlan: this.getQueryCondition_taskplan()
            };
            return obj;
        },
        getQueryCondition_taskplan() {
            if (this.modalForm.task_plan_type == 1) {
                return {
                    task_plan_type: this.modalForm.task_plan_type
                };
            } else if (this.modalForm.task_plan_type == 2) {
                return {
                    task_plan_type: this.modalForm.task_plan_type,
                    timingdate: this.modalForm.timingdate,
                    task_plan_start: this.modalForm.task_plan_start
                };
            } else if (this.modalForm.task_plan_type == 3) {
                return {
                    task_plan_type: this.modalForm.task_plan_type,
                    task_plan_start: this.modalForm.task_plan_start
                };
            } else if (this.modalForm.task_plan_type == 4) {
                return {
                    task_plan_type: this.modalForm.task_plan_type,
                    week: this.modalForm.week,
                    task_plan_start: this.modalForm.task_plan_start
                };
            } else if (this.modalForm.task_plan_type == 5) {
                return {
                    task_plan_type: this.modalForm.task_plan_type,
                    dayofmonth: this.modalForm.dayofmonth,
                    task_plan_start: this.modalForm.task_plan_start
                };
            }
        },
        // 提交表单
        submitForm() {
            this.$refs.myForm
                .validate()
                .then(() => {
                    this.loading = true;
                    this.modalInfo.type == "add"
                        ? this.handelAdd()
                        : this.handelEdit(this.modalInfo.data.task_basic_id);
                })
                .catch(err => {
                    this.loading = false;
                    console.log(err);
                    return false;
                });
        },
        handelAdd() {
            const queryCondition = this.getQueryCondition_add();
            console.log("新增操作提交的表单数据 ", queryCondition);
            addTaskBasic(queryCondition)
                .then(res => {
                    if (res.data) {
                        this.$XModal.message({
                            message: "新增成功",
                            status: "success"
                        });
                        this.$emit("refreshTable");
                        this.resetForm();

                      this.modalInfo.show = false;
                    } else {
                        this.$XModal.message({
                            message: `${res.message}`,
                            status: "warning"
                        });
                    }
                })
                .catch(e => console.log(e))
                .finally(() => {
                    this.loading = false;
                    this.modalForm = {
                      // 任务名称
                      task_name: "",
                      task_type: "0",
                      // 是否漏洞复测
                      task_repeat: "0",
                      // 共享用户id列表
                      userIdList: [],
                      // 备注
                      task_remark: "",
                      // 检测类型
                      testType: "1",
                      // 目标类型
                      targetType: "1",
                      // ip类型
                      asset_ip_type: "1",
                      // 检测目标
                      testTarget: "",
                      //隶属关系
                      asset_department_id: null,
                      //分组选项
                      departmentids: [],
                      // 工具id
                      tool_basic_id: "",
                      // 任务计划类型
                      task_plan_type: "2",
                      // 任务启动时间
                      task_plan_start: "",
                      // 任务计划-定时执行日期 yyyy-mm-dd
                      timingdate: "",
                      // 任务计划 每月第几天
                      dayofmonth: "",
                      // 任务计划 每个星期几
                      week: ""
                    }
                });
        },
        handelEdit(task_basic_id) {
            const queryCondition = this.getQueryCondition_edit(task_basic_id);
            modifyTaskBasic(queryCondition)
                .then(({ data, msg }) => {
                    if (data) {
                        this.$XModal.message({
                            message: "修改成功",
                            status: "success"
                        });
                        this.$emit("refreshTable");
                    } else {
                        this.$XModal.message({
                            message: msg,
                            status: "warning"
                        });
                    }
                })
                .finally(() => {
                    this.loading = false;
                  this.modalForm = {
                    // 任务名称
                    task_name: "",
                    task_type: "0",
                    // 是否漏洞复测
                    task_repeat: "0",
                    // 共享用户id列表
                    userIdList: [],
                    // 备注
                    task_remark: "",
                    // 检测类型
                    testType: "1",
                    // 目标类型
                    targetType: "1",
                    // ip类型
                    asset_ip_type: "1",
                    // 检测目标
                    testTarget: "",
                    //隶属关系
                    asset_department_id: null,
                    //分组选项
                    departmentids: [],
                    // 工具id
                    tool_basic_id: "",
                    // 任务计划类型
                    task_plan_type: "2",
                    // 任务启动时间
                    task_plan_start: "",
                    // 任务计划-定时执行日期 yyyy-mm-dd
                    timingdate: "",
                    // 任务计划 每月第几天
                    dayofmonth: "",
                    // 任务计划 每个星期几
                    week: ""
                  }
                    this.resetForm();
                });
        }
    }
};
</script>

<style lang="scss" scoped>
#topDiv {
    .vxe-form /deep/ .vxe-form--item-inner {
        min-height: 30px !important;
    }
    .bigTitle {
        margin-top: 10px;
        span {
            font-size: 15px;
            font-weight: bolder;
        }
    }

    .lastItem {
        margin-bottom: 25px !important;
    }
    .el-divider--horizontal {
        margin: 5px 0;
    }
    .tip {
        color: red;
        font-size: 10px;
    }

    .my-dropdown1 {
        height: 200px;
        overflow: auto;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
    }
    .list-item1:hover {
        background-color: #f5f7fa;
    }
    .addRow {
        float: right;
        position: relative;
        top: -5px;
    }
    .delRow {
        float: right;
        margin-left: 5px;
        position: relative;
        top: -5px;
    }
}
</style>
