<template>
  <el-container>
    <el-main>
      <el-tabs
      ref="tabs"
      v-model="activeName"
      @tab-click="tabChange"
      >
      <el-tab-pane label="基础配置" name="0"><email-properties></email-properties></el-tab-pane>
      <el-tab-pane label="发件模板配置" name="1"><email-template ref="emailTemplate"></email-template></el-tab-pane>

      </el-tabs>
    </el-main>
  </el-container>

</template>

<script>
import emailProperties from "@/views/holemanage/config/email/components/emailProperties";
import emailTemplate from "@/views/holemanage/config/email/components/emailTemplate";
export default {
  name: "index",
  components:{emailProperties,emailTemplate},
  data(){
    return{
      activeName:'',
    }
  },
  created() {

  },
  methods:{
    tabChange(tab,event) {
      if (tab.index === '1'){
        setTimeout( () =>{
          this.$refs.emailTemplate.setTableHeight();

        },500)
      }
    },
  }
}
</script>

<style scoped>

</style>
