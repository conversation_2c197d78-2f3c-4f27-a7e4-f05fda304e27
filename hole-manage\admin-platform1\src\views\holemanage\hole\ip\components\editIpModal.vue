<template>
  <vxe-modal
    width="50%"
    position="centers"
    height="99%"
    :title="modalInfo.title"
    :loading="loading"
    v-model="modalInfo.show"
    showFooter
    @close="handleModalClose">
    <vxe-form ref="myForm" title-width="120" :data="modalForm" :rules="rules" title-align="right" prevent-submit
              span="12">
      <vxe-form-item title="漏洞名称" field="hole_name">
        <vxe-input clearable :disabled="!show" placeholder="漏洞名称" v-model="modalForm.hole_name"/>
      </vxe-form-item>

      <vxe-form-item title="漏洞级别" field="hole_level">
        <vxe-select :disabled="!show" clearable placeholder="漏洞级别"
                    v-model="modalForm.hole_level" :options="holeLevelEnum"/>
      </vxe-form-item>
      <vxe-form-item title="漏洞类型" field="hole_type">
        <vxe-select :disabled="!show" clearable placeholder="漏洞类型"
                    v-model="modalForm.hole_type" :options="holeTypeEnum"/>
      </vxe-form-item>
      <vxe-form-item title="影响资产" field="asset_ip">
        <vxe-input clearable :disabled="!show" placeholder="输入单个IP" v-model="modalForm.asset_ip"/>
      </vxe-form-item>
      <vxe-form-item title="漏洞说明" field="hole_remark">
        <vxe-textarea v-model="modalForm.hole_remark" placeholder="漏洞说明"/>
      </vxe-form-item>
      <vxe-form-item title="修复建议" field="hole_repair">
        <vxe-textarea v-model="modalForm.hole_repair" placeholder="修复建议"/>
      </vxe-form-item>
      <vxe-form-item title="参考信息" field="hole_referenceinfo">
        <vxe-textarea v-model="modalForm.hole_referenceinfo" placeholder="参考信息"/>
      </vxe-form-item>
    </vxe-form>
    <template v-slot:footer>
      <el-button type="" @click="handleModalClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm"
      >确定
      </el-button
      >
    </template>
  </vxe-modal>
</template>

<script>
import {getAssetByIp, saveHoleIp} from "@/api/holemanage/asset";
import {clearProperty} from "@/utils/guozw-core";

export default {
  name: "editIpModal",
  data() {
    var validatorIp = ({itemValue}) => {
      return new Promise((r, j) => {
        console.log('itemValue', itemValue);
        if (itemValue === '') {
          j(new Error('请输入影响资产'))
        } else {
          let reg = /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/;
          let isIp = reg.test(itemValue);
          if (isIp) {
            r();
          } else {
            j(new Error('ip格式错误'));
          }
        }
        r();
      })
    };

    return {


      loading: false,
      modalForm: {
        hole_name: '',
        hole_level: '',
        hole_type: '',
        asset_ip_id: '',
        asset_ip: '',
        hole_remark: '',
        hole_repair: '',
        hole_referenceinfo: ''
      },
      rules: {
        hole_name: [{required: true, message: "必填字段"}],
        hole_level: [{required: true, message: "必填字段"}],
        hole_type: [{required: true, message: "必填字段"}],
        asset_ip: [{required: true, validator: validatorIp, trigger: 'blur'}],
      },
      show: false,

    }
  },
  props: {
    modalInfo: {
      type: Object,
      default: () => {
      }
    },
    holeLevelEnum: {
      type: Array,
      default: () => []
    },
    holeTypeEnum: {
      type: Array,
      default: () => []
    },
  },
  watch: {
    "modalInfo.type": {
      handler(n) {
        if (n === "edit") {
          this.modalForm = this.modalInfo.data;
          this.show = true;
        } else if (n === "view") {
          this.modalForm = this.modalInfo.data;
          this.show = false;
        } else {
          this.show = true;
        }
      }
    },
  },
  methods: {
    handleModalClose() {
      this.modalInfo.show = false;
      this.$refs.myForm.clearValidate();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
      clearProperty(this.modalForm);
    },
    submitForm() {

      this.$refs.myForm
        .validate()
        .then(() => {
          this.loading = true;
          let userInfo = this.$store.getters.userInfo;
          this.modalForm.hole_discover_department_id = userInfo.departmentcode;
          this.modalForm.hole_source = '1';
          console.log('this.modalForm',this.modalForm);
          saveHoleIp(this.modalForm).then(res => {
            if (res.data) {
              this.$XModal.message({
                message: this.modalForm.asset_ip_id
                  ? "修改成功"
                  : "新增成功",
                status: "success"
              });
              this.$emit("refreshTable");
              this.modalInfo.show = false;
              clearProperty(this.modalForm);
            } else {
              this.$XModal.message({
                message: res.message,
                status: "error"
              });
            }
          }).finally(() => {
            this.loading = false;

          })
        })
        .catch(err => {
          console.log(err);
          this.loading = false;
          return false;
        });
    },
  }
}
</script>

<style scoped>

</style>
