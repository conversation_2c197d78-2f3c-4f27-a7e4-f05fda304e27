import request from '@/utils/request'

export function page(data) {
  return request({
    url: '/holemanage/whiteHole/page',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function save(data) {
  return request({
    url: '/holemanage/whiteHole/save',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function batchDeleteWhiteHole(data) {
  return request({
    url: '/holemanage/whiteHole/batchDeleteWhiteHole',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function checkWhiteHole(data) {
  return request({
    url: '/holemanage/whiteHole/checkWhiteHole',
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
    params: data
  })
}
