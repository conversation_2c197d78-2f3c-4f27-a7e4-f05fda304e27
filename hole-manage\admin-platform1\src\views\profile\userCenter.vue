<template>
  <div class="app-container">
    <div v-if="user">
      <el-row :gutter="20">

        <el-col :span="6" :xs="24">
          <user-card :user="user" />
        </el-col>

        <el-col :span="18" :xs="24">
          <el-card>
            <el-tabs v-model="activeTab">
              <el-tab-pane label="用户信息" name="userInfo">
                <UserInfo />
              </el-tab-pane>
              <!-- <el-tab-pane label="Timeline" name="timeline">
                <timeline />
              </el-tab-pane>
              <el-tab-pane label="Account" name="account">
                <account :user="user" />
              </el-tab-pane> -->
            </el-tabs>
          </el-card>
        </el-col>

      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserCard from './components/UserCard'
import Activity from './components/Activity'
import Timeline from './components/Timeline'
import Account from './components/Account'
import UserInfo from './components/UserInfo'

export default {
  name: 'UserCenter',
  components: { UserCard, Activity, Timeline, Account,UserInfo },
  data() {
    return {
      user: {},
      activeTab: 'userInfo'
    }
  },
  computed: {
   
  },
  created() {
    this.getUser()
  },
  methods: {
    getUser() {
      this.user = this.$store.getters.userInfo
    }
  }
}
</script>
