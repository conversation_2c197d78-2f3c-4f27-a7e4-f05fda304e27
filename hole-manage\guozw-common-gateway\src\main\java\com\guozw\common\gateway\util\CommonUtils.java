package com.guozw.common.gateway.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.ErrorCodeEnum;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Optional;

public class CommonUtils {

    public static Mono<Void> responseWrite(ServerWebExchange exchange, BaseResult result) {
        return responseWrite(exchange, HttpStatus.OK.value(), result);
    }

    public static Mono<Void> responseWrite(ServerWebExchange exchange, int httpStatus, BaseResult result) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.valueOf(httpStatus));
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String body = JSONUtil.toJsonStr(result);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(CharsetUtil.CHARSET_UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    public static Mono<Void> writeErrorMessage(ServerHttpResponse response, ErrorCodeEnum errorCodeEnum) {
        String result = JSONUtil.toJsonStr(BaseResult.of(errorCodeEnum));
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        DataBuffer buffer = response.bufferFactory().wrap(result.getBytes(CharsetUtil.CHARSET_UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
    public static Mono<Void> writeErrorMessage(ServerHttpResponse response, String message) {
        String result = JSONUtil.toJsonStr(BaseResult.failed().setMessage(message));
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        DataBuffer buffer = response.bufferFactory().wrap(result.getBytes(CharsetUtil.CHARSET_UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
}
