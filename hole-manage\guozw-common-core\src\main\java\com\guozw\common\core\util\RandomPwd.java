package com.guozw.common.core.util;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 生成密码
 * 必须要包含大写字符、小写字符、数字、特殊符号，密码长度在 8-20 位
 */
@Slf4j
public class RandomPwd {

    private static final String lowStr = "abcdefghijklmnopqrstuvwxyz";
    private static final String specialStr = "!@#$%^&*?";
    private static final String numStr = "0123456789";

    // 随机获取字符串字符
    private static char getRandomChar(String str) {
        SecureRandom random = new SecureRandom();
        return str.charAt(random.nextInt(str.length()));
    }

    // 随机获取小写字符
    private static char getLowChar() {
        return getRandomChar(lowStr);
    }

    // 随机获取大写字符
    private static char getUpperChar() {
        return Character.toUpperCase(getLowChar());
    }

    // 随机获取数字字符
    private static char getNumChar() {
        return getRandomChar(numStr);
    }

    // 随机获取特殊字符
    private static char getSpecialChar() {
        return getRandomChar(specialStr);
    }

    //指定调用字符函数
    private static char getRandomChar(int funNum) {
        switch (funNum) {
            case 0:
                return getLowChar();
            case 1:
                return getUpperChar();
            case 2:
                return getNumChar();
            default:
                return getSpecialChar();
        }
    }

    /**
     * 指定长度，随机生成复杂密码
     * @param length 密码长度
     * @return
     */
    public static String getRandomPwd(int length) {
        if (length > 20 || length < 8) {
            log.info("长度不满足要求");
            return "";
        }
        List<Character> list = new ArrayList<>(length);
        list.add(getLowChar());
        list.add(getUpperChar());
        list.add(getNumChar());
        list.add(getSpecialChar());

        for (int i = 4; i < length; i++) {
            SecureRandom random = new SecureRandom();
            int funNum = random.nextInt(4);
            list.add(getRandomChar(funNum));
        }

        Collections.shuffle(list);   // 打乱排序
        StringBuilder stringBuilder = new StringBuilder(list.size());
        for (Character c : list) {
            stringBuilder.append(c);
        }

        return stringBuilder.toString();
    }

    /**
     * 随机获取8-20位的随机密码
     * @return
     */
    public static String getRandomPwd() {
        return getRandomPwd(RandomUtil.randomInt(8, 21));
    }

    // 测试函数入口
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            int num = 10;
            System.out.println(getRandomPwd(num));
        }
    }

}

