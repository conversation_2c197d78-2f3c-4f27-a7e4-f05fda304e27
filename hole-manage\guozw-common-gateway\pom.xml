<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.guozw.common</groupId>
		<artifactId>guozw-common-parent</artifactId>
		<version>1.0-SNAPSHOT</version>
		<relativePath>../guozw-common-parent/pom.xml</relativePath>
	</parent>
	<groupId>com.guozw.common</groupId>
	<artifactId>guozw-common-gateway-holemanage</artifactId>
	<version>${guozw-common-gateway.version}</version>
	<packaging>jar</packaging>
	<name>guozw-common-gateway</name>
	<description>Demo project for Spring Boot</description>

	<properties>
		<java.version>${jdk.version}</java.version>
		<project.build.sourceEncoding>${project.encoding}</project.build.sourceEncoding>
		<maven.compiler.source>${jdk.version}</maven.compiler.source>
		<maven.compiler.target>${jdk.version}</maven.compiler.target>
		<!--打包的时候跳过测试环节-->
		<skipTests>true</skipTests>
	</properties>


	<dependencies>

		<!--guozw-->
		<dependency>
			<groupId>com.guozw.common</groupId>
			<artifactId>guozw-common-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.guozw.common</groupId>
			<artifactId>guozw-common-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.guozw.facade</groupId>
			<artifactId>guozw-facade-security</artifactId>
		</dependency>


		<!-- springboot -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<!-- spingcloud -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-gateway</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<!--apache-->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!--alibaba-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>

		<!-- dubbo -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-dubbo</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-openfeign-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>


		<!-- 移除druid -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--log4j2-->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<!-- springboot redis 缓存 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!-- lettuce pool 缓存连接池 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<!-- hutool工具类-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>

		<!--图形验证码-->
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
		</dependency>

	</dependencies>

	<!--打成jar包并分离出lib依赖-->
	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<!-- 指定项目编译时的java版本和编码方式 -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<target>${jdk.version}</target>
					<source>${jdk.version}</source>
					<encoding>${project.encoding}</encoding>
					<compilerArguments>
						<extdirs>lib</extdirs>
						<verbose />
						<bootclasspath>${java.home}\lib\rt.jar;${java.home}\lib\jce.jar</bootclasspath>
					</compilerArguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>${maven-jar-plugin.version}</version>
				<configuration>
					<archive>
						<manifest>
							<mainClass>com.guozw.common.gateway.GatewayApplication</mainClass> <!-- 指定入口类 -->
							<addClasspath>true</addClasspath> <!-- 在jar的MF文件中生成classpath属性 -->
							<classpathPrefix>lib/</classpathPrefix> <!-- classpath前缀,即依赖jar包的路径 -->
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>${maven-dependency-plugin.version}</version>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<!-- 指定依赖包的输出路径，需与上方的classpathPrefix保持一致 -->
							<outputDirectory>${project.build.directory}/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
