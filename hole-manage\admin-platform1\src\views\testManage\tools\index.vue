<template>
  <el-container>
    <el-main>
      <!-- region 查询条件表单-->
      <vxe-form ref="queryConditionForm" title-width="100" title-align="right" span="6" :data="queryParams" @submit="handleQueryConditionFormSubmit" @toggle-collapse="handleQueryConditionFormToggleCollapse">
        <vxe-form-item field="tool_make" title="扫描工具品牌">
          <el-select v-model="queryParams.tool_make" clearable filterable placeholder="扫描工具品牌" style="width:100%" @change="loadTable">
            <el-option v-for="item in brandEnum" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="tool_name" title="扫描工具名称">
          <vxe-input clearable placeholder="扫描工具名称" v-model="queryParams.tool_name"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_address" title="扫描工具地址">
          <vxe-input clearable placeholder="扫描工具地址" v-model="queryParams.tool_address"></vxe-input>
        </vxe-form-item>
        <vxe-form-item align="left">
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <el-button type="primary" icon="fa fa-plus" @click="handleAdd"
              v-btnpermission="'tools:toolsdialog:add'">
            新增</el-button>
          <el-button
            type="primary"
            icon="fa fa-link"
            @click="handleDepartmentAssign"
            v-btnpermission="'tools:department:allot'"
          >
            分配部门</el-button>
        </template>

      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table id="myTable" ref="myTable" v-loading="loading" element-loading-text="拼命加载中" border auto-resize resizable height="auto" show-overflow :sort-config="{ trigger: 'cell', remote: true }" @sort-change="sortChange" :custom-config="{ storage: true }" :data="page.records" :checkbox-config="{ trigger: 'row' }">
          <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column>
          <vxe-table-column title="序号" type="seq" width="60" fixed="left"></vxe-table-column>
          <vxe-table-column field="tool_name" title="扫描工具名称"></vxe-table-column>
          <vxe-table-column field="tool_make" title="扫描工具品牌" :formatter="['formatSelect', brandEnum]"></vxe-table-column>
          <vxe-table-column field="tool_address" title="扫描工具地址"></vxe-table-column>
          <vxe-table-column field="tool_status" title="扫描工具状态">
            <template v-slot="{ row }">
              <el-tag v-if="row.tool_status !== null && row.tool_status" type="success" size="mini" effect="dark">在线</el-tag>
              <el-tag v-if="row.tool_status !== null &&row.tool_status === false" type="info" size="mini" effect="dark">离线</el-tag>
            </template>
          </vxe-table-column>
          <vxe-table-column field="tool_run_count" title="运行中任务数"></vxe-table-column>
          <vxe-table-column field="tool_wait_count" title="等待任务数"></vxe-table-column>
          <vxe-table-column field="tool_max_task" title="最大任务数"></vxe-table-column>
          <vxe-table-column field="tool_all_count" title="已存在任务数"></vxe-table-column>

          <vxe-table-column field title="操作" fixed="right" width>
            <template v-slot="{ row }">
              <i class="el-icon-edit" v-btnpermission="'tools:toolsdialog:edit'" style="font-size: 18px; color: #409eff" @click="handleEdit(row)"></i>
              <i class="el-icon-view" style="font-size: 18px; color: #409EFF" @click="handleView(row)"></i>
              <i class="el-icon-delete" v-btnpermission="'tools:toolsdialog:del'" style="font-size: 18px; color: #F56C6C" @click="handleDel(row)"></i>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager ref="pager" :current-page="queryParams.pagenumber" :page-size="queryParams.pagesize" :total="page.total" @page-change="handlePageChange"></vxe-pager>
      <!-- endregion -->
        <vxe-modal
          ref="departmentTreeModal"
          height="99%"
          width="300"
          position="center"
          resize
          title="分配部门"
          showFooter
        >
          <department-tree
            ref="departmentTree"
            show-checkbox
            :node-key="departmentTreeConfig.id"
            :department-tree-nodes="departmentTreeNodes2"
            :default-checked-keys="defaultCheckedDepartmentCodes"
            :isCheckOnlyOne="false"
            :showActionButton="true"
            :show-add-button="false"
            :show-edit-button="false"
            :show-del-button="false"
          ></department-tree>
          <template v-slot:footer>
            <vxe-button
              type="button"
              content="取消"
              @click="$refs.departmentTreeModal.close()"
            ></vxe-button>
            <vxe-button
              type="button"
              status="primary"
              content="确定"
              @click="handleDepartmentTreeModalConfirm"
            ></vxe-button>
          </template>
        </vxe-modal>
      <ToolsDialog :modalInfo="editModalInfo" :selectEnum="selectEnum" @refreshTable="loadTable" :brandEnum="brandEnum" :agreementEnum="agreementEnum"></ToolsDialog>
    </el-main>
  </el-container>
</template>

<script>
import { listDictionarys } from "@/api/security/dictionary";
import ToolsDialog from "./components/ToolsDialog.vue";
import { listDepartments } from "@/api/security/department";
import { mapGetters } from "vuex";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import { getTableData, deleteToolBasic, getToolStatus } from "@/api/testManage";
import {listToTreeList} from "@/utils/guozw-core";
import { saveToolBasic } from "@/api/testManage";

export default {
  name: "Tools",
  components: { ToolsDialog ,DepartmentTree},
  props: {},
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        val;
    }
  },
  computed: {
    ...mapGetters(["extraHeight"])
  },
  data() {
    return {
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      loading: false,
      showloading: false,
      page: {
        total: 0,
        records: []
      },
      departmentTreeConfig: {
        id: "departmentcode",
        parentId: "departmentparentcode",
        children: "children"
      },
      defaultCheckedDepartmentCodes: [], // 默认选中的部门编码
      departmentTreeNodes: [], // 左侧部门树节点
      departmentTreeNodes2: [], // modal中的部门树节点
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        // 资产状态  0-未定义  1-在线 2-沉默
        // asset_status: "1",
        tool_name: "",
        tool_make: "",
        tool_address: ""
      },

      // 扫描工具品牌枚举
      brandEnum: [],
      //选择显示枚举
      selectEnum: [],
      // 协议枚举
      agreementEnum: [],

      // 新增 修改 弹窗的表单默认数据
      formDefaultData: {},
      // 新增 修改 弹窗的显示关闭以及标题等信息
      editModalInfo: {
      },
    };
  },
  created() {
    this.initData();
    this.loadTable();
  },
  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
    this.windowResize();
  },
  methods: {
    initData() {
      // 加载字典
      listDictionarys().then(res => {
        this.loading = true;
        res.data.forEach(item => {
          const dict = {
            label: item.dictionaryname,
            value: item.dictionaryvalue
          };
          //  设备类型
          if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[7]
              .dictionarytypecode
          ) {
            this.brandEnum.push(dict);
          } else if (item.dictionarytypecode == this.$guozwSettings.dictionarytypelist[24].dictionarytypecode) {
            //工具品牌选择框显示类型
            const dictSelect = {
              label: item.dictionarynote,
              value: item.dictionaryvalue
            };
            this.selectEnum.push(dictSelect);
          } else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[8]
              .dictionarytypecode
          ) {
            this.agreementEnum.push(dict);
          }
        });
        if (this.showloading == true) {
          this.loading = false;
          console.log("字典关闭")
        }
        this.showloading = true;
      })
    },

    handleDepartmentTreeModalConfirm() {
      // 获取选中的节点
      const checkedNodes = this.$refs.departmentTree
        .getElTree()
        .getCheckedNodes();
      console.log(checkedNodes);

      if (checkedNodes && checkedNodes.length >= 1) {
        let departmentCodes = checkedNodes.map(e => { return e[this.departmentTreeConfig.id];})
        // 获取表格选中的记录
        const checkedRecords = this.$refs.myTable.getCheckboxRecords();
        let saveData = checkedRecords[0];
        console.log('checkedRecords', checkedRecords);
        console.log('saveData', saveData);
        saveData.toolShareDepartmentList = departmentCodes;
        saveData.passwordKey = saveData.tool_password;
        console.log('departmentCodes',departmentCodes);
         saveToolBasic(saveData).then(response => {
           this.$refs.departmentTreeModal.close();
           this.$XModal.message({
             message: "分配成功",
             status: "success"
           });
           this.loadTable();
         });
      } else {
        this.$XModal.message({
          message: "请选择部门",
          status: "warning"
        });
      }
    },
    // 处理分配部门按钮点击
    handleDepartmentAssign() {
      // 获取表格选中的记录
      const checkedRecords = this.$refs.myTable.getCheckboxRecords();
      if (checkedRecords && checkedRecords.length === 1) {
        this.$refs.departmentTreeModal.open();

        this.$nextTick(() => {
          this.$refs.departmentTree
            .getElTree()
            .setCheckedKeys(checkedRecords[0].toolShareDepartmentList);
        });
      } else {
        this.$XModal.message({
          message: "请选择一条记录",
          status: "warning"
        });
      }
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this;
      window.onresize = () => {
        return (() => {
          that.setTableHeight();
          console
            .log
            // "窗口resize-----------------" + that.tableHeight
            ();
        })();
      };
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },
    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },
    /* 字段排序 */
    sortChange(e) {
      const { field, order } = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },
    // 查询条件
    getQueryCondition() {
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      getTableData(this.getQueryCondition())
        .then(response => {
          this.page.records = response.data.records;
          this.page.total = response.data.total;
          return response.data.records;
        })
        .then(res => {
          const tool_addressArr = res
            .map(i => i.tool_address)
            .toString();
          const tool_portArr = res.map(i => i.tool_port).toString();
          this.loadTableItemStatus(
            {
              tool_address: tool_addressArr,
              tool_port: tool_portArr
            },
            res
          );
        })
        .catch(error => {
          this.loading = false;
        })
        .finally(() => {
          if (this.showloading == true) {
            this.loading = false;
          }
          this.showloading == true
        });
    },
    // 扫描工具状态
    loadTableItemStatus(itemReqObj, res) {
      this.loading = true;
      getToolStatus(itemReqObj)
        .then(({ code, data }) => {
          if (code === 20000) {
            res.forEach((i, k) => {
              i.tool_status = data[k];
            });
            // this.page.records = res;
            this.$refs.myTable.reloadData(res);
            // console.error(this.page.records)
          }
        })
        .finally(() => (this.loading = false));
    },
    // 处理新增按钮点击
    handleAdd() {
      //   this.formDefaultData = {};
      this.editModalInfo = {
        title: "新增扫描工具",
        show: true,
        type: "add",
        data: {}
      };
    },

    // 处理查看
    handleView(row) {
      // this.formDefaultData = row;
      this.editModalInfo = {
        title: "查看资产",
        show: true,
        isActionView: true,
        type: "view",
        data: row
      };
    },

    // 处理删除按钮点击
    handleDel(row) {
      this.$XModal
        .confirm({
          message: "确定要删除吗？",
          position: "center",
          status: "warning"
        })
        .then(type => {
          if (type === "confirm") {
            const toolBasicIdList = [row.tool_basic_id]
            deleteToolBasic({ toolBasicIdList }).then(
              ({ code, data }) => {
                this.$XModal.message({
                  message: "删除成功",
                  status: "success"
                });
                this.loadTable();
              }
            );
          }
        });
    },

    // 处理修改
    handleEdit(row) {
      //   this.formDefaultData = row;
      const rows = JSON.parse(JSON.stringify(row));
      this.editModalInfo = {
        title: "修改扫描工具箱",
        show: true,
        type: "edit",
        data: rows
      };
    }
  }
};
</script>

<style lang="scss" scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
