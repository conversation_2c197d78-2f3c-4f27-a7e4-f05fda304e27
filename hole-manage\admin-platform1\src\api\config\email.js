import request from "@/utils/request";

export function getConfigEmailOne() {
  return request({
    url: '/holemanage/config/getConfigEmailOne',
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}
export function saveConfigEmail(data) {
  return request({
    url: '/holemanage/config/saveConfigEmail',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function testEmail(data) {
  return request({
    url: '/holemanage/config/testEmail',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function saveConfigEmailTemplate(data) {
  return request({
    url: '/holemanage/config/saveConfigEmailTemplate',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function deleteBatchConfigEmailTemplate(data) {
  return request({
    url: '/holemanage/config/deleteBatchConfigEmailTemplate',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function pageConfigEmailTemplate(data) {
  return request({
    url: '/holemanage/config/pageConfigEmailTemplate',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: JSON.stringify(data)
  })
}
export function listConfigEmailTemplate(data) {
  return request({
    url: '/holemanage/config/listConfigEmailTemplate',
    method: 'post',
    headers: { 'Content-Type': "'multipart/form-data" },
    data: data
  })
}
