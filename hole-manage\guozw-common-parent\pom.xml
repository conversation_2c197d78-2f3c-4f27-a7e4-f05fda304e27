<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.guozw.common</groupId>
	<artifactId>guozw-common-parent</artifactId>
	<version>1.0-SNAPSHOT</version>
	<name>guozw-common-parent</name>
	<packaging>pom</packaging>
	<description>parent</description>

	<properties>
		<jdk.version>1.8</jdk.version>
		<project.encoding>UTF-8</project.encoding>
		<maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
		<maven-jar-plugin.version>3.1.0</maven-jar-plugin.version>
		<maven-dependency-plugin.version>3.1.1</maven-dependency-plugin.version>
		<maven-source-plugin.version>3.2.1</maven-source-plugin.version>

		<!-- guozw -->
		<guozw-common-core.version>1.0-SNAPSHOT</guozw-common-core.version>
		<guozw-common-config.version>1.0-SNAPSHOT</guozw-common-config.version>
		<guozw-common-gateway.version>1.0-SNAPSHOT</guozw-common-gateway.version>
		<guozw-common-api.version>1.0-SNAPSHOT</guozw-common-api.version>
		<guozw-facade-security.version>1.0-SNAPSHOT</guozw-facade-security.version>
		<guozw-provider-security.version>1.0-SNAPSHOT</guozw-provider-security.version>
		<guozw-facade-log.version>1.0-SNAPSHOT</guozw-facade-log.version>
		<guozw-provider-log.version>1.0-SNAPSHOT</guozw-provider-log.version>
	</properties>

	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>com.guozw.common</groupId>
				<artifactId>guozw-common-core</artifactId>
				<version>${guozw-common-core.version}</version>
			</dependency>
			<dependency>
				<groupId>com.guozw.common</groupId>
				<artifactId>guozw-common-config</artifactId>
				<version>${guozw-common-config.version}</version>
			</dependency>
			<dependency>
				<groupId>com.guozw.facade</groupId>
				<artifactId>guozw-facade-security</artifactId>
				<version>${guozw-facade-security.version}</version>
			</dependency>
			<dependency>
				<groupId>com.guozw.facade</groupId>
				<artifactId>guozw-facade-log</artifactId>
				<version>${guozw-facade-log.version}</version>
			</dependency>

			<!-- springboot -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>2.2.5.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- springcloud -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>Hoxton.SR4</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>2.2.6.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!--pdf-->
			<dependency>
				<groupId>com.itextpdf</groupId>
				<artifactId>itextpdf</artifactId>
				<version>5.5.13.1</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/com.itextpdf/itext-asian -->
			<dependency>
				<groupId>com.itextpdf</groupId>
				<artifactId>itext-asian</artifactId>
				<version>5.2.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/com.itextpdf.tool/xmlworker -->
			<dependency>
				<groupId>com.itextpdf.tool</groupId>
				<artifactId>xmlworker</artifactId>
				<version>5.5.13.1</version>
			</dependency>
			<!-- apache -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.10</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>3.1.0</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.4</version>
			</dependency>
			<!-- poi -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>4.1.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>4.1.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml-schemas</artifactId>
				<version>4.1.2</version>
			</dependency>

			<!-- alibaba -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>1.1.14</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>2.2.8</version>
			</dependency>

			<!-- jdbc -->
			<dependency>
				<groupId>com.oracle.ojdbc</groupId>
				<artifactId>ojdbc8</artifactId>
				<version>19.3.0.0</version>
			</dependency>

			<!-- mybatisplus -->
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>3.3.0</version>
			</dependency>

			<!-- log4j -->
			<dependency>
				<groupId>log4j</groupId>
				<artifactId>log4j</artifactId>
				<version>1.2.17</version>
			</dependency>

			<!--log4j2-->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-api</artifactId>
				<version>2.15.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>2.15.0</version>
			</dependency>

			<!-- jjwt -->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>0.9.1</version>
			</dependency>

			<!-- junit -->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.12</version>
			</dependency>

			<!-- 用于AES加解密 -->
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15on</artifactId>
				<version>1.64</version>
			</dependency>
			<!-- ESAPI XSS防御-->
			<dependency>
				<groupId>org.owasp.esapi</groupId>
				<artifactId>esapi</artifactId>
				<version>2.2.0.0</version>
			</dependency>
			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>1.13.1</version>
			</dependency>

			<!-- 数据库用户密码加密 -->
			<dependency>
				<groupId>com.github.ulisesbocchio</groupId>
				<artifactId>jasypt-spring-boot-starter</artifactId>
				<version>3.0.3</version>
			</dependency>

			<!-- hutool工具类-->
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>5.7.22</version>
			</dependency>

			<!-- freemarker 使用ftl模板导出word-->
			<dependency>
				<groupId>org.freemarker</groupId>
				<artifactId>freemarker</artifactId>
				<version>2.3.29</version>
			</dependency>

			<!--TTL 线程之间传递参数-->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>transmittable-thread-local</artifactId>
				<version>2.11.4</version>
			</dependency>

			<!--图形验证码-->
			<dependency>
				<groupId>com.github.penggle</groupId>
				<artifactId>kaptcha</artifactId>
				<version>2.3.2</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<debug>true</debug>
					<debuglevel>source,lines,vars</debuglevel>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>

		</plugins>
	</build>

</project>
