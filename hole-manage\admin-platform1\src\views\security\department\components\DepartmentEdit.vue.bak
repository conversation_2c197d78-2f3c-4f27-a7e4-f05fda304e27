<template>
    <div>
        <vxe-modal
            height="90%"
            width="60%"
            position="center"
            resize
            :title="modalInfo.title"
            v-model="modalInfo.show"
            @close="handleModalClose()"
            showFooter
        >
            <vxe-form
                ref="myForm"
                title-width="120"
                :data="modalForm"
                :rules="rules"
                title-align="right"
                prevent-submit
                span="12"
            >
                <div class="bigTitle">
                    <span>基本信息</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item title="分组名称" field="departmentname">
                    <template v-slot="scope">
                        <vxe-input
                            clearable
                            placeholder="分组名称"
                            v-model.trim="modalForm.departmentname"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                        </vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="单位名称" field="dwmc">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.dwmc"
                            placeholder="单位名称"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="部门名称" field="bmmc">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.bmmc"
                            placeholder="部门名称"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="科室名称" field="ksmc">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.ksmc"
                            placeholder="科室名称"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="备注信息"
                    field="bzxx"
                    class="lastItem"
                    :span="24"
                >
                    <template v-slot="scope">
                        <vxe-textarea
                            v-model.trim="modalForm.bzxx"
                            placeholder="备注信息"
                            maxlength="100"
                            show-word-count
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-textarea>
                    </template>
                </vxe-form-item>

                <div class="bigTitle">
                    <span>分组状态</span>
                    <el-divider content-position="left"></el-divider>
                </div>
                <vxe-form-item title="分组状态" field="fzzt">
                    <template v-slot="scope">
                        <vxe-select
                            v-model.trim="modalForm.fzzt"
                            placeholder="分组状态"
                            @input="$refs.myForm.updateStatus(scope)"
                            :options="[
                                { label: '有效', value: 1 },
                                { label: '无效', value: 0 }
                            ]"
                        ></vxe-select>
                    </template>
                </vxe-form-item>
                <vxe-form-item title="隶属关系" field="departmentparentcode" class="lastItem">
                    <template v-slot="scope">
                        <vxe-pulldown ref="xDown1" style="width: 100%">
                            <template v-slot>
                                <vxe-input
                                    v-model.trim="selectedDepartment.departmentname"
                                    placeholder="隶属关系"
                                    @input="$refs.myForm.updateStatus(scope)"
                                    @focus="focusEvent1"
                                ></vxe-input>
                            </template>
                            <template v-slot:dropdown>
                                <div class="my-dropdown1">
                                    <el-input
                                        placeholder="输入关键字进行过滤"
                                        v-model="filterText"
                                    ></el-input>
                                    <el-tree
										ref="departmentTree"
                                        class="filter-tree"
                                        :data="departmentTreeNodes"
                                        :props="defaultProps"
                                        default-expand-all
										@node-click="handleNodeClick"
                                        :filter-node-method="filterNode"
                                        
                                    >
                                    </el-tree>
                                </div>
                            </template>
                        </vxe-pulldown>
                    </template>
                </vxe-form-item>

                <div class="bigTitle">
                    <span>风险权重</span>
                    <el-divider content-position="left"></el-divider>
                </div>

                <vxe-form-item title="等级保护" field="djbh">
                    <template v-slot="scope">
                        <vxe-select
                            clearable
                            placeholder="等级保护"
                            readonly
                            v-model.trim="modalForm.djbh"
                            @input="$refs.myForm.updateStatus(scope)"
							:options="djbhEnum"
                        >
                        </vxe-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="重要程度" field="zycd">
                    <template v-slot="scope">
                        <vxe-select
                            v-model.trim="modalForm.zycd"
                            placeholder="重要程度"
                            @input="$refs.myForm.updateStatus(scope)"
							:options="zycdEnum"
                        ></vxe-select>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="涉密状态" field="smzt" >
                    <template v-slot="scope">
                        <vxe-select
                            v-model.trim="modalForm.smzt"
                            placeholder="涉密状态"
                            @input="$refs.myForm.updateStatus(scope)"
							:options="smztEnum"
                        ></vxe-select>
                    </template>
                </vxe-form-item>
 				<vxe-form-item title=""  class="lastItem">
                </vxe-form-item>

                <div class="bigTitle">
                    <span>联系信息</span>
                    <el-divider content-position="left"></el-divider>
					<el-button type="primary" icon="fa fa-plus" @click="handleAdd"></el-button>
                </div>

                <vxe-form-item title="联系人" field="departmentname">
                    <template v-slot="scope">
                        <vxe-input
                            clearable
                            placeholder="联系人"
							:maxlength="20"
                            v-model.trim="modalForm.departmentname"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                        </vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="电话" field="dwmc">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.dwmc"
                            placeholder="电话"
							type="number"
							:max="11"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="邮箱" field="bmmc">
                    <template v-slot="scope">
                        <vxe-input
                            v-model.trim="modalForm.bmmc"
                            placeholder="邮箱"
							:maxlength="30"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-input>
                    </template>
                </vxe-form-item>
            </vxe-form>
            <template v-slot:footer>
                <el-button type="" @click="handleDialogCancel">取消</el-button>
                <el-button type="primary" :loading="loading" @click="submitForm"
                    >确定</el-button
                >
            </template>
        </vxe-modal>
    </div>
</template>
<script>
import { listToTreeList } from "@/utils/guozw-core";
import { listDepartments, saveDepartment } from "@/api/security/department.js";
import { listDictionarys } from "@/api/security/dictionary.js";
import { clearProperty, copyProperty } from "@/utils/guozw-core.js";
export default {
    name: "DepartmentEdit",
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        },
        formDefaultData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            filterText: "",
			 departmentTreeConfig: {
                id: "departmentcode",
                parentId: "departmentparentcode",
                children: "children"
            },
            defaultProps: {
                children: "children",
                label: "departmentname"
            },
			// 部门树
            departmentTreeNodes: [],
			// 选中的部门
			selectedDepartment: {},
			// 等级保护 dictionarytypecode=1582319253844295681
			djbhEnum: [],
			// 重要程度 dictionarytypecode=1582319959779213313
			zycdEnum: [],
			// 涉密状态 dictionarytypecode=1582329732339761154
			smztEnum: [],
			// 联系信息列表
			lxxxList: [],
            modalForm: {
				departmentname: "afdsfsfds",
                departmentparentname: "123",
            },
            rules: {
                before_version: [
                    { required: true, message: "必填字段" },
                    { min: 1, max: 100, message: "长度最大为100字符" }
                ],
                after_version: [
                    { required: true, message: "必填字段" },
                    { min: 1, max: 100, message: "长度最大为100字符" }
                ],
                up_time: [{ required: true, message: "必填字段" }]
            }
        };
    },

    watch: {
        formDefaultData(newVal, oldVal) {
            
        },
		filterText(val) {
            this.$refs.departmentTree.filter(val);
        }
    },

    computed: {},
    created() {
		this.initData()
        this.loadDepartmentTree();
    },
    methods: {
        handleModalClose() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        handleDialogCancel() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        loadDepartmentTree() {
            listDepartments({ isenabled: 1 }).then(response => {
                const treeList = listToTreeList(
                    response.data,
                    this.departmentTreeConfig.id,
                    this.departmentTreeConfig.parentId
                );

                this.departmentTreeNodes = treeList;
            });
        },
		initData() {

				listDictionarys({dictionarytypecode:"1582319253844295681"}).then(res => {
					res.data.forEach(item => {
						this.djbhEnum.push({
							label: item.dictionaryname,
							value: item.dictionaryvalue
						})
					})
				})
				listDictionarys({dictionarytypecode:"1582319959779213313"}).then(res => {
					res.data.forEach(item => {
						this.zycdEnum.push({
							label: item.dictionaryname,
							value: item.dictionaryvalue
						})
					})
				})
				listDictionarys({dictionarytypecode:"1582329732339761154"}).then(res => {
					res.data.forEach(item => {
						this.smztEnum.push({
							label: item.dictionaryname,
							value: item.dictionaryvalue
						})
					})
				})
		},
        focusEvent1() {
            this.$refs.xDown1.showPanel();
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.departmentname.indexOf(value) !== -1;
        },
		// 处理节点点击
        handleNodeClick(data, node) {
           console.log("点击树节点", node)
		   this.selectedDepartment = node.data
		   this.modalForm.departmentparentname = node.data.departmentname
        },
        submitForm() {
            this.$refs.myForm
                .validate()
                .then(() => {
                    this.loading = true;
                    saveDepartment(this.modalForm)
                        .then(response => {
                            this.$XModal.message({
                                message: "升级成功",
                                status: "success"
                            });
                            this.$emit("refreshTable");
                            this.modalInfo.show = false;
                        })
                        .finally(() => {
                            this.loading = false;
                            clearProperty(this.modalForm);
                            this.modalInfo.show = false;
                        });
                })
                .catch(err => {
                    console.log(err);
                    return false;
                });
        }
    }
};
</script>
<style scoped>
.vxe-form /deep/ .vxe-form--item-inner {
    min-height: 36px !important;
}

.bigTitle span {
    font-size: 15px;
    font-weight: bolder;
}

.lastItem {
    margin-bottom: 36px;
}
.el-divider--horizontal {
    margin: 5px 0;
}
.tip {
    color: red;
    font-size: 10px;
}

.my-dropdown1 {
    height: 200px;
    overflow: auto;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
}
.list-item1:hover {
    background-color: #f5f7fa;
}
</style>
