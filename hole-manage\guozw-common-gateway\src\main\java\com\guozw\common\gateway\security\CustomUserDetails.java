package com.guozw.common.gateway.security;

import cn.hutool.core.collection.CollUtil;
import com.guozw.security.vo.UserVO;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 用户详细信息
 */
public class CustomUserDetails implements UserDetails {

    /**
     * 用户id
     */
    private String id;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 是否账户没有过期
     */
    private Boolean isaccountnonexpired;
    /**
     * 是否账户没有锁住
     */
    private Boolean isaccountnonlocked;
    /**
     * 是否凭证没有过期
     */
    private Boolean iscredentialsnonexpired;
    /**
     * 是否可用
     */
    private Boolean isenabled;
    /**
     * 权限集合（角色集合）
     */
    private Collection<? extends GrantedAuthority> authorities;


    public CustomUserDetails(UserVO vo) {
        this(vo, true, true, true, true);
    }

    public CustomUserDetails(UserVO vo, Boolean isaccountnonexpired, Boolean isaccountnonlocked, Boolean iscredentialsnonexpired, Boolean isenabled) {
        this.id = vo.getUsercode();
        this.username = vo.getUsername();
        this.password =vo.getUserpassword();
        this.isaccountnonexpired = isaccountnonexpired;
        this.isaccountnonlocked = isaccountnonlocked;
        this.iscredentialsnonexpired = iscredentialsnonexpired;
        this.isenabled = isenabled;
        if (CollUtil.isNotEmpty(vo.getRolecodes())) {
            this.authorities = vo.getRolecodes().stream().map(rolecode -> new SimpleGrantedAuthority(rolecode)).collect(Collectors.toList());

        }
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    @Override
    public String getUsername() {
        return this.username;
    }
    @Override
    public String getPassword() {
        return this.password;
    }
    @Override
    public boolean isAccountNonExpired() {
        return this.isaccountnonexpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return this.isaccountnonlocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return this.iscredentialsnonexpired;
    }

    @Override
    public boolean isEnabled() {
        return this.isenabled;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
