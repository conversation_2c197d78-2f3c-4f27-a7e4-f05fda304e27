package com.guozw.security.facade.holemanage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.CommonEnum;
import com.guozw.security.entity.holemanage.ApiTask;
import com.guozw.security.entity.holemanage.TaskBasic;
import com.guozw.security.vo.holemanage.*;

import java.util.List;
import java.util.Set;

public interface TaskBasicService extends IService<TaskBasic> {

    Page<TaskBasicVO> pageTaskBasic(TaskBasicVO vo);

    TaskBasicVO oneTaskBasic(TaskBasicVO vo);
    Page<ApiTaskVO> listApiTask(ApiTaskVO vo);

    BaseResult addTaskBasic(TaskBasicVO vo);

    boolean modifyTaskBasic(TaskBasicVO vo);

    boolean deleteTaskBasic(TaskBasicVO vo);

    boolean startTaskBasic(List<String> taskBasicIdList);
    boolean stopTaskBasic(List<String> taskBasicIdList);
    boolean stopTaskBasic(List<String> taskBasicIdList,CommonEnum.Source source);

    Page<TaskPlanDispatchVO> pageTaskPlanDispatch(TaskPlanDispatchVO vo);
    Page<ApiTaskDispatchVO> pageApiTaskDispatch(ApiTaskDispatchVO vo);

    /**
     * 保存ip漏洞
     * @param holeBasicIpVOList 同一个ip下的漏洞信息
     * @param repeatModel 是否复测模式
     * @return
     */
    boolean saveHoleBasicIp(List<HoleBasicIpVO> holeBasicIpVOList, String repeatModel);

    /**
     * 保存网站漏洞
     * @param holeBasicWebsiteVOList 同一个网站下的漏洞信息
     * @param repeatModel
     * @return
     */
    boolean saveHoleBasicWebsite(List<HoleBasicWebsiteVO> holeBasicWebsiteVOList, String repeatModel);

    /**
     * 更新未扫描到漏洞的IP的漏洞信息
     * @param ipRange 下发任务时传入的ip字符列列表 逗号分割
     * @param scannedIpList 扫描到漏洞的ip列表
     * @param repeatModel 下发任务时是否时复测模式
     * @return
     */
    boolean handleNotScannedIpList(String ipRange, Set<String> scannedIpList, String repeatModel, String toolBasicId, String holeTypeBig);
    boolean handleNotScannedWebsiteList(String ipRange, Set<String> scannedIpList, String repeatModel, String toolBasicId);

    /**
     * 更新ip资产状态
     * @param taskBasic
     * @param apiTask
     * @param assetSet
     */
    void updateAssetStatus(TaskBasic taskBasic, ApiTask apiTask, Set<String> assetSet);
}
