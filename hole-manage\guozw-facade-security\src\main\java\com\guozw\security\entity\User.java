package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 用户表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "users")
public class User extends BaseEntity<User> implements Serializable {


    private static final long serialVersionUID = 8376293040558682311L;
    /**
     * 用户编码
     */
    @TableId
    private String usercode;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 用户性别
     */
    private String usersex;
    /**
     * 用户密码
     */
    @JsonIgnore
    private String userpassword;
    /**
     * 用户昵称
     */
    private String nickname;
    private String userphone;
    private String useraddress;
    private String usermail;
    /**
     * 用户类型编码
     */
    private String usertypecode;

    /**
     * 用户设置信息
     */
    private String usersettinginfo;
    /**
     * 用户初始密码
     */
    private String userinitpassword;
    /**
     * 用户头像地址
     */
    private String useravatar;
    /**
     * 用户介绍
     */
    private String userintroduction;
    /**
     * 是否启用 0-否 1-是
     */
    private String isenabled;
    /**
     * 是否删除 0-否 1-是
     */
    private String isdelete;

    /**
     * 排序序号
     */
    private Integer ordernumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
