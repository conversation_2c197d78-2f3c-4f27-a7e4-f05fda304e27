package com.guozw.common.gateway.exception;

import com.guozw.common.core.constant.ErrorCodeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.access.AccessDeniedException;

public class CustomAccessDeniedException extends AccessDeniedException {

    @Getter
    @Setter
    private Integer code;

    public CustomAccessDeniedException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getMessage());
        this.code = errorCodeEnum.getCode();
    }

    public CustomAccessDeniedException(String msg) {
        super(msg);
    }

    public CustomAccessDeniedException(String msg, Throwable t) {
        super(msg, t);
    }
}
