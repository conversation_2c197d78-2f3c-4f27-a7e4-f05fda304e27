package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@Data
@TableName(value = "homepage",autoResultMap = true)
public class Homepage implements Serializable {
    @TableId
    private int id;
    //用户id
    private  String usercode;
    //ipv4资产数
    private  String assetIpCount;
    //在线资产数
    private  String assetIpOnLineCount;
    //有漏洞资产数
    private  String vulnerableAssetsCount;
    //漏洞数
    private  String leakCount;
    //已修复漏洞数
    private  String fixedLeakCount;
    //今日新增在线资产数
    private  String todayAssetIpOnLineCount;
    //今日新增有漏洞资产数
    private  String todayVulnerableAssetsCount;
    //今日新增漏洞数
    private  String todayLeakCount;
    //今日新增已修复漏洞数
    private  String todayFixedLeakCount;

    //资产排名
    private  String assetRanking;
    //资产新增趋势
    private  String assetAdditionTrend;
    //资产设备类型排名
    private  String deviceTypeRanking;
    //资产按操作系统排名
    private  String systemRanking;
    //服务排名
    private  String serviceRanking;
    //漏洞数量排名 5 IP
    private  String vulnerabilitiesRanking;
    //已修复漏洞分析
    private  String vulnerabilityAnalysis;
    //已修复漏洞类型分布
    private  String vulnerabilityType;
    //已修复漏洞排行
    private  String vulnerabilityRanking;
    //已修复修复率
    private  String repairRate;
    //已修复漏洞变化趋势
    private  String vulnerabilityTrend;
    //未修复漏洞分析
    private  String notVulnerabilityAnalysis;
    //未修复漏洞类型分布
    private  String notVulnerabilityType;
    //未修复漏洞排行
    private  String notVulnerabilityRanking;
    //未修复漏洞变化趋势
    private  String notVulnerabilityTrend;
    private Date createtime;
    private  Date updatetime;
    //部门级别（1：省级，0市，县级)
    private  String province;
}
