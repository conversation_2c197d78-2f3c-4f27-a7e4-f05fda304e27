package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资产管理-web资产
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "asset_website")
public class AssetWebsite extends BaseEntity<AssetWebsite> implements Serializable {
    private static final long serialVersionUID = 8135197271466308924L;

    @TableId
    private String asset_website_id;
    private String asset_website_name;
    private String asset_ipv4;
    private String asset_ipv6;
    private String asset_department_id;

    private String device_type;
    private String host_name;
    private String fqdn;
    private String webtype;
    private String technical;
    private String system_type;
    private String asset_status;
    private String remark;

    private String terminal_inip;
    private String terminal_outip;
    private String terminal_kernel;
    private String terminal_business;
    private String terminal_uptime;
    private String terminal_installtime;
    private String terminal_onlinetime;
    private String terminal_offlinetime;

    private String hardware_producer;
    private String hardware_equiptype;
    private String hardware_serial;
    private String hardware_uuid;
    private String hardware_ram;
    private String hardware_cpu;
    private String hardware_utilization;
    private String hardware_systemload;

    private String weight_protection;
    private String weight_importance;
    private String weight_secret;

    private String organ_unit;
    private String organ_department;
    private String organ_office;

    private String position_country;
    private String position_province;
    private String position_city;
    private String position_county;
    private String position_other;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
