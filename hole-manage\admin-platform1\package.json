{"name": "vue-element-admin", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@smallwei/avue": "^2.6.13", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "crypto-js": "^4.0.0", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^5.3.3", "electron": "^19.0.8", "element-ui": "2.13.0", "encryptlong": "^3.1.4", "file-saver": "2.0.1", "font-awesome": "^4.7.0", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsencrypt": "^3.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "lodash-es": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "^6.9.4", "screenfull": "4.2.0", "script-loader": "0.7.2", "showdown": "1.9.0", "sortablejs": "1.8.4", "tui-editor": "1.3.3", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-cron": "^1.0.9", "vue-drag-resize": "^1.5.4", "vue-puzzle-vcode": "^1.1.4", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "vxe-table": "2.9.24", "xe-utils": "2.7.13", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "copy-webpack-plugin": "^6.0.3", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "plop": "2.3.0", "runjs": "^4.3.2", "sass-loader": "^10.1.0", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}