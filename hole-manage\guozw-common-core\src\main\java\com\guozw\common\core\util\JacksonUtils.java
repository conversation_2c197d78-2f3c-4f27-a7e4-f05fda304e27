package com.guozw.common.core.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * Jackson工具类
 * <AUTHOR>
 */
@Slf4j
public class JacksonUtils {

    private static final ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();

        // 忽略在json字符串中存在，但是在java对象中不存在的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    private JacksonUtils(){}

    public static ObjectMapper getInstance() {
        return objectMapper;
    }


    @SneakyThrows
    public static String obj2json(Object obj) {
        return objectMapper.writeValueAsString(obj);
    }

}
