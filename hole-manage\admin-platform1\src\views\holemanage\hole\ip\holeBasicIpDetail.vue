<template>
  <el-container>
    <el-header height="auto" style="margin: 1px; display: inline-block;">
      <div style="height: 100%;width: 100%;display: inline-block;padding: 5px 0px">
        <div class="headerText" style=" ">
          <span>{{ formatterHoleLevel }}</span>
          <span>{{ holeBasicIpInfo.hole_name }}</span>
        </div>

        <div class="box" style="height: auto">
          <div class="boxHeader">
            &emsp;
            <span style="align-self:center"> 发现方式:{{ formatterHoleDiscoveryMethod }}</span>
            &emsp;
            &emsp;
            <span style="align-self:center">漏洞类型:{{ formatterHoleType }}</span>
          </div>
          <el-form label-position="right">
            <el-row :gutter="20">
              <el-col :span="6" :offset="3">
                <div class="grid-content bg-purple">
                  <el-form-item label="影响当前资产:">
                    <span>{{ holeBasicIpInfo.departmentname }}/{{ holeType === '1' ? holeBasicIpInfo.asset_ip : holeBasicIpInfo.asset_website_name  }}</span>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="6" :offset="6">
                <div class="grid-content bg-purple">
                  <div class="grid-content bg-purple">
                    <el-form-item label="首次发现时间:">
                      <span>{{ holeBasicIpInfo.createtime }}</span>
                    </el-form-item>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6" :offset="3">
                <div class="grid-content bg-purple">
                  <el-form-item label="最近发现时间:">
                    <span>{{ holeBasicIpInfo.updatetime }}</span>
                  </el-form-item>
                </div>
              </el-col>
              <!--                            <el-col :span="6" :offset="6">
                                              <div class="grid-content bg-purple">
                                                  <div class="grid-content bg-purple">
                                                      <el-form-item label="发现人:">
                                                          <span>admin</span>
                                                      </el-form-item>
                                                  </div>
                                              </div>
                                          </el-col>-->
            </el-row>
          </el-form>
        </div>
      </div>


    </el-header>
    <el-main style=" display: inline-block;padding: 0px 20px;height: auto">
      <div style="margin: 0px 50px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="基础信息" name="1">
            <basic-info-tab ref="basicInfoTab" :hole-basic-ip-info="holeBasicIpInfo"></basic-info-tab>
          </el-tab-pane>
<!--          <el-tab-pane label="生命周期" name="2">
            <life-cycle-tab ref="liftCycleTab" :time-data-list="holeBasicIpInfo.holePlanBackVOList"
                            :plan-back-type-enum="enumData.planBackTypeEnum"
                            :test-status-enum="enumData.testStatusEnum"
                            :retest-status-enum="enumData.retestStatusEnum"
                            :repair-status-enum="enumData.repairStatusEnum"
                            :close-status-enum="enumData.closeStatusEnum"></life-cycle-tab>
          </el-tab-pane>
          <el-tab-pane label="检查信息" name="3">
            <check-info-tab ref="checkInfoTab" :hole-basic-ip-info="holeBasicIpInfo" :hole-type="holeType"></check-info-tab>
          </el-tab-pane>-->
        </el-tabs>
      </div>


    </el-main>
  </el-container>
</template>

<script>
import basicInfoTab from "@/views/holemanage/hole/ip/components/basicIpDetailTabComponents/basicInfoTab";
import checkInfoTab from "@/views/holemanage/hole/ip/components/basicIpDetailTabComponents/checkInfoTab";
import lifeCycleTab from "@/views/holemanage/hole/ip/components/basicIpDetailTabComponents/lifeCycleTab";

export default {
  name: "holeBasicIpDetail",
  components: {basicInfoTab, checkInfoTab, lifeCycleTab},
  data() {
    return {
      activeName: '1',
      holeBasicIpInfo: {},
      enumData: {
        holeLevelEnum: [],
        holeTypeEnum: [],
        holeDiscoveryMethodEnum: [],
        testStatusEnum: [],
        retestStatusEnum: [],
        closeStatusEnum: [],
        repairStatusEnum: [],
        planBackTypeEnum:[]
      },
      holeType: ''

    }
  },

  created() {
    this.initData();
  },

  methods: {
    initData() {
      this.holeBasicIpInfo = this.$route.query.holeBasicInfo;
      this.enumData = this.$route.query.enumData;
      this.holeType = this.$route.query.holeType;
      console.log(this.enumData);


    },
  },
  computed: {
    formatterHoleLevel: function () {
      console.log('1', this);
      let levelEnum = this.enumData.holeLevelEnum;
      if (levelEnum && levelEnum.length > 0) {
        for (let levelEnumElement of levelEnum) {
          if (levelEnumElement.value === this.holeBasicIpInfo.hole_level) {
            return levelEnumElement.label;
          }
        }
      } else {
        return this.holeBasicIpInfo.hole_level;
      }
    },
    formatterHoleDiscoveryMethod: function () {
      console.log('1', this);
      let discoveryMethodEnum = this.enumData.holeDiscoveryMethodEnum;
      if (this.holeBasicIpInfo.hole_discovery_method) {
        if (discoveryMethodEnum && discoveryMethodEnum.length > 0) {
          for (let d of discoveryMethodEnum) {
            if (d.value === this.holeBasicIpInfo.hole_discovery_method) {
              return d.label;
            }
          }
        } else {
          return this.holeBasicIpInfo.hole_discovery_method;
        }
      } else {
        return '无';
      }
    },
    formatterHoleType: function () {
      console.log('1', this);
      let holeTypeEnum = this.enumData.holeTypeEnum;
      if (this.holeBasicIpInfo.hole_type) {
        if (holeTypeEnum && holeTypeEnum.length > 0) {
          for (let d of holeTypeEnum) {
            if (d.value === this.holeBasicIpInfo.hole_type) {
              return d.label;
            }
          }
        } else {
          return this.holeBasicIpInfo.hole_type;
        }
      } else {
        return '无';
      }
    },

  }
}
</script>

<style scoped>
.box {
  border: solid 0.2px #EBEEF5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 20px 50px 50px;
}

.headerText {
  margin-left: 50px;
  margin-top: 20px;
  font-weight: bold;
  font-size: 20px
}

.boxHeader {
  height: 50px;
  background: #DCDFE6;
  margin: 30px 30px;
  display: flex;
}
</style>
