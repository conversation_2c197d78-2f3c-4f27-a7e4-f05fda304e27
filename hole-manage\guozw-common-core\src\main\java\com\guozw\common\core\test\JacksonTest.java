package com.guozw.common.core.test;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guozw.common.core.util.CommonUtils;
import org.apache.tomcat.util.http.fileupload.FileUtils;
import org.junit.Test;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.io.*;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.core.util.XmlUtil.*;
import static cn.hutool.core.util.XmlUtil.elementText;

public class JacksonTest {

    public static void main(String[] args) throws IOException {

        File inputFile = new File("E:\\file\\download/755ed84d-f2b6-4e85-816c-6c0187abe1f4.xml");
        File outFile = new File("E:\\file\\download/aaa.xml");
//        CommonUtils.xmlTransformation(file);


        List<String> descriptList = new ArrayList<>();
        descriptList.add("name");
        descriptList.add("vuln-descript");
        CommonUtils.xmlUpload(inputFile, outFile, descriptList);
        //xml读取
//        Document document = readXML(outFile);
        BufferedInputStream bis = FileUtil.getInputStream("E:\\file\\download/aaa.xml");
        Document document = CommonUtils.readXml(bis);
        Element goalElement = getElementByXPath("//cnnvd", document);
        List<Element> elementList = getElements(goalElement, "entry");
        for (Element element : elementList) {
            String name1 = elementText(element, "name");
            System.out.println(name1);
        }
    }

    @Test
    public void sss() {
        File file = new File("E:\\file\\download/cf84deee-b36a-443b-b7ca-35bab88cf2f4.xml");
        File file2 = new File("E:\\file\\download/aaa.xml");
        String label = "vuln-descript";
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader bReader = null;
        OutputStreamWriter osw = null;
        FileOutputStream fos = null;
        BufferedWriter bWriter = null;
        try {
            String line;
            StringBuffer stringBuffer;
            fis = new FileInputStream(file);//定义输入文件
            fos = new FileOutputStream(file2);//定义输出文件
            isr = new InputStreamReader(fis, "UTF-8");//读取输入文件
            osw = new OutputStreamWriter(fos, "UTF-8");//写入输入文件
            bReader = new BufferedReader(isr);//读取缓冲区
            bWriter = new BufferedWriter(osw);//写入缓存区
            while ((line = bReader.readLine()) != null) { //按行读取数据
                Pattern pattern = Pattern.compile(".*?<" + label + ">.*?<.*?</" + label + ">.*?");
                Matcher matcher = pattern.matcher(line);
                StringBuffer chapterContentLine = new StringBuffer();

                if (matcher.find()) {

                    String descript = line.split("<" + label + ">")[1].split("</" + label + ">")[0];
                    String replace = descript.replace("<", "&lt;")
                            .replace(">", "&gt;");
//                    matcher.appendReplacement(chapterContentLine, descript.replace("<", "&lt;"));
                    chapterContentLine.append(replace);
                    chapterContentLine.insert(0, "<" + label + ">");
                    chapterContentLine.append("</" + label + ">");
//                    matcher.appendTail(chapterContentLine);
                    String group = chapterContentLine.toString();
                    bWriter.write(group);//将拼结果按行写入出入文件中
                    bWriter.newLine();
                } else {
                    bWriter.write(line);
                    bWriter.newLine();
                }

//                if(line.indexOf("OnLineBList &")>0 || line.indexOf("ReturnBList &")>0) {
//                    int pos = line.indexOf("<");
//                            line=new StringBuffer(line).delete(pos+1,pos+2+str.length()).toString();
//                    System.out.println(line);//输出拼接结果
//                    bWriter.write(line);//将拼结果按行写入出入文件中
//                    bWriter.newLine();
//                }else{
//                    bWriter.write(line);
//                    bWriter.newLine();
//                }
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
//            logger.error("找不到文件",e);
        } catch (IOException e) {
            throw new RuntimeException(e);
//            logger.error("读取文件失败",e);
        } finally {
            try {
                bReader.close();//关闭读取缓冲区
                isr.close();//关闭读取文件内容
                fis.close();//关闭读取文件
                bWriter.close();//关闭写入缓存区
                osw.close();//关闭写入文件内容
                fos.close();//关闭写入文件
            } catch (IOException e) {

                throw new RuntimeException(e);
//                logger.error("关闭文件流失败！",e);
            }
        }
        /*String resourceXml = "<vuln-descript>Firewall-1无法正确过滤脚本标签。远程攻击者通过在SCRIPT标签前包含额外的<绕过Strip Script Tags限制。</vuln-descript>";
        Pattern pattern = Pattern.compile("<vuln-descript>.*?<.*?</vuln-descript>");
        Matcher matcher = pattern.matcher(resourceXml);
        StringBuffer chapterContentLine = new StringBuffer();

        while (matcher.find()) {

            String key = matcher.group(0);
            String descript = resourceXml.split("<vuln-descript>")[1].split("</vuln-descript>")[0];

            matcher.appendReplacement(chapterContentLine,descript.replace("<","&lt;"));

        }
        chapterContentLine.insert(0,"<vuln-descript>");
        chapterContentLine.append("</vuln-descript>");
        matcher.appendTail(chapterContentLine);
        System.out.println(chapterContentLine);*/
    }

    @Test
    public void test() {
       /* String uploadPath = "E:\\file\\download";
        try {

            SysFile sysFile = JSON.parseObject(JSON.toJSONString(CommonUtils.upload(uploadPath, file)), SysFile.class);


//            File documentFile=new File(uploadPath+"/"+sysFile.getFile_storage_name());
//            Document document = CommonUtils.xmlTransformation(documentFile);
            File documentFile = new File(uploadPath + "/" + sysFile.getFile_storage_name());
            String FileName = UUID.randomUUID() + sysFile.getFile_suffix();
            String newFileName = uploadPath + "/" + FileName;
            boolean newFile = new File(newFileName).createNewFile();

            if (newFile) {
                List<String> descriptList = new ArrayList<>();
                descriptList.add("name");
                descriptList.add("vuln-descript");
                CommonUtils.xmlUpload(documentFile, new File(newFileName), descriptList);
                File xmlNewFile = new File(newFileName);
                //xml读取
                Document document = readXML(xmlNewFile);
                Element goalElement = getElementByXPath("//cnnvd", document);

//        Object bString = getByXPath("//cnnvd/entry/name", document, XPathConstants.STRING);
//        System.out.println("b元素节点值："+bString);
//        String name = goalElement.getAttribute("name");
//        System.out.println("a元素属性值："+name);
                List<HoleLibrary> holeLibraryList=new ArrayList<>();
                Map holeLibraryMap=new HashMap();
                //获取内容下所有对应标签
                List<Element> elementList = getElements(goalElement, "entry");
                for (Element element : elementList) {
                    Element other = getElement(element, "other-id");
                    String vuln=elementText(element, "vuln-id")+elementText(other,"cve-id");
                    if (holeLibraryMap.containsKey(vuln)){
                        continue;
                    }
                    holeLibraryMap.put(vuln,"vuln");
                    HoleLibrary holeLibrary = new HoleLibrary();
                    holeLibrary.setHole_library_name(elementText(element, "name"));
                    holeLibrary.setHole_library_cnnvd(elementText(element, "vuln-id"));
                    holeLibrary.setHole_library_public_time(DateUtils.parseDate(elementText(element, "published"),DateUtils.DATE_FORMAT_10));
                    holeLibrary.setModifydate(DateUtils.parseDate(elementText(element, "modified"),DateUtils.DATE_FORMAT_10));
//                    holeLibrary.set(elementText(element, "source"));
                    holeLibrary.setHole_library_level(elementText(element, "severity"));
                    holeLibrary.setHole_library_type(elementText(element, "vuln-type"));
                    holeLibrary.setHole_library_remark(elementText(element, "vuln-descript").getBytes("UTF8"));

                    holeLibrary.setHole_library_cve(elementText(other,"cve-id"));
//                    elementText(other,"bugtraq-id");
                    holeLibrary.setHole_library_id(IdUtil.getSnowflakeNextIdStr());
                    holeLibrary.setCreatedate(new Date());

                    holeLibraryList.add(holeLibrary);


                }
                if (holeLibraryList.size()!=0){
                    QueryWrapper<HoleLibrary> queryWrapper = new QueryWrapper<>();
                    queryWrapper.select("hole_library_cve");
                    queryWrapper.select("hole_library_cnnvd");
                    List<HoleLibrary> holeLibraries=this.list(queryWrapper);
                    Map hashMapKey = new HashMap<>();
                    if (holeLibraries.size()!=0){
                        for (HoleLibrary holeLibrary : holeLibraries) {
                            String vuln=holeLibrary.getHole_library_cnnvd()+holeLibrary.getHole_library_cve();
                            hashMapKey.put(vuln,"vuln");
                        }
                    }
                    List<List<HoleLibrary>> parts = Lists.partition(holeLibraryList, 500);
                    for (List<HoleLibrary> part : parts) {
                        for (int i = 0; i < part.size(); i++) {
                            HoleLibrary holeLibrary = part.get(i);
                            String vuln=holeLibrary.getHole_library_cnnvd()+holeLibrary.getHole_library_cve();
                            if (hashMapKey.containsKey(vuln)){
                                part.remove(holeLibrary);
                            }
                        }
                        this.saveBatch(part);
                    }
//                    QueryWrapper<HoleLibrary> objectQueryWrapper = new QueryWrapper<>();
//                    objectQueryWrapper.eq("hole_library_cve",holeLibrary.getHole_library_cve());
//                    objectQueryWrapper.eq("hole_library_cnnvd",holeLibrary.getHole_library_cnnvd());
//                    HoleLibrary one = holeLibraryMapper.selectOne(objectQueryWrapper);
//                    HoleLibrary one = lambdaQuery()
//                            .eq(HoleLibrary::getHole_library_cve, vo.getHole_library_cve())
//                            .eq(HoleLibrary::getHole_library_cnnvd, vo.getHole_library_cnnvd())
//                            .one();
//                    if (one==null){
//                        this.save(holeLibrary);
//                    }
                }
                documentFile.delete();
                sysFile.setFile_storage_name(FileName);
                sysFileService.save(sysFile);
            }


        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return null;*/

    }


}
