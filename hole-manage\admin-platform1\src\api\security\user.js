import request from '@/utils/request'
import qs from 'qs'

export function login2(data) {
  return request({
    url: `/auth/login?code=${data.code}&random_code=${data.random_code}`,
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getBackendPublickey(data) {
  return request({
    url: '/security/user/getBackendPublickey',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function getAesKey(data) {
  return request({
    url: '/security/user/getAesKey',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function logout2(token) {
  return request({
    url: '/security/user/logout',
    method: 'post',
    data: qs.stringify({ token })
  })
}

export function getUserDetailInfo() {
  return request({
    url: '/security/user/getUserDetailInfo',
    method: 'post'
  })
}
export function oneUser(data) {
  return request({
    url: '/security/user/oneUser',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function pageUsers(data) {
  return request({
    url: '/security/user/pageUsers',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function userExists(data) {
  return request({
    url: '/security/user/userExists',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function saveUser(data) {
  return request({
    url: '/security/user/saveUser',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function modifyUser(data) {
  return request({
    url: '/security/user/modifyUser',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function modifyUser2(data) {
  return request({
    url: '/security/user/modifyUser2',
    method: 'post',
    params: data
  })
}
export function resetPassword(data) {
  return request({
    url: '/security/user/resetPassword',
    method: 'post',
    params: data
  })
}
export function enabledUser(data) {
  return request({
    url: '/security/user/enabledUser',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function deleteUser(data) {
  return request({
    url: '/security/user/deleteUser',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function saveUserDepartmentRelation(data) {
  return request({
    url: '/security/user/saveUserDepartmentRelation',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function saveUserRoleRelation(data) {
  return request({
    url: '/security/user/saveUserRoleRelation',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function modifyPassword(data) {
  return request({
    url: '/security/user/modifyPassword',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function listUser() {
  return request({
    url: '/security/user/listUser',
    headers: { 'Content-Type': 'application/json' },
    method: 'post'
  })
}

export function listUsers() {
  return request({
    url: '/security/user/listUserVO',
    headers: { 'Content-Type': 'application/json' },
    method: 'get'
  })
}
