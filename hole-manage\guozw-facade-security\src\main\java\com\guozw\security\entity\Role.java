package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 角色表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "role")
public class Role extends BaseEntity<Role> implements Serializable {


    private static final long serialVersionUID = -3792233780045955092L;
    /**
     * 角色编码
     */
    @TableId
    private String rolecode;
    /**
     * 角色名称
     */
    private String rolename;
    /**
     * 角色描述
     */
    private String rolenote;
    /**
     * 是否删除 0-否 1-是
     */
    private Integer isdelete;

    /**
     * 排序序号
     */
    private Integer ordernumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
