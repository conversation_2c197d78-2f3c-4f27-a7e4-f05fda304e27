<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo && menuStyle !== 'hybrid'" :collapse="isCollapse" />

    <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="sidebarBg.menuBg" :text-color="sidebarBg.textColor" :active-text-color="sidebarBg.activeTextColor" :unique-opened="false" :collapse-transition="false" :mode="mode">
      <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path" :style="{ 'background-color': sidebarBg.activeBgColor }" :class="menuStyle === 'horizontal' && selectMenu(route)" />
    </el-menu>
  </div>
</template>

<script>
import {
  mapGetters
} from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  name: 'sibar',
  components: {
    SidebarItem,
    Logo
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar',
      'menuStyle',
      'sidebarBg',
      'activeMenu'
    ]),
    showLogo () {
      return this.$store.state.settings.sidebarLogo
    },
    variables () {
      return variables
    },
    isCollapse () {
      return !this.sidebar.opened
    },
    mode () {
      return this.menuStyle !== 'horizontal' ? 'vertical' : 'horizontal'
    },

  },
  methods: {
    selectMenu (routeItem) {
      if (routeItem.children && routeItem.children.length > 1 && this.activeMenu.includes(routeItem.path)) {
        return 'selectMenu'
      } else {
        return 'notSelectMenu'
      }
    }
  }

}

</script>
