package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资产管理-IP资产表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "asset_ip")
public class AssetIp extends BaseEntity<AssetIp> implements Serializable {

    private static final long serialVersionUID = -728518107683582661L;
    /**
     * 资产ip 逐渐
     */
    @TableId
    private String asset_ip_id;
    /**
     * 资产ip类型 1-IPV4 2-IPV6
     */
    private String asset_ip_type;
    /**
     * 资产IP地址
     */
    private String asset_ip;
    /**
     * 资产隶属部门关系
     */
    private String asset_department_id;
    /**
     * 设备类型
     */
    private String device_type;
    /**
     * 主机名称
     */
    private String host_name;
    /**
     * 系统类型
     */
    private String system_type;
    /**
     * 资产状态  0-未定义  1-在线 2-沉默
     */
    private String asset_status;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 终端安全-内网IP
     */
    private String terminal_inip;
    /**
     * 终端安全-外网IP
     */
    private String terminal_outip;
    /**
     * 终端安全-内核版本
     */
    private String terminal_kernel;
    /**
     * 终端安全-业务组
     */
    private String terminal_business;
    /**
     * 终端安全-系统启动时间
     */
    private String terminal_uptime;
    /**
     * 终端安全-安装时间
     */
    private String terminal_installtime;
    /**
     * 终端安全-最后上线时间
     */
    private String terminal_onlinetime;
    /**
     * 终端安全-最后下线时间
     */
    private String terminal_offlinetime;
    /**
     * 硬件配置-生产商
     */
    private String hardware_producer;
    /**
     * 硬件配置-设备型号
     */
    private String hardware_equiptype;
    /**
     * 硬件配置-序列号
     */
    private String hardware_serial;
    /**
     * 硬件配置-设备UUID
     */
    private String hardware_uuid;
    /**
     * 硬件配置-内存
     */
    private String hardware_ram;
    /**
     * 硬件配置-cpu
     */
    private String hardware_cpu;
    /**
     * 硬件配置-使用率
     */
    private String hardware_utilization;
    /**
     * 硬件配置-系统负载 0-未知 1-低 2-中 3-高
     */
    private String hardware_systemload;
    /**
     * 资产权重-等级保护
     */
    private String weight_protection;
    /**
     * 资产权重-重要程度
     */
    private String weight_importance;
    /**
     * 资产权重-涉密
     */
    private String weight_secret;
    /**
     * 组织架构-单位
     */
    private String organ_unit;
    /**
     * 组织架构-部门
     */
    private String organ_department;
    /**
     * 组织架构-科室
     */
    private String organ_office;
    /**
     * 地理位置-国家
     */
    private String position_country;
    /**
     * 地理位置-省份
     */
    private String position_province;
    /**
     * 地理位置-地市
     */
    private String position_city;
    /**
     * 地理位置-区县
     */
    private String position_county;
    /**
     * 地理位置-其他
     */
    private String position_other;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
