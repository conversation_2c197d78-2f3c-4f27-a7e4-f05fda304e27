package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "api_task")
public class ApiTask extends BaseEntity<ApiTask> implements Serializable {
    private static final long serialVersionUID = 6277421539446783773L;


    /**
     * 主键
     */
    @TableId
    private String api_task_id;
    /**
     * 调用api接口创建任务的任务id
     */
    private String task_id;
    /**
     * 任务名称
     */
    private String task_name;
    /**
     * 探测任务目标类
     * 型，4 表示 IPv4、
     * 6 表 示 IPv6 、
     * domain 表示域名
     */
    private String ip_version;
    /**
     * ip
     */
    private String ip_range;
    /**
     * 在本系统创建的任务id
     */
    private String task_basic_id;

    /**
     * 扫描工具id
     */
    private String tool_basic_id;
    /**
     * 调用api生成的任务返回的结果信息
     */
    private String api_task_info;
    /**
     * 任务类型 漏洞扫描有多种扫描类型
     */
    private String task_type;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
