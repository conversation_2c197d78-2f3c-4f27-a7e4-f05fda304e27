<template>
    <div class="childTask-container">
        <el-container>
            <el-main>
                <el-tabs
                    ref="tabs"
                    v-model="activeTab"
                    @tab-click="handleTabClick"
                >
                    <el-tab-pane
                        v-for="item of statusEnum"
                        :key="item.label"
                        :label="item.label"
                        :name="item.value"
                    ></el-tab-pane>
                </el-tabs>

                <!-- region 表格 -->
                <div v-bind:style="{ height: tableHeight + 'px' }">
                    <vxe-table
                        id="myTable"
                        ref="myTable"
                        v-loading="loading"
                        element-loading-text="拼命加载中"
                        border
                        auto-resize
                        resizable
                        height="auto"
                        show-overflow
                        :sort-config="{ trigger: 'cell', remote: true }"
                        @sort-change="sortChange"
                        :data="page.records"
                        :checkbox-config="{ trigger: 'row' }"
                    >
                        <vxe-table-column
                            type="checkbox"
                            width="50"
                            fixed="left"
                        ></vxe-table-column>
                        <vxe-table-column
                            title="序号"
                            type="seq"
                            width="60"
                            fixed="left"
                        ></vxe-table-column>
                        <vxe-table-column field="task_name" title="任务名称" sortable>
                        </vxe-table-column>
                        <vxe-table-column
                            field="ip_version"
                            title="目标类型"
                            width="80"
                        >
                            <template v-slot="{ row }">
                                <el-tag type="" size="mini" effect="plain">
                                    {{ ip_versionText(row.ip_version) }}
                                </el-tag>
                            </template>
                        </vxe-table-column>
                        <vxe-table-column field="ip_range" title="检测目标">
                        </vxe-table-column>
                        <vxe-table-column
                            field="dispatch_start_time"
                            title="开始时间"
                            width="150"
                            sortable
                        >
                        </vxe-table-column>
                        <vxe-table-column
                            field="dispatch_end_time"
                            title="结束时间"
                            width="150"
                            sortable
                        >
                        </vxe-table-column>
                        <vxe-table-column
                            field="dispatch_run_time"
                            title="运行时间"
                            width="100"
                        >
                        </vxe-table-column>
                        <vxe-table-column
                            field="task_progress"
                            title="任务进度"
                            v-if="tool_basic_id != 1"
                            sortable
                        >
                            <template v-slot="{ row }">
                                <el-progress
                                    :stroke-width="5"
                                    :percentage="
                                        !row.task_progress
                                            ? 0
                                            : row.task_progress
                                    "
                                ></el-progress>
                            </template>
                        </vxe-table-column>
                        <vxe-table-column
                            field="task_status"
                            title="任务状态"
                            width="100"
                            sortable
                        >
                            <template v-slot="{ row }">
                                <el-tag
                                    size="mini"
                                    effect="dark"
                                    :type="statusTagType(row.task_status)"
                                >
                                    {{ task_statusText(row.task_status) }}
                                </el-tag>
                            </template></vxe-table-column
                        >
                        <vxe-table-column
                            field=""
                            title="操作"
                            fixed="right"
                            width="100"
                        >
                            <template v-slot="{ row }">
                                <i
                                    class="el-icon-s-promotion"
                                    style="font-size: 18px; color: #409eff"
                                    @click="handleRunHistory(row)"
                                ></i>
                            </template>
                        </vxe-table-column>
                    </vxe-table>
                </div>
                <!-- endregion-->

                <!-- region 分页-->
                <vxe-pager
                    ref="pager"
                    :current-page="queryParams.pagenumber"
                    :page-size="queryParams.pagesize"
                    :total="page.total"
                    @page-change="handlePageChange"
                >
                </vxe-pager>

                <!-- 运行历史弹窗 -->
                <vxe-modal
                    ref="operationHistoryModal"
                    width="60%"
                    height="99%"
                    position="center"
                    resize
                    title="运行历史"
                    showFooter
                >
                    <vxe-table
                        id="operationHistoryTable"
                        v-loading="operationHistoryTableoading"
                        element-loading-text="拼命加载中"
                        border
                        auto-resize
                        resizable
                        height="90%"
                        show-overflow
                        :custom-config="{ storage: true }"
                        :data="operationHistoryTableData.data"
                    >
                        <vxe-table-column
                            title="序号"
                            type="seq"
                            width="60"
                            fixed="left"
                        ></vxe-table-column>
                        <vxe-table-column
                            field="dispatch_start_time"
                            title="任务开始时间"
                        ></vxe-table-column>
                        <vxe-table-column
                            field="dispatch_end_time"
                            title="任务结束时间"
                        ></vxe-table-column>
                        <vxe-table-column
                            field="dispatch_run_time"
                            title="所用时长"
                        ></vxe-table-column>
                        <vxe-table-column field="task_status" title="状态">
                            <template v-slot="{ row }">
                                <el-tag
                                    size="mini"
                                    effect="dark"
                                    :type="statusTagType(row.task_status)"
                                >
                                    {{ task_statusText(row.task_status) }}
                                </el-tag>
                            </template>
                        </vxe-table-column>
                        <vxe-table-column
                            field="failure_reason"
                            title="失败原因"
                        >
                            <template v-slot="{ row }">
                                {{ row.failure_reason || "无" }}
                            </template>
                        </vxe-table-column>
                    </vxe-table>
                    <template v-slot:footer>
                        <vxe-button
                            type="button"
                            content="关闭"
                            @click="$refs.operationHistoryModal.close()"
                        ></vxe-button>
                    </template>

                    <vxe-pager
                        ref="operationHistoryPager"
                        :current-page="operationHistoryTableData.pagenumber"
                        :page-size="operationHistoryTableData.pagesize"
                        :total="operationHistoryTableData.total"
                        @page-change="handleHistoryPageChange"
                    >
                    </vxe-pager>
                </vxe-modal>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import { listDictionarys } from "@/api/security/dictionary";
import { listApiTask, pageApiTaskDispatch } from "@/api/testManage";
import { mapGetters } from "vuex";
export default {
    name: "ChildTask",
    components: {},
    props: {},
    watch: {
        extraHeight() {
            this.setTableHeight();
        },
        // 监听查询条件表单高度变化
        queryConditionFormHeight(val) {
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.$refs.pager.$el.offsetHeight -
                this.tabsHeight -
                val;
        }
    },
    computed: {
        ...mapGetters(["extraHeight"])
    },
    data() {
        return {
            activeTab: "99",
            tabsHeight: 54,
            queryConditionFormHeight: 0, //查询条件表单高度
            tableHeight: 0, // 表格高度
            loading: true,
            page: {
                total: 0,
                records: []
            },
            // 查询条件
            queryParams: {
                pagenumber: 1,
                pagesize: 10,
                task_basic_id: ""
            },
            tool_basic_id: "",
            //   任务状态枚举
            statusEnum: [{ label: "全部", value: "99" }],
            //   目标类型-检测目标
            testTargetEnum: [],

            //   运行历史弹窗表格数据
            operationHistoryTableData: {
                total: 0,
                pagenumber: 1,
                pagesize: 10,
                data: []
            },
            operationHistoryTableData_row: {},
            operationHistoryTableoading: false,
            firstLoadTableData: true
        };
    },
    created() {
        this.initData();
    },
    mounted() {
        this.queryParams.task_basic_id = this.$route.query.id;
        this.tool_basic_id = this.$route.query.tool_basic_id;
        this.loadTable();
        // this.setQueryConditionFormHeight();
        this.setTableHeight();
        this.windowResize();
    },
    methods: {
        initData() {
            // 加载字典
            listDictionarys().then(res => {
                res.data.forEach(item => {
                    const dict = {
                        label: item.dictionaryname,
                        value: item.dictionaryvalue
                    };
                    if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[10]
                            .dictionarytypecode
                    ) {
                        this.statusEnum.push(dict);
                    } else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[13]
                            .dictionarytypecode
                    ) {
                        this.testTargetEnum.push(dict);
                    }
                });
            });
        },
        // 处理tabs点击
        handleTabClick(tab) {
            this.queryParams.task_status = tab.name;
            if (tab.name === "99") this.queryParams.task_status = "";
            this.loadTable();
        },
        // 设置查询条件表单的高度
        setQueryConditionFormHeight() {
            this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
        },
        // 设置表格的高度
        setTableHeight() {
            // console.log("extraHeight=", this.extraHeight);
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.queryConditionFormHeight -
                this.$refs.pager.$el.offsetHeight -
                this.tabsHeight;

            // console.log("tableHeight=", this.tableHeight);
        },
        // 监听窗口改变
        windowResize() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    that.setTableHeight();
                    console
                        .log
                        // "窗口resize-----------------" + that.tableHeight
                        ();
                })();
            };
        },
        // 处理查询条件表单折叠按钮折叠
        handleQueryConditionFormToggleCollapse(collapse, data) {
            this.$nextTick(function() {
                this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
            });
        },
        // 处理查询条件表单提交
        handleQueryConditionFormSubmit({ data }) {
            this.queryParams.pagenumber = 1;
            this.loadTable();
        },
        // 处理页码变化
        handlePageChange({ currentPage, pageSize }) {
            this.queryParams.pagenumber = currentPage;
            this.queryParams.pagesize = pageSize;
            this.loadTable();
        },
        // 查询条件
        getQueryCondition() {
            let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
            if (this.firstLoadTableData) quryCondition.task_status = "";
            return quryCondition;
        },
         /* 字段排序 */
        sortChange(e) {
            const { field, order } = e;

            this.queryParams.sort_field = field;
            this.queryParams.sort_order = order;

            this.loadTable();
        },
        // 加载表格
        loadTable() {
            this.loading = true;
            listApiTask(this.getQueryCondition())
                .then(response => {
                    this.page.records = response.data.records;
                    this.page.total = response.data.total;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                })
                .finally(() => (this.firstLoadTableData = false));
        },
        ip_versionText(val) {
            const item = this.testTargetEnum.find(i => i.value === val);
            return item && item.label;
        },
        task_statusText(val) {
            const item = this.statusEnum.find(i => i.value === val);
            return item && item.label ? item.label : "未运行";
        },
        statusTagType(val) {
            let type;
            switch (val) {
                case "5":
                    type = "danger";
                    break;
                case "4":
                    type = "";
                    break;
                case "3":
                    type = "info";
                    break;
                case "2":
                    type = "info";
                    break;
                case "1":
                    type = "success";
                    break;
                case "0":
                    type = "info";
                    break;
                default:
                    break;
            }
            return type;
        },

        //运行历史相关

        // 请求列表数据
        loadHistoryTableData(row) {
            console.log(row);
            pageApiTaskDispatch({
                api_task_id: row.api_task_id,
                tool_basic_id: row.tool_basic_id,
                pagenumber: this.operationHistoryTableData.pagenumber,
                pagesize: this.operationHistoryTableData.pagesize
            })
                .then(({ code, data }) => {
                    if (code === 20000) {
                        this.operationHistoryTableData.total = data.total;
                        this.operationHistoryTableData.data = data.records;
                    }
                })
                .finally(() => (this.operationHistoryTableoading = false));
        },
        // 运行历史弹窗按钮点击
        handleRunHistory(row) {
            this.operationHistoryTableData.data = [];
            this.$refs.operationHistoryModal.open();
            this.operationHistoryTableoading = true;
            this.loadHistoryTableData(row);
            this.operationHistoryTableData_row = row;
        },
        // 运行历史页码变化
        handleHistoryPageChange({ currentPage, pageSize }) {
            this.operationHistoryTableData.pagenumber = currentPage;
            this.operationHistoryTableData.pagesize = pageSize;
            this.loadHistoryTableData(this.operationHistoryTableData_row);
        }
    }
};
</script>

<style lang="scss" scoped></style>
