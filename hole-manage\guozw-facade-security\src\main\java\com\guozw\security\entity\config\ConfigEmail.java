package com.guozw.security.entity.config;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "config_email")
public class ConfigEmail extends BaseEntity<ConfigEmail> implements Serializable {

    private static final long serialVersionUID = -8180428915332587690L;

    @TableId
    private String config_email_id;
    /**
     * 邮箱地址（邮箱账号）
     */
    private String email_address;
    /**
     * 登录密码
     */
    private String email_password;
    /**
     * 显示名称
     */
    private String email_name;
    /**
     * 发件服务器
     */
    private String email_from_server;
    /**
     * 是否使用ssl 0-不使用 1-使用
     */
    private String isssl;
    /**
     * 邮箱端口
     */
    private String email_port;
    /**
     * 创建人
     */
    private String createuserid;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
