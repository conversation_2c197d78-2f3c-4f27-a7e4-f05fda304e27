package com.guozw.common.core.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 调用api的应用信息
 */
@Data
@Accessors(chain = true)
public class AppInfo implements Serializable {

    private static final long serialVersionUID = -9215865898703862227L;

    /**
     * 应用id
     */
    private String appId;
    /**
     * 应用秘钥 16位 作为参数aes加密的秘钥，也加入到参数签名当中
     */
    private String appSecret;
}
