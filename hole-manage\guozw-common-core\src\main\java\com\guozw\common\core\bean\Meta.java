package com.guozw.common.core.bean;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class Meta implements Serializable {
    private static final long serialVersionUID = 2839480305300652039L;

    private String title;
    private String icon;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
