package com.guozw.common.core.util;

import com.guozw.common.core.constant.JwtConstant;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;

/**
 * JwtToken工具类
 * <AUTHOR>
 * @date 2020/4/19
 */
@Slf4j
public class JwtTokenUtils {

    /**
     * token的过期时间86400秒，即一天
     */
    private static final long EXPIRATION = Long.parseLong(JwtConstant.JWT_TOKEN_EXPIRATION);
    /**
     * 选择了记住我之后的过期时间为7天
     */
    private static final long EXPIRATION_REMEMBER = Long.parseLong(JwtConstant.JWT_TOKEN_REMEMBER_EXPIRATION);

    /**
     * 加密算法
     */
    private static final SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS256;

    /**
     * 创建token
     * @param username 用户名
     * @param remenberme 是否记住我
     * @return
     */
    public static String createToken(String username, Boolean remenberme) {
        long expiration = remenberme ? EXPIRATION_REMEMBER : EXPIRATION;
        Calendar calendar = Calendar.getInstance();
        Date currentDate = new Date();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DATE, 1);
        return Jwts.builder()
                .signWith(SIGNATURE_ALGORITHM, JwtConstant.JWT_TOKEN_SECRET)
                .setSubject(username)
                .setIssuer(JwtConstant.JWT_TOKEN_ISS)
                .setIssuedAt(new Date())
//                .setExpiration(calendar.getTime()) // jwtToken交有Redis管理，这里不设置过期时间
                .compact();
    }

    /**
     * 从token中获取用户名
     * @param token
     * @return
     */
    public static String getUsername(String token) {
        return getTokenBody(token).getSubject();
    }
    public static String getUsername(HttpServletRequest request) {
        return getTokenBody(getToken(request)).getSubject();
    }
    /**
     * 获取过期时间
     * @param token
     * @return
     */
    public static Date getExpiration(String token) {
        return getTokenBody(token).getExpiration();
    }

    /**
     * token是否过期
     * @param token
     * @return
     */
    public static boolean isExpiration(String token) {
        return getTokenBody(token).getExpiration().before(new Date());
    }
    /**
     * 从请求头中获取token
     * @param request
     * @return
     */
    public static String getToken(HttpServletRequest request) {
        String token = request.getHeader(JwtConstant.JWT_TOKEN_HEADER);
        token = token.replace(JwtConstant.JWT_TOKEN_PREFIX, StringUtils.EMPTY);
        return token;
    }

    private static Claims getTokenBody(String token) {
        return Jwts.parser()
                .setSigningKey(JwtConstant.JWT_TOKEN_SECRET)
                .parseClaimsJws(token)
                .getBody();
    }


    public static void main(String[] args) {

        String token = "Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ6aGFuZ2ZlaSIsInJvbGVzIjpbIjMwMDAwIl0sImlzcyI6Imd1b3p3IiwiZXhwIjoxNTk3NTg5NDAyLCJpYXQiOjE1OTc1MDMwMDJ9.-USGKOmF3ZvlDOEn87YU9vptAfudkgJ9DYkqX_gqVkM";
        token = token.replace(JwtConstant.JWT_TOKEN_PREFIX, StringUtils.EMPTY);
        boolean bol = isExpiration(token);
        String s = "";
    }
}
