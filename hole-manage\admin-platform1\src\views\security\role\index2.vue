<template>
    <el-container>
        <el-main>
            <!-- region 表格工具栏 -->
            <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
                <template v-slot:buttons>
                    <el-button-group>
                        <el-button icon="fa fa-plus" type="primary" @click="handleAdd">
                            新增</el-button
                        >
                        <el-button icon="fa fa-edit" type="primary" @click="handleModify">
                            修改</el-button
                        >
                        <el-button icon="fa fa-times" type="primary" @click="handleDelete">
                            删除</el-button
                        >
                        <el-button icon="fa fa-list-alt" type="primary" @click="handleLook">
                            查看</el-button
                        >
                        <el-button
                            icon="fa fa-link"
                            type="primary"
                            @click="handleResourceAssign"
                        >
                            权限分配</el-button
                        >
                    </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->
            <!-- region 角色表格 -->
            <vxe-table
                id="roleTable"
                ref="roleTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                resizable
                :custom-config="{ storage: true }"
                :data="roleList"
                :checkbox-config="{ trigger: 'row' }"
            >
                <vxe-table-column
                    type="checkbox"
                    width="50"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    type="seq"
                    width="60"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="rolename"
                    title="角色名称"
                ></vxe-table-column>
                <vxe-table-column
                    field="rolenote"
                    title="角色描述"
                ></vxe-table-column>
                <vxe-table-column
                    field="ordernumber"
                    title="角色排序"
                ></vxe-table-column>
                <vxe-table-column
                    field="createdate"
                    title="创建时间"
                ></vxe-table-column>
                <vxe-table-column
                    field="modifydate"
                    title="修改时间"
                ></vxe-table-column>
            </vxe-table>

            <!-- endregion -->

            <Edit :modalInfo="modalInfo" @refreshTable="refreshTable"></Edit>
            <ResourceAssign :modalInfo="modalInfo2" ></ResourceAssign>
        </el-main>
    </el-container>
</template>

<script>
import { listRoles, deleteRole } from "@/api/security/role";
import Edit from "./components/Edit.vue";
import ResourceAssign from "./components/ResourceAssign.vue";

export default {
    name: "index2",
    components: { Edit, ResourceAssign },
    data() {
        return {
            loading: true,
            roleList: [],
            modalInfo: { show: false, title: null, type: null, rolecode: null },
            modalInfo2: { show: false, title: null, type: null, rolecode: null }
        };
    },
    created() {
        this.loadTable();
    },
    methods: {
        // 加载角色列表
        loadTable() {
            this.loading = true;
            listRoles()
                .then(response => {
                    this.roleList = response.data;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        refreshTable() {
            this.loadTable();
        },
        handleAdd() {
            this.modalInfo = {
                show: true,
                title: this.$guozwSettings.operationType[0].title,
                type: this.$guozwSettings.operationType[0].type
            };
        },
        handleModify() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();

            if (checkedRecords && checkedRecords.length === 1) {
                this.modalInfo = {
                    show: true,
                    title: this.$guozwSettings.operationType[1].title,
                    type: this.$guozwSettings.operationType[1].type,
                    rolecode: checkedRecords[0].rolecode
                };
            } else {
                this.$XModal.alert({
                    message: "请选择一条记录",
                    position: "center",
                    status: "info"
                });
            }
        },
        handleLook() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();

            if (checkedRecords && checkedRecords.length === 1) {
                this.modalInfo = {
                    show: true,
                    title: this.$guozwSettings.operationType[2].title,
                    type: this.$guozwSettings.operationType[2].type,
                    rolecode: checkedRecords[0].rolecode
                };
            } else {
                this.$XModal.alert({
                    message: "请选择一条记录",
                    position: "center",
                    status: "info"
                });
            }
        },
        handleDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                this.$XModal
                    .confirm({
                        message: "确定要删除吗？",
                        position: "center",
                        status: "warning"
                    })
                    .then(type => {
                        if (type === "confirm") {
                            deleteRole({
                                rolecode: checkedRecords[0].rolecode
                            }).then(response => {
                                this.$XModal.alert({
                                    message: "删除成功",
                                    position: "center",
                                    status: "success"
                                });
                                this.loadTable();
                            });
                        }
                    });
            } else {
                this.$XModal.alert({
                    message: "请选择一条记录",
                    position: "center",
                    status: "info"
                });
            }
        },
        handleResourceAssign() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.roleTable.getCheckboxRecords();

            if (checkedRecords && checkedRecords.length === 1) {
                this.modalInfo2 = {
                    show: true,
                    title: this.$guozwSettings.operationType[3].title,
                    type: this.$guozwSettings.operationType[3].type,
                    rolecode: checkedRecords[0].rolecode
                };
            } else {
                this.$XModal.alert({
                    message: "请选择一条记录",
                    position: "center",
                    status: "info"
                });
            }
        }
    }
};
</script>

<style scoped></style>
