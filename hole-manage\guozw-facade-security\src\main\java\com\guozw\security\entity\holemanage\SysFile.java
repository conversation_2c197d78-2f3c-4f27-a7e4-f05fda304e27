package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 漏洞处置反馈
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "sys_file")
public class SysFile extends BaseEntity<SysFile> implements Serializable {

    private static final long serialVersionUID = -5338063640482397617L;
    @TableId
    private String file_id;
    /**
     * 文件存储名称
     */
    private String file_storage_name;
    /**
     * 文件存储名称
     */
    private String file_real_name;
    /**
     * 文件后缀，如.pdf
     */
    private String file_suffix;
    /**
     * 文件类型
     */
    private String file_type;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
