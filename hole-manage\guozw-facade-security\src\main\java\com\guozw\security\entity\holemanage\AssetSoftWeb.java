package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资产管理-软件/web信息
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "asset_softweb")
public class AssetSoftWeb extends BaseEntity<AssetSoftWeb> implements Serializable {

    private static final long serialVersionUID = -1290160879408929627L;
    @TableId
    private String asset_web_id;
    /**
     * 关联类型 1-IP资产  2-网站资产
     */
    private String relation_type;
    /**
     * 关联表主键 IP资产表主键或网站资产表主键
     */
    private String relation_id;
    /**
     * 产品
     */
    private String asset_web_product;
    /**
     * 厂商
     */
    private String asset_web_com;
    /**
     * 版本
     */
    private String asset_web_version;
    /**
     * 版次
     */
    private String asset_web_edition;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
