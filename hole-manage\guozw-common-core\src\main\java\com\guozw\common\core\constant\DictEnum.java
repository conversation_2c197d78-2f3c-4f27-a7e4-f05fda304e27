package com.guozw.common.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典枚举 用来找到要的字典
 */

@AllArgsConstructor
@Getter
public enum DictEnum {


    DEVICT_TYPE("设备类型", "1583638119824404482"),
    HOLE_TYPE("漏洞类别", "1592752586292015106"),
    HOLE_PLAN_BACK_TYPE("处置反馈类型", "1592057522003034114"),
    HOLE_WHITE_STATUS("白名单状态", "1590518230176960513"),
    HOLE_CLOSE_STATUS("关闭状态", "1590517552696201217"),
    HOLE_REPAIR_STATUS("修复状态", "1590517272701243394"),
    HOLE_RETEST_STATUS("复测状态", "1590516980953845762"),
    HOLE_TEST_STATUS("验证状态", "1590516763378520066"),
    HOLE_DISCOVERY_METHOD("发现方式", "1590516317624668162"),
    HOLE_LEVEL("漏洞级别", "1590273196141015041"),
    HOLE_DISCOVERY_STATUS("发现状态", "1590516502912241666"),
    SYSTEM_TYPE("系统类型", "1583662568103653378"),
    SYSTEM_LOAD("系统负载", "1583656102483374082"),
    IMPORTANCE("重要程度", "1582319959779213313"),
    PROTECTION("等级保护", "1582319253844295681"),
    SECRET("涉密状态", "1582329732339761154"),
    ASSET_STATUS("资产状态", "1583363886477099010");


    /**
     * 字典的文本
     */
    private String text;
    /**
     * 字典的id
     */
    private String value;


}
