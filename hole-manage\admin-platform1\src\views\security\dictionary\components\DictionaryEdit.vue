<template>
  <div class="TableDialog">
    <vxe-modal
      height="90%"
      width="600"
      position="center"
      resize
      @close="handleDialogClose()"
      showFooter
      :title="dialogInfo.title"
      v-model="dialogInfo.show"
    >
      <vxe-form
        ref="ruleForm"
        title-width="120"
        :data="dialogForm"
        :rules="rules"
        prevent-submit
        :model="dialogForm"
      >
        <vxe-form-item title="字典名称:" field="dictionaryname" span="24">
          <vxe-input v-model="dialogForm.dictionaryname" placeholder="字典名称"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="字典值:" field="dictionaryvalue" span="24">
          <vxe-input v-model="dialogForm.dictionaryvalue" placeholder="字典值"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="字典描述:" field="dictionarynote" span="24">
          <vxe-input v-model="dialogForm.dictionarynote" placeholder="字典描述"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="字典排序:" field="ordernumber" span="24">
          <vxe-input v-model.number="dialogForm.ordernumber" placeholder="字典排序"></vxe-input>
        </vxe-form-item>
      </vxe-form>
      <template v-slot:footer>
        <vxe-button type="button" content="取消" @click="handleDialogCancel('ruleForm')"></vxe-button>
        <vxe-button
          type="button"
          status="primary"
          content="确定"
          @click="handleDialogSubmit('ruleForm')"
        ></vxe-button>
      </template>
    </vxe-modal>
  </div>
</template>
<script>
import { saveDictionary, modifyDictionary } from "@/api/security/dictionary.js";
export default {
  name: "TableDialog",
  props: {
    dialogInfo: {
      type: Object,
      default: () => {}
    },
    form: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      formLabelWidth: "100px",
      dialogForm: {},
      rules: {
        dictionaryname: [
          { required: true, message: "请输入字典名称" },
          { min: 1, max: 100, message: "长度最大为 100 字符" }
        ],
        dictionaryvalue: [
          { required: true, message: "请输入字典值" },
          { min: 1, max: 100, message: "长度最大为 100 字符" }
          ],
        dictionarynote: [
          { required: true, message: "请输入字典描述" },
         { min: 1, max: 100, message: "长度最大为 100 字符" }
        ],
        ordernumber: [
          { required: true, message: "请输入字典排序" },
          { type: "number", message: "类型为number类型" }
        ]
      },
       dictionarytypecode:"",
    };
  },
   
  watch: {
    form(newVal, oldVal) {
      this.dialogForm = JSON.parse(JSON.stringify(newVal));
        this.dictionarytypecode=this.dialogForm.dictionarytypecode
    }
  },
  methods: {
    
    handleDialogClose() {
      this.dialogInfo.show = false;
      this.$refs.ruleForm.clearValidate();
    },
    handleDialogCancel(formName) {
      this.dialogInfo.show = false;
        this.$refs.ruleForm.clearValidate();
    },
    handleDialogSubmit(formName) {
      this.dialogForm.dictionarytypecode=this.dictionarytypecode
      this.$refs[formName].validate().then(()=> {
          if (this.dialogInfo.type == "add") {
            saveDictionary(this.dialogForm)
              .then(response => {
                this.$XModal.message({
                                message: "新增成功",
                                status: "success"
                            });
                
                this.dialogInfo.show = false;
                this.$emit("upData");
              })
              .catch(err => {
                this.$XModal.message({
                                message: "新增失败",
                                status: "error"
                            });
                this.dialogInfo.show = false;
              });
          } else if (this.dialogInfo.type == "edit") {
            modifyDictionary(this.dialogForm)
              .then(response => {
                this.$XModal.message({
                                message: "修改成功",
                                status: "success"
                            });
               
                this.dialogInfo.show = false;
                this.$emit("upData");
              })
              .catch(err => {
                this.$XModal.message({
                                message: "修改失败",
                                status: "error"
                            });
              
                this.dialogInfo.show = false;
              });
          } else if (this.dialogInfo.type == "look") {
            
            return (this.dialogInfo.show = false);
          } 
      }).catch(err=>{
        console.log(err)
        return false;
      })
    }
  }
};
</script>
