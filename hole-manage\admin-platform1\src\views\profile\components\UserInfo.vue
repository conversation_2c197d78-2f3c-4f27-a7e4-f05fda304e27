<template>
    <vxe-form
        ref="form"
        title-align="right"
        title-width="100"
        :data="formData"
        :rules="formRules"
        prevent-submit
    >
        <vxe-form-item
            title="昵称"
            field="nickname"
            span="24"
            :reset-value="userInfo.nickname"
        >
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.nickname"
                    placeholder="昵称"
                    clearable
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>
        <vxe-form-item
            title="性别"
            field="usersex"
            span="24"
            :reset-value="userInfo.usersex"
        >
            <template v-slot="scope">
                <el-select
                    v-model="formData.usersex"
                    clearable
                    filterable
                    placeholder="性别"
                    style="width:100%"
                    @input="$refs.form.updateStatus(scope)"
                >
                    <el-option
                        v-for="item in sexEnum"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </template>
        </vxe-form-item>

        <vxe-form-item
            title="联系方式"
            field="userphone"
            span="24"
            :reset-value="userInfo.userphone"
        >
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.userphone"
                    placeholder="联系方式"
                    clearable
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>
        <vxe-form-item
            title="联系地址"
            field="useraddress"
            span="24"
            :reset-value="userInfo.useraddress"
        >
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.useraddress"
                    placeholder="联系地址"
                    clearable
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>

        <vxe-form-item
            title="邮箱地址"
            field="usermail"
            span="24"
            :reset-value="userInfo.usermail"
        >
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.usermail"
                    placeholder="邮箱地址"
                    clearable
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>

        <vxe-form-item align="center" span="24">
            <template v-slot>
                <vxe-button type="" status="primary" @click="handleSubmit"
                    >提交修改</vxe-button
                >
                <vxe-button type="reset">重置</vxe-button>
            </template>
        </vxe-form-item>
    </vxe-form>
</template>

<script>
import { getEnumSex } from "@/api/security/common.js";
import { modifyUser2 } from "@/api/security/user";
import { patterns } from "@/utils/guozw-core";

export default {
    data() {
        return {
            userInfo: this.$store.getters.userInfo,
            formData: {},
            sexEnum: [],
            formRules: {
                nickname: [
                    { required: true, message: "请输入姓名" },
                    { min: 1, max: 10, message: "长度在 1 到 10 个字符" }
                ],
                usersex: [{ required: true, message: "请选择性别" }],

                userphone: [
                    { required: true, message: "请输入联系电话" },
                    {
                       pattern: patterns.phoneOrTel, message:"格式不正确"
                    }
                ],
                useraddress: [{ max: 50, message: "最多 50 个字符" }],
                usermail: [
                     { required: true, message: "请输入联系电话" },
                    {
                       pattern: patterns.email, message:"格式不正确"
                    }
                ]
            }
        };
    },
    created() {
        this.loadFormData();
        this.loadSexEnum();
    },
    methods: {
        loadFormData() {
            const userInfo = this.$store.getters.userInfo;
            this.formData = {
                usercode: userInfo.usercode,
                nickname: userInfo.nickname,
                usersex: userInfo.usersex,
                userphone: userInfo.userphone,
                useraddress: userInfo.useraddress,
                userphone: userInfo.userphone,
                usermail: userInfo.usermail
            };
        },
        loadSexEnum() {
            getEnumSex().then(res => {
                this.sexEnum = res.data;
            });
        },
        handleSubmit() {
            this.$refs.form.validate().then(() => {
                modifyUser2(this.formData).then(response => {
                    this.$XModal.message({
                        message: "修改成功",
                        status: "success"
                    });
                    this.$store.dispatch("user/getInfo");
                });
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
