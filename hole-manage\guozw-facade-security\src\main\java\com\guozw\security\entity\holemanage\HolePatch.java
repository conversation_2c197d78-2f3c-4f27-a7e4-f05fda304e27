package com.guozw.security.entity.holemanage;


import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import com.guozw.security.entity.WorkorderWarn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

import java.util.Date;

/**
* 漏洞补丁库
* @TableName hole_patch
*/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value ="hole_patch")
public class HolePatch extends BaseEntity<HolePatch> implements Serializable {

    /**
     * 主键
     */
    private String hole_patch_id;

    /**
     * 官方地址
     */
    private String hole_patch_url;

    /**
     * 补丁描述
     */
    private String hole_patch_remark;

    /**
     * 补丁附件
     */
    private String file_id;

    /**
     * 补丁附件类型ID
     */
    private String filetype_id;

    /**
     * 更新时间
     */
//    private Date modifytime;
//    private Date createdate;;
}
