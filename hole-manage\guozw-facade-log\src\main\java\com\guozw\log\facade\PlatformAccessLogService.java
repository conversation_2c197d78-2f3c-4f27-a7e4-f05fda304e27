package com.guozw.log.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guozw.log.entity.PlatformAccessLog;
import com.guozw.log.vo.PlatformAccessLogVo;

/**
 * 平台访问日志
 * <AUTHOR>
 */
public interface PlatformAccessLogService extends IService<PlatformAccessLog> {

    Page<PlatformAccessLogVo> pagePlatformAccessLog(PlatformAccessLogVo entity);

    PlatformAccessLog onePlatformAccessLog(PlatformAccessLog entity);

    /**
     * 异步保存平台访问日志
     * @param entity
     */
    void savePlatformAccessLogAsync(PlatformAccessLog entity);

    /**
     * 异步修改平台访问日志
     * @param entity
     */
    void modifyPlatformAccessLogAsync(PlatformAccessLog entity);
}
