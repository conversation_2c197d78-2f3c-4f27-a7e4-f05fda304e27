<template>
  <el-container>
    <el-aside width="200px">
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText"
      ></el-input>
      <vxe-toolbar ref="toolbar">
        <template v-slot:buttons>
          <el-button
            icon="fa fa-plus"
            type="primary"
            size="mini"
            @click="handleTreeAdd"
            v-btnpermission="'security:dictionarytype:add'"
          ></el-button>
          <el-button
            icon="fa fa-edit"
            type="primary"
            size="mini"
            @click="handleTreeModify"
            v-btnpermission="'security:dictionarytype:modify'"
          ></el-button>
          <el-button
            icon="fa fa-trash-o"
            type="primary"
            size="mini"
            @click="handleTreeDelete"
            v-btnpermission="'security:dictionarytype:del'"
          ></el-button>
        </template>
      </vxe-toolbar>
      <el-tree
        class="dictionaryTypeTree"
        ref="dictionaryTypeTree"
        node-key="dictionarytypecode"
        :filter-node-method="filterNode"
        :data="dictionaryTypeTreeNodes"
        default-expand-all
        highlight-current
        @node-click="handleTreeClick"
        :props="{ children: 'children', label: 'dictionarytypename' }"
      ></el-tree>
    </el-aside>
    <el-main>
      <!-- region 表格工具栏 -->
      <vxe-toolbar
        ref="toolbar"
        :refresh="{ query: loadDictionaryTable }"
        custom
      >
        <template v-slot:buttons>
          <el-button-group>
            <el-button
              type="primary"
              icon="fa fa-plus"
              @click="handleDictionaryAdd"
              v-btnpermission="'security:dictionary:add'"
            >
              新增</el-button
            >
            <el-button
              type="primary"
              icon="fa fa-edit"
              @click="handleDictionaryModify"
              v-btnpermission="'security:dictionary:modify'"
            >
              修改</el-button
            >
            <el-button
              type="primary"
              icon="fa fa-trash"
              @click="handleDictionaryDelete"
              v-btnpermission="'security:dictionary:del'"
            >
              删除</el-button
            >
            <el-button
              type="primary"
              icon="fa fa-list-alt"
              @click="handleDictionaryLook"
            >
              查看</el-button
            >
          </el-button-group>
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="dictionaryTable"
          ref="dictionaryTable"
          v-loading="loading"
          element-loading-text="拼命加载中"
          border
          auto-resize
          align="center"
          height="auto"
          resizable
          :custom-config="{ storage: true }"
          :data="dictionaryList"
          :checkbox-config="{ trigger: 'row' }"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
            title="序号"
          ></vxe-table-column>
          <vxe-table-column
            type="seq"
            width="60"
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="dictionaryname"
            title="字典名称"
          ></vxe-table-column>
          <vxe-table-column
            field="dictionaryvalue"
            title="字典值"
          ></vxe-table-column>
          <vxe-table-column
            field="dictionarynote"
            title="字典描述"
          ></vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->
    </el-main>
    <DictionaryEdit
      :dialogInfo="dialogInfo"
      :form="form"
      @upData="upData"
    ></DictionaryEdit>
    <DictionaryTypeEdit
      :dictionaryTypeModal="dictionaryTypeModal"
      :treeForm="treeForm"
      @treeUpdata="treeUpdata"
    ></DictionaryTypeEdit>
  </el-container>
</template>
<script>
import { listDictionarys, deleteDictionary } from "@/api/security/dictionary";
import {
  listDictionaryTypes,
  deleteDictionaryType
} from "@/api/security/dictionaryType";
import DictionaryEdit from "./components/DictionaryEdit.vue";
import DictionaryTypeEdit from "./components/DictionaryTypeEdit.vue";
import {
  mapGetters
} from 'vuex'
export default {
  name: "index",
  data() {
    return {
      filterText: "",
      loading: true,
      dictionaryTypeTreeNodes: [
        { dictionarytypename: "字典项类别", children: [] }
      ],
      dictionaryList: [],
      dialogInfo: { show: false, title: "" },
      form: {
        dictionaryname: "",
        dictionaryvalue: "",
        dictionarynote: "",
        dictionarytypecode: "",
        ordernumber: ""
      },
      queryParams: {
        dictionarytypecode: ""
      },
      tableHeight: 0,
      treetypecode: "字典项类别",
      dictionaryTypeModal: {
        type: "",
        show: false,
        title: ""
      },
      treeForm: {
        dictionarytypename: "",
        dictionarytypenote: ""
      },
      publicDictionaryCode: ""
    };
  },
  created() {
    this.loadDictionaryTable();
    this.loadDictionaryTypesTree();
  },
  computed: {
    ...mapGetters(['extraHeight'])
  },
  watch: {
    extraHeight() {
      this.setTableHeight()
    },
    filterText(val) {
      this.$refs.dictionaryTypeTree.filter(val);
    }
  },
  mounted() {
    this.setTableHeight();

  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.dictionarytypename.indexOf(value) !== -1;
    },
    setTableHeight() {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight;
    },
    loadDictionaryTable() {
      this.loading = true;
      listDictionarys(this.queryParams)
        .then(response => {
          this.dictionaryList = response.data;
          this.loading = false;
        })
        .catch(error => {
          this.loading = false;
        });
    },
    loadDictionaryTypesTree() {
      listDictionaryTypes()
        .then(response => {
          this.dictionaryTypeTreeNodes[0].children = response.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleTreeClick(node) {
      if (node.dictionarytypename == "字典项类别") {
        this.treetypecode = "字典项类别";
        this.queryParams.dictionarytypecode = null;
        this.publicDictionaryCode = "";
        this.loadDictionaryTable();
        return false;
      }
      this.treetypecode = node.dictionarytypecode;
      this.treeForm = node;
      this.queryParams.dictionarytypecode = node.dictionarytypecode;
      this.publicDictionaryCode = node.dictionarytypecode;
      this.loadDictionaryTable();
    },
    //table添加
    handleDictionaryAdd() {
      if (this.publicDictionaryCode == "") {
        return this.$XModal.alert({
          message: "请添加类型",
          status: "warning"
        });;
      }
      this.form = {
        dictionaryname: "",
        dictionaryvalue: "",
        dictionarynote: "",
        dictionarytypecode: this.publicDictionaryCode,
        ordernumber: ""
      };
      this.dialogInfo = {
        show: true,
        title: "新增字典项",
        type: "add"
      };
    },
    upData() {
      this.loadDictionaryTable();
    },
    //table修改
    handleDictionaryModify() {
      const checkedRecords = this.$refs.dictionaryTable.getCheckboxRecords();
      if (checkedRecords.length != 1) {
        return this.$XModal.alert({
          message: "请选择一条记录",
          status: "warning"
        });;
      }
      this.form = checkedRecords[0];
      this.dialogInfo = {
        show: true,
        title: "修改字典项",
        type: "edit"
      };
    },
    //table删除
    handleDictionaryDelete() {
      const checkedRecords = this.$refs.dictionaryTable.getCheckboxRecords();
      if (checkedRecords.length != 1) {
        return this.$XModal.alert({
          message: "请选择一条数据",
          status: "warning"
        });;
      }
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        deleteDictionary({ dictionarycode: checkedRecords[0].dictionarycode })
          .then(response => {
            this.$XModal.message({
              message: "删除成功",
              status: "success"
            });
            this.loadDictionaryTable();
          })
          .catch(() => {
            this.$XModal.message({
              message: "删除失败",
              status: "error"
            });
          });
      });
    },
    //table查看
    handleDictionaryLook() {
      const checkedRecords = this.$refs.dictionaryTable.getCheckboxRecords();
      if (checkedRecords.length != 1) {
        return this.$XModal.alert({
          message: "请选择一条记录",
          status: "warning"
        });
      }
      this.form = checkedRecords[0];
      this.dialogInfo = {
        show: true,
        title: "查看字典项",
        type: "look"
      };
    },
    //tree添加
    handleTreeAdd() {
      if (this.treetypecode != "字典项类别" && this.treetypecode != "") {
        return this.$XModal.message({
          message: "该目录分类下已建目录，不可以建子目录分类",
          status: "warning"
        });
      }
      this.dictionaryTypeModal = {
        type: "Add",
        show: true,
        title: "添加"
      },
        this.treeForm = {
          dictionarytypename: "",
          dictionarytypenote: ""
        };
      // this.treeDialog.show=true
    },
    //tree编辑
    handleTreeModify() {
      if (this.treetypecode == "字典项类别") {
        return this.$XModal.alert({
          message: "请选择节点",
          status: "warning"
        });
      }
      this.dictionaryTypeModal = {
        type: "Edit",
        show: true,
        title: "修改"
      };
    },
    //tree删除
    handleTreeDelete() {
      if (this.treetypecode == "字典项类别") {
        return this.$XModal.alert({
          message: "请选择节点",
          status: "warning"
        });
      }
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        deleteDictionaryType({
          dictionarytypecode: this.treeForm.dictionarytypecode
        })
          .then(res => {
            this.loadDictionaryTypesTree();
            this.$XModal.message({
              message: "删除成功",
              status: "success"
            });
          })
          .catch(() => {
            this.$XModal.message({
              message: "删除失败",
              status: "error"
            });
          });
      });
    },
    treeUpdata() {
      this.loadDictionaryTypesTree();
    }
  },
  components: {
    DictionaryEdit,
    DictionaryTypeEdit
  }
};
</script>

<style scoped lang="scss">
/deep/ .dictionaryTypeTree {
  .el-tree-node__label {
    font-size: 12px;
  }
}
</style>

