package com.guozw.security.entity.holemanage;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class HomePageCount implements Serializable {


    private static final long serialVersionUID = 1060017635892476753L;
    //ipv4资产数
    private  Long assetIpCount;
    //在线资产数
    private  Long assetIpOnLineCount;
    //有漏洞资产数
    private  Long vulnerableAssetsCount;
    //漏洞数
    private  Long leakCount;
    //已修复漏洞数
    private  Long fixedLeakCount;
    //今日新增在线资产数
    private  Long todayAssetIpOnLineCount;
    //今日新增有漏洞资产数
    private  Long todayVulnerableAssetsCount;
    //今日新增漏洞数
    private  Long todayLeakCount;
    //今日新增已修复漏洞数
    private  Long todayFixedLeakCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
