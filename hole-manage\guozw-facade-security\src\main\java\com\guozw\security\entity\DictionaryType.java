package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 字典类型表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "dictionarytype")
public class DictionaryType extends Model<DictionaryType> implements Serializable {


    private static final long serialVersionUID = -5132407394714332031L;
    /**
     * 字典类型编码
     */
    @TableId
    private String dictionarytypecode;
    /**
     * 字典类型名称
     */
    private String dictionarytypename;
    /**
     * 字典类型描述
     */
    private String dictionarytypenote;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
