package com.guozw.common.gateway.filter;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.constant.CommonEnum;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.core.exception.CustomException;
import com.guozw.common.core.util.RedisUtils;
import com.guozw.common.gateway.config.CustomPropertiesConfig;
import com.guozw.common.gateway.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;


@Slf4j
@Component
public class CaptchaWebFilter implements WebFilter {
    @Autowired
    private CustomPropertiesConfig customPropertiesConfig;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        // 验证是不是登录请求路径
        boolean isLogin = CharSequenceUtil.containsAnyIgnoreCase(request.getURI().getPath(),
                customPropertiesConfig.getUrl_login());
        // 不是登录请求，直接向下执行其他的过滤器
        if (!isLogin) {
            return chain.filter(exchange);
        }
        try {
            // 校验验证码
            if (!checkCaptcha(request)) {
                response.setStatusCode(HttpStatus.OK);
                return CommonUtils.writeErrorMessage(response, ErrorCodeEnum.CAPTCHA_ERROR);
            }
        } catch (Exception e) {
            response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
            return CommonUtils.writeErrorMessage(response, e.getMessage());
        }
        return chain.filter(exchange);
    }

    private boolean checkCaptcha(ServerHttpRequest request) {
        boolean checkResult = false;
        String code = request.getQueryParams().getFirst(CommonConstant.CODE_PARAMETER);
        if(StrUtil.isNotBlank(code)){
            // 校验验证码的随机key值
            String random_code = request.getQueryParams().getFirst(CommonConstant.RANDOMCODE_PARAMETER);
            String key = StrUtil.format("{}_{}", CommonConstant.RANDOMCODE_PARAMETER, random_code);
            Object captchaObj = RedisUtils.get(key);
            RedisUtils.delete(key);
            if(ObjectUtil.isNotNull(captchaObj) && code.equals(captchaObj.toString())){
                checkResult = true;
            }
        }
        return checkResult;
    }
}
