<template>
    <el-container>
        <el-main>
            <!-- region 工具栏 -->
            <vxe-toolbar
                ref="toolbar"
                :refresh="{ query: loadResourceList }"
                custom
            >
                <template v-slot:buttons>
                    <el-button-group>
                        <el-button
                            type="primary"
                            icon="fa fa-plus"
                            @click="handleResourceAdd"
                              v-btnpermission="'security:resourceitem:add'"
                        >
                            新增</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-edit"
                            @click="handleResourceModify"
                              v-btnpermission="'security:resourceitem:modify'"
                        >
                            修改</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-trash"
                            @click="handleResourceDelete"
                              v-btnpermission="'security:resourceitem:del'"
                        >
                            删除</el-button
                        >
                        <el-button
                            type="primary"
                            icon="fa fa-list-alt"
                            @click="handleResourceLook"
                        >
                            查看</el-button
                        >
                    </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->

            <!-- region 表格-->
            <vxe-table
                id="resourceTable"
                ref="resourceTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                auto-resize
                resizable
                :height="tableHeight"
                :custom-config="{ storage: true }"
                :data="resources"
                :checkbox-config="{
                    labelField: 'resourcenote',
                    checkStrictly: true,
                    trigger: 'row'
                }"
                :tree-config="{ expandAll: true }"
            >
                <vxe-table-column
                    type="checkbox"
                    title="资源名称"
                    tree-node
                    align="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="resourcename"
                    title="路由名称"
                ></vxe-table-column>
                <vxe-table-column
                    field="resourceicon"
                    title="图标样式"
                    width="80"
                ></vxe-table-column>
                <vxe-table-column
                    field="resourceuri"
                    title="资源路径"
                ></vxe-table-column>
                <vxe-table-column field="resourcetypecode" title="资源类型"  width="80">
                    <template v-slot="{ row }">
                        <el-tag
                            effect="dark"
                            size="mini"
                            v-if="row.resourcetypecode == 1"
                            >菜单</el-tag
                        >
                        <el-tag
                            effect="dark"
                            type="success"
                            size="mini"
                            v-else-if="row.resourcetypecode == 2"
                            >按钮</el-tag
                        >
                    </template>
                </vxe-table-column>
                <vxe-table-column
                    field="permissiontag"
                    title="权限标识"
                ></vxe-table-column>
                <vxe-table-column
                    field="componentname"
                    title="组件路径"
                ></vxe-table-column>
                <vxe-table-column field="isenabled" title="是否启用"  width="80">
                    <template v-slot="{ row }">
                        <vxe-switch
                            v-model="row.isenabled"
                            on-label="是"
                            off-label="否"
                            :on-value="1"
                            :off-value="0"
                            @change="
                                ({ value, $event }) =>
                                    handleEnabledSwitch(
                                        value,
                                        $event,
                                        row.resourcecode
                                    )
                            "
                        ></vxe-switch>
                    </template>
                </vxe-table-column>
                <vxe-table-column field="hidden" title="是否隐藏"  width="80">
                    <template v-slot="{ row }">
                        <vxe-switch
                            v-model="row.hidden"
                            on-label="是"
                            off-label="否"
                            :on-value="'1'"
                            :off-value="'0'"
                            @change="
                                ({ value, $event }) =>
                                    handleHiddenSwitch(
                                        value,
                                        $event,
                                        row.resourcecode
                                    )
                            "
                        ></vxe-switch>
                    </template>
                </vxe-table-column>
                <vxe-table-column
                    field="createdate"
                    title="创建时间"
                ></vxe-table-column>
            </vxe-table>
            <!-- endregion -->

            <!-- region 弹窗 新增|修改 -->
            <vxe-modal
                ref="resourceFormModal"
                height="90%"
                width="600"
                position="center"
                resize
                :title="resourceFormModalTitle"
                :showFooter="!disabled"
            >
                <vxe-form
                    ref="resourceForm"
                    title-align="right"
                    title-width="100"
                    :data="resourceInfo"
                    :rules="resourceFormRules"
                    prevent-submit
                >
                    <vxe-form-item
                        title="父资源名称"
                        field="resourceparentnote"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.resourceparentnote"
                                readonly
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="资源类型"
                        field="resourcetypecode"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-select
                                v-model="resourceInfo.resourcetypecode"
                                placeholder="请输入菜单类型"
                                clearable
                                :disabled="disabled"
                                @change="$refs.resourceForm.updateStatus(scope)"
                            >
                                <vxe-option
                                    v-for="resourcetype in resourcetypes"
                                    :key="resourcetype.resourcetypecode"
                                    :value="resourcetype.resourcetypecode"
                                    :label="resourcetype.resourcetypename"
                                ></vxe-option>
                            </vxe-select>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="资源名称"
                        field="resourcenote"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.resourcenote"
                                placeholder="请输入资源名称"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="路由名称"
                        field="resourcename"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.resourcename"
                                placeholder="请输入路由名称"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="图标样式"
                        field="resourceicon"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.resourceicon"
                                placeholder="请输入图标样式"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="资源路径"
                        field="resourceuri"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.resourceuri"
                                placeholder="请输入资源路径"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>

                    <vxe-form-item
                        title="权限标识"
                        field="permissiontag"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.permissiontag"
                                placeholder="请输入权限标识 "
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="组件路径"
                        field="componentname"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.componentname"
                                placeholder="组件路径"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="资源排序"
                        field="ordernumber"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="resourceInfo.ordernumber"
                                type="number"
                                placeholder="请输入资源排序"
                                clearable
                                :disabled="disabled"
                                @input="$refs.resourceForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                </vxe-form>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.resourceFormModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleResourceFormModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import {
    deleteResource,
    enabledResource,
    hiddenResource,
    hasChildren,
    listResources,
    listResourceTypes,
    modifyResource,
    oneResource,
    saveResource
} from "@/api/security/resource";
import {
        mapGetters
    } from 'vuex'
import { listToTreeList } from "@/utils/guozw-core";

export default {
    name: "index",
    data() {
        const that = this;
        return {
            loading: true,
            tableHeight: 0, // 表格高度
            resources: [],
            resourceTreeConfig: {
                id: "resourcecode",
                parentId: "resourceparentcode",
                children: "children"
            },
            resourceInfo: {
                resourcecode: null,
                resourcename: null,
                resourcenote: null,
                resourceparentcode: null,
                resourceparentnote: null,
                resourceicon: null,
                resourceuri: null,
                resourcetypecode: null,
                permissiontag: null,
                componentname: null,
                ordernumber: null
            },
            resourceFormRules: {
                resourcenote: [
                    { required: true, message: "请输入资源名称" },
                    {
                        min: 2, max: 10,
                        message: "只能输入 2 到 10 个字符"
                    }
                ],
                resourcename: [
                    { required: true, message: "请输入路由名称" },
                    {
                        pattern: "^[a-zA-Z/-]{5,99}$",
                        message: "只能输入 5 到 99 个英文字母或/或-"
                    }
                ],
                resourceuri: [
                    { required: true, message: "请输入资源路径" },
                    {
                        pattern: "^[a-zA-Z/-]{5,99}$",
                        message: "只能输入 5 到 99 英文字母或/或-"
                    }
                ],
                resourcetypecode: [
                    { required: true, message: "请输入资源类型" }
                ],
                permissiontag: [
                    { required: false, message: "请输入权限标识" },
                    {
                        pattern: "^[a-z:]{5,99}$",
                        message:
                            "只能输入 5 到 99 个小写英文字母或:(英文下的冒号)"
                    }
                ],
                componentname: [
                    { required: true, message: "请输入组件路径" },
                    {
                        pattern: "^[a-zA-Z0-9/-]{5,99}$",
                        message: "只能输入 5 到 99 个数字、英文字母或/或"
                    }
                ],
                ordernumber: [{ required: true, message: "请输入资源排序" }]
            },
            resourcetypes: [],
            resourceFormModalTitle: null,
            disabled: false // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
        };
    },

    created() {
        this.loadResourceList();
        this.loadResourceTypeList();
    },
     mounted() {
        this.setTableHeight();
        this.windowResize();
    },
    methods: {
        // 设置表格的高度
        setTableHeight() {
            console.log('extraHeight=', this.extraHeight)
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.$refs.toolbar.$el.offsetHeight

                console.log('tableHeight=',this.tableHeight)
        },
        // 监听窗口改变
        windowResize() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    that.setTableHeight();
                    console.log(
                        // "窗口resize-----------------" + that.tableHeight
                    );
                })();
            };
        },
        // 加载资源列表
        loadResourceList() {
            this.loading = true;
            listResources()
                .then(response => {
                    this.resources = listToTreeList(
                        response.data,
                        this.resourceTreeConfig.id,
                        this.resourceTreeConfig.parentId
                    );
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },

        loadResourceTypeList() {
            listResourceTypes().then(response => {
                this.resourcetypes = response.data;
            });
        },

        // 处理资源新增按钮点击
        handleResourceAdd() {
            this.disabled = false;
            Object.keys(this.resourceInfo).forEach(
                key => (this.resourceInfo[key] = "")
            );

            // 获取表格选中的记录
            const checkedRecords = this.$refs.resourceTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                this.resourceInfo.resourceparentcode =
                    checkedRecords[0].resourcecode;
                this.resourceInfo.resourceparentnote =
                    checkedRecords[0].resourcenote;
            } else if (checkedRecords.length > 1) {
                this.$XModal.message({
                    message: "请至多选择一条记录",
                    status: "warning"
                });
                return;
            }
            this.resourceFormModalTitle = "新增资源";
            this.$refs.resourceFormModal.open();
            this.$nextTick(() => {
                this.$refs.resourceForm.clearValidate();
            });
        },

        // 处理资源修改按钮点击
        handleResourceModify() {
            this.disabled = false;
            // 获取表格选中的记录
            const checkedRecords = this.$refs.resourceTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                oneResource({
                    resourcecode: checkedRecords[0].resourcecode
                }).then(response => {
                    const resourceInfo = response.data;
                    Object.keys(this.resourceInfo).forEach(
                        key => (this.resourceInfo[key] = resourceInfo[key])
                    );
                    this.resourceFormModalTitle = "修改资源";
                    this.$refs.resourceFormModal.open();
                    this.$nextTick(() => {
                        this.$refs.resourceForm.clearValidate();
                    });
                });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理资源删除按钮点击
        handleResourceDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.resourceTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                this.$XModal
                    .confirm({
                        message: "确定要删除吗？",
                        position: "center",
                        status: "warning"
                    })
                    .then(type => {
                        if (type === "confirm") {
                            hasChildren({
                                resourcecode: checkedRecords[0].resourcecode
                            })
                                .then(response => {
                                    return response.data;
                                })
                                .then(response => {
                                    if (response) {
                                        this.$XModal.message({
                                            message:
                                                "该资源存在子资源，不能删除",
                                            status: "warning"
                                        });
                                    } else {
                                        deleteResource({
                                            resourcecode:
                                                checkedRecords[0].resourcecode
                                        }).then(response => {
                                            this.$XModal.message({
                                                message: "删除成功",
                                                status: "success"
                                            });
                                            this.loadResourceList();
                                        });
                                    }
                                });
                        }
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "info"
                });
            }
        },
        // 处理资源查询按钮点击
        handleResourceLook() {
            this.disabled = true;
            // 获取表格选中的记录
            const checkedRecords = this.$refs.resourceTable.getCheckboxRecords();
            if (checkedRecords.length == 1) {
                oneResource({
                    resourcecode: checkedRecords[0].resourcecode
                }).then(response => {
                    this.resourceInfo = response.data;
                    this.resourceFormModalTitle = "查看资源";
                    this.$refs.resourceFormModal.open();
                    this.$nextTick(() => {
                        this.$refs.resourceForm.clearValidate();
                    });
                });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        handleResourceFormModalConfirm() {
            this.$refs.resourceForm
                .validate()
                .then(() => {
                    if (this.resourceInfo.resourcecode) {
                        // 修改资源
                        modifyResource(this.resourceInfo).then(response => {
                            this.$refs.resourceFormModal.close();
                            this.$XModal.message({
                                message: "修改成功",
                                status: "success"
                            });
                            this.loadResourceList();
                        });
                    } else {
                        // 新增资源
                        // 如果资源父编码为空 则该节点为顶级节点 默认顶级节点的父编码为0
                        if (!this.resourceInfo.resourceparentcode) {
                            this.resourceInfo.resourceparentcode = this.$guozwSettings.rootId;
                        }
                        saveResource(this.resourceInfo).then(response => {
                            this.$refs.resourceFormModal.close();
                            this.$XModal.message({
                                message: "新增成功",
                                status: "success"
                            });
                            this.loadResourceList();
                        });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },

        handleEnabledSwitch(value, $event, resourcecode) {
            enabledResource({
                resourcecode: resourcecode,
                isenabled: value
            }).then(response => {
                this.$XModal.message({
                    message: value === 1 ? "启用成功" : "禁用成功",
                    status: "success"
                });
            });
        },
        handleHiddenSwitch(value, $event, resourcecode) {
            hiddenResource({
                resourcecode: resourcecode,
                hidden: value
            }).then(response => {
                this.$XModal.message({
                    message: "操作成功",
                    status: "success"
                });
            });
        }
    },
    computed: {
            ...mapGetters(['extraHeight'])
    },
    watch: {
        extraHeight() {
              this.setTableHeight()
         },

    }
};
</script>

<style scoped></style>
