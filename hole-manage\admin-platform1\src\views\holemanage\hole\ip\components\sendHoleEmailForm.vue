<template>
  <div v-show="show">
    <vxe-form
      ref="myForm"
      align="center"
      title-width="120"
      title-align="right"
      :data="emailForm"
      :loading="loading"
      :rules="rules"
      prevent-submit
      span="24"
    >
      <vxe-form-item title="系统用户" field="email_to_user">
        <template v-slot="scope">
          <el-select
            v-model="emailForm.userIdList"
            clearable
            filterable
            multiple
            placeholder="请选择系统用户"
            style="width:100%"
            @change="selectUserChange"
          >
            <el-option
              v-for="item in userEnum"
              :key="item.usercode"
              :label="item.nickname"
              :value="item.usercode"
            >
              <span style="float: left">{{ item.nickname }}</span>
              <span
                style="float: right; color: #8492a6; font-size: 13px;margin-left:10px"
              >{{ item.departmentname }}</span>
            </el-option>
          </el-select>

        </template>
      </vxe-form-item>
      <vxe-form-item title="收件人邮箱" field="email_number">
        <template v-slot="scope">
          <vxe-input
            clearable
            placeholder="收件人邮箱"
            v-model="emailForm.email_number"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="发件模板" field="config_email_template_id">
        <template v-slot="scope">
          <vxe-select
            clearable
            placeholder="发件模板"
            readonly
            transfer
            v-model.trim="emailForm.config_email_template_id"
            :options="emailTemplateEnum"
            @change="selectEmailTemplateChange"
          >
          </vxe-select>
        </template>
      </vxe-form-item>
      <vxe-form-item title="邮件主题" field="email_theme">
        <template v-slot="scope">
          <vxe-input
            clearable
            placeholder="邮件主题"
            v-model="emailForm.email_theme"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="邮件正文" field="email_text">
        <template v-slot="scope">
          <vxe-textarea
            clearable
            placeholder="邮件正文"
            v-model="emailForm.email_text"
          ></vxe-textarea>
        </template>
      </vxe-form-item>
      <vxe-form-item title="补丁库" field="patch_id_list">
        <template v-slot="scope" >
          <vxe-checkbox-group v-model="emailForm.haveBatchHoleBasicIdList">
            <vxe-checkbox v-for="item in holeBatchList" :content="item.hole_name" :label="item.hole_basic_id" :key="item.hole_basic_id" :disabled="!item.haveBatch"/>
          </vxe-checkbox-group>
        </template>
      </vxe-form-item>
      <vxe-form-item title="抄送人邮箱" field="email_copy_number">
        <template v-slot="scope">
          <vxe-input
            clearable
            placeholder="抄送人邮箱"
            v-model="emailForm.email_copy_number"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="抄送邮件主题" field="email_copy_theme">
        <template v-slot="scope">
          <vxe-input
            clearable
            placeholder="邮件主题"
            v-model="emailForm.email_copy_theme"
          ></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="抄送邮件正文" field="email_copy_text">
        <template v-slot="scope">
          <vxe-textarea
            clearable
            placeholder="邮件正文"
            v-model="emailForm.email_copy_text"
          ></vxe-textarea>
        </template>
      </vxe-form-item>
      <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
        <template v-slot="scope">
          <el-upload
            class="upload-demo"
            ref="upload"
            :action="baseApi+'/holemanage/hole/sendEmail'"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="handleChange"
            :file-list="fileList"
            :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <div slot="tip" class="el-upload__tip" style="color:red">上传文件不超过5M</div>
          </el-upload>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script>
import {validEmail} from "@/utils/validate";

export default {
  name: "sendEmailForm",
  components: {},
  data() {
    return {
      rules: {
        email_number: [{
          required: true,
           trigger: 'change',
          validator: ({itemValue}) => {
            console.log('itemValue', itemValue);
            let emails = itemValue.split(';');
            console.log('emails', emails);

            if (emails && emails.length > 0) {
              for (let email of emails) {
                if (validEmail(email)) {
                } else {
                  return new Error(
                    email + "，请重新检查邮箱地址格式！"
                  );
                }
              }
            } else {
              return new Error('请输入邮箱地址!')
            }

          }
        }],
        email_theme:[{required:true,message:'请输入邮件主题'}],
        email_text:[{required:true,message:'请输入邮件主题'}],
      },
      emailForm: {
        email_number: '',
        email_to_user: '',
        email_text: '',
        email_theme: '',
        haveBatchHoleBasicIdList: [],
        config_email_template_id: '',
        email_copy_number: '',
        email_copy_text:'',
        email_copy_theme:'',
        userIdList: []
      },
      fileList: [],
      baseApi: process.env.VUE_APP_BASE_API,
      loading: false
    }
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    //用户
    user: {
      type: Array,
      default: () => []
    },
    //邮件模板
    emailTemplate: {
      type: Array,
      default: () => []
    },
    holeBatchList: {
      type:Array,
      default: () => []
    }
  },
  created() {
    this.initData();
  },
  computed: {
    userEnum() {
      return this.user;
    } ,
    emailTemplateEnum() {
      let emailTemplateEnum = [];
      for (let emailEl of this.emailTemplate) {
        emailTemplateEnum.push({label: emailEl.template_name, value: emailEl.config_email_template_id})
      }
      return emailTemplateEnum;
    }

  },
  methods: {
    initData() {


    },
    reset() {
      this.$refs.myForm.reset();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    handleChange(file, fileList) {
      console.log("onChange", file, fileList);
      if (this.checkFile(file)) {
        // 移除校验失败的文件
        this.$refs.upload.uploadFiles.splice(
          this.$refs.upload.uploadFiles
            .length - 1,
          1
        );
        console.log("onChange", fileList);
        fileList.push(file);
        this.fileList = fileList;
        return;
      }
    },
    /* 文件校验 */
    checkFile(file) {
      // 判断文件大小是否符合要求
      if (file.size / 1024 / 1024 > 5) {
        this.$XModal.message({
          message: "单个上传文件大小不能超过 5 M",
          status: "error"
        });
        return false;
      }
      return true;
    },
    clearValidate() {
      this.$refs.myForm.clearValidate();
      this.$refs.myForm.reset();
      this.fileList = [];
      this.emailForm.userIdList = [];
    },
    //提供给父组件获取表单值
    getFormDate() {
      return new Promise((resolve, reject) => {
        this.$refs.myForm
          .validate()
          .then(() => {
            this.loading = true;
            let formData = new FormData();
            this.buildFormDate(this.emailForm, formData, null);
            console.log('fileList', this.fileList);
            if (this.fileList && this.fileList.length > 0) {
              formData.append('file', this.fileList[0].raw);
            }
            console.log( this.emailForm);
            resolve(formData)
          })
          .catch(err => {
            console.log(err);
            reject(err);
          })
          .finally(() => {
            this.loading = false;
          });
      });

    },
    getDate() {
      return new Promise((resolve, reject) => {
        this.$refs.myForm
          .validate()
          .then(() => {
            this.loading = true;
            let formData = {};
            formData.vo = this.emailForm;

            if (this.fileList && this.fileList.length > 0) {
              formData.file = this.fileList[0].raw;
            }
            console.log(formData);
            resolve(formData)
          })
          .catch(err => {
            console.log(err);
            reject(err);
          })
          .finally(() => {
            this.loading = false;
          });
      });

    },
    buildFormDate(object, formData, prefix) {
      Object.keys(object).forEach(key => {
        const value = object[key]
        if (Array.isArray(value)) {
          value.forEach((subValue, i) => {
              if (prefix) {
                formData.append(prefix + '.' + key + `[${i}]`, subValue)
              } else {
                formData.append(key + `[${i}]`, subValue)
              }
            }
          )
        } else if (Object.prototype.toString.call(object[key]) === '[object Object]') {
          if (prefix) {
            this.getFormDate(object[key], formData, prefix + '.' + key);
          } else {
            this.getFormDate(object[key], formData, key)
          }
        } else {
          if (prefix) {
            formData.append(prefix + '.' + key, object[key])
          } else {
            formData.append(key, object[key])
          }
        }
      })
    },
    //用户下拉多选框改变的时候改变收件人邮箱的值
    selectUserChange(value) {
      console.log('selectUserChange', value);
      let selectUserList = value;
      if (selectUserList && selectUserList.length > 0) {
        let email_number = '';
        let isFirst = true;
        let user = this.user;
        for (let selectUserListElement of selectUserList) {
          for (let userElement of user) {
            if (userElement.usercode == selectUserListElement) {

              if (userElement.usermail) {
                if ((!this.emailForm.email_number || !this.emailForm.email_number == '') && isFirst) {
                  isFirst = false;
                  console.log('selectUserChange', userElement);
                  email_number += userElement.usermail;
                } else {
                  if (isFirst) {
                    email_number += this.emailForm.email_number + ';' + userElement.usermail;
                    isFirst = false;
                  } else {
                    email_number += ';' + userElement.usermail;
                  }
                }
              }
            }
          }
        }
        this.emailForm.email_number = email_number;
      } else {
        this.emailForm.email_number = '';
      }


    },
    selectEmailTemplateChange(value){
      console.log('selectEmailTemplateChange', value);
      let selectValue = value.value;
      if (selectValue && selectValue != ''){
        let emailTemplateList = this.emailTemplate;
        for (let emailTemplateListElement of emailTemplateList) {
          if (selectValue === emailTemplateListElement.config_email_template_id){
            this.emailForm.email_text = emailTemplateListElement.template_text;
            this.emailForm.email_theme = emailTemplateListElement.template_theme;
            return;
          }
        }
      }else {
        this.emailForm.email_text = '';
        this.emailForm.email_theme = '';
      }
    }
  },

}
</script>

<style scoped>

</style>
