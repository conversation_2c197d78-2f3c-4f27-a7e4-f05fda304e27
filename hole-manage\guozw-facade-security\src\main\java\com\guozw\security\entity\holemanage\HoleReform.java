package com.guozw.security.entity.holemanage;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "hole_reform")
public class HoleReform extends BaseEntity<HoleReform> implements Serializable {

    private static final long serialVersionUID = -7292623027585648606L;

    @TableId
    private String hole_reform_id;

    /**
     * 漏洞表主键
     */
    private String hole_basic_id;
    /**
     * 漏洞类型1-ip漏洞  2-网站漏洞
     */
    private String hole_type;
    /**
     * 整改说明
     */
    private String reform_remark;
    /**
     * 是否已整改 1-已整改 0-未整改
     */
    private String is_reform;

    /**
     * 反馈人id
     */
    private String reform_user;

    /**
     * 反馈人联系方式
     */
    private String reform_user_phone;

    /**
     * 下发部门id
     */
    private String reform_down_dept;

    /**
     * 下发人id
     */
    private String reform_down_user;

    /**
     * 状态
     */
    private String reform_staus;
    /**
     * 时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date reform_staus_time;
    /**
     * 退回原因
     */
    private String reform_staus_reson;
    /**
     * 下发附件id
     */
    private String file_id_reform;
    /**
     * 下发附件类型
     */
    private String file_type_reform;
    /**
     * 反馈附件id
     */
    private String file_id_back;
    /**
     * 反馈附件类型
     */
    private String file_type_back;
    /**
     * 审核附件id
     */
    private String file_id_audit;
    /**
     * 审核附件类型
     */
    private String file_type_audit;
    /**
     * 要求完成时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date request_success_time;

    /**
     * 特别说明
     */
    private String special_remark;

    /**
     * 整改完成时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date back_date;

    /**
     * 整改部门
     */
    private String reform_department_id;

    /**
     * 是否系统自动下发
     */
    private Boolean is_system_auto_reform;
}
