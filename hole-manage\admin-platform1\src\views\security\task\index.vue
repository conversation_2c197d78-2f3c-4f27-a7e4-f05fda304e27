<template>
    <el-container>
        <el-main>
            <!-- region 表格工具栏 -->
            <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
                <template v-slot:buttons>
                    <el-button-group> </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->
            <vxe-table
                id="myTable"
                ref="myTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                resizable
                :custom-config="{ storage: true }"
                :data="dataList"
                :checkbox-config="{ trigger: 'row' }"
                :edit-config="{ trigger: 'dblclick', mode: 'cell' }"
                @edit-closed="editClosedEvent"
            >
                <vxe-table-column
                    field="task_id"
                    title="任务编号"
                ></vxe-table-column>
                <vxe-table-column
                    field="task_name"
                    title="任务名称"
                ></vxe-table-column>
                <vxe-table-column
                    field="task_note"
                    title="任务描述"
                ></vxe-table-column>
                <vxe-table-column field="task_cron" title="CRON表达式">
                </vxe-table-column>
                <vxe-table-column field="task_frequency" title="任务频率">
                </vxe-table-column>
                <vxe-table-column
                    field="task_clazz"
                    title="任务执行类"
                ></vxe-table-column>
                <vxe-table-column field="task_status" title="任务状态">
                    <template v-slot="{ row }">
                        <el-tag
                            effect="dark"
                            size="mini"
                            type="primary"
                            v-if="row.task_status == '启动'"
                            >启动</el-tag
                        >
                        <el-tag effect="dark" type="danger" size="mini" v-else
                            >停止</el-tag
                        >
                    </template>
                </vxe-table-column>
                <vxe-table-column field="" title="操作">
                    <template v-slot="{ row }">
                        <el-button
                            size="mini"
                            type="primary"
                            :disabled="row.task_status == '启动'"
                            @click="start(row)"
                            >启动</el-button
                        >
                        <el-button
                            size="mini"
                            type="danger"
                            :disabled="row.task_status != '启动'"
                            @click="stop(row)"
                            >停止</el-button
                        >
                    </template>
                </vxe-table-column>
            </vxe-table>
        </el-main>
    </el-container>
</template>

<script>
import { getFileCleanTaskList, startFileCleanTask, stopTask } from "@/api/security/task.js";

export default {
    name: "index",
    data() {
        return {
            loading: true,
            dataList: []
        };
    },
    created() {
        this.loadTable();
    },
    methods: {
        /* 设置查询条件表单的高度 */
        setQueryConditionFormHeight() {
            this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
        },
        /* 设置表格的高度 */
        setTableHeight() {
            this.tableHeight =
                window.innerHeight -
                this.$guozwSettings.extraHeight -
                this.queryConditionFormHeight -
                this.$refs.toolbar.$el.offsetHeight -
                this.$refs.pager.$el.offsetHeight;
        },
        /* 监听窗口改变 */
        windowResize() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    that.setTableHeight();
                    console.log(
                        // "窗口resize-----------------" + that.tableHeight
                    );
                })();
            };
        },
        /* 处理查询条件表单折叠按钮折叠 */
        handleQueryConditionFormToggleCollapse(collapse, data) {
            this.$nextTick(function() {
                this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
            });
        },
        /* 处理查询条件表单提交 */
        handleQueryConditionFormSubmit({ data }) {
            this.queryParams.pagenumber = 1;
            this.loadTable();
        },

        // 加载表格
        loadTable() {
            this.loading = true;
            getFileCleanTaskList()
                .then(response => {
                    this.dataList = response.data;
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        start(row) {
            startFileCleanTask({
                task_id: row.task_id
            }).then(response => {
                this.$XModal.message({
                    message: "启动成功",
                    status: "success"
                });
                this.loadTable();
            });
        },
        stop(row) {
            stopTask({
                task_id: row.task_id
            }).then(response => {
                this.$XModal.message({
                    message: "停止成功",
                    status: "success"
                });
                this.loadTable();
            });
        },
        editClosedEvent({ row, column }) {
            console.log(row, column);
        }
    }
};
</script>

<style scoped></style>
