<template>
  <div>
    <vxe-modal
      width="450"
      height="500"
      v-model="modalShow"
      position="centers"
      resize
      title="催促反馈通知"
      showFooter
      @close="handleModalClose"
    >
      <send-email-form ref="sendEmailForm"
                       show
                       :user="userList"
                       :email-template="emailTemplate">
      </send-email-form>

      <template v-slot:footer>
        <el-button type="" @click="handleModalClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定
        </el-button>
      </template>
    </vxe-modal>

  </div>
</template>

<script>
import SendEmailForm from "@/views/holemanage/hole/ip/components/sendEmailForm.vue";
import {saveAndSendEmail} from "@/api/holemanage/asset";

export default {
  name: 'urgeModal',
  components: {SendEmailForm},
  data() {
    return {
      modalShow: false,
    }
  },
  props: {

    userList: {
      type: Array,
      require: true
    },
    emailTemplate: {
      type: Array,
      require: true
    },

  },
  methods: {
    handleModalClose() {
      this.$refs.sendEmailForm.clearValidate();
      this.$refs.sendEmailForm.reset();
      this.modalShow = false;
    },
    handleSubmit() {
      this.$refs.sendEmailForm.getFormDate().then(res => {
        console.log('res', res)
        saveAndSendEmail(res).then(res => {
          console.log('senEmail', res)
          this.$XModal.message({
            message: '发送成功',
            status: 'success'
          })
          this.$refs.sendEmailForm.clearValidate()
          this.modalShow = false
        }).catch(err => {
          console.log(err)
        })
      })
    },
    open(departmentCode) {
      this.modalShow = true;
      console.log(this.userList);
      let find = this.userList.find(e => e.departmentcode === departmentCode);
      console.log(find);
      if (find) {
        this.$nextTick(() => {
          this.$refs.sendEmailForm.emailForm.userIdList = [find.usercode]
          this.$refs.sendEmailForm.selectUserChange([find.usercode]);
          console.log('giao')
        })

      }
    }
  }
}

</script>

<style scoped>

</style>
