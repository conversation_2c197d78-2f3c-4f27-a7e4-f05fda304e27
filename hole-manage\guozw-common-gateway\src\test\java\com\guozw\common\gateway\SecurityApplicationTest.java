package com.guozw.common.gateway;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SecurityApplicationTest {


    @Test
    public void test() {
        String password = "Y1egvf0w2ceYgV+yWOiwPQ==";
        String aesKey = "hewjk0cvb9kl2hi6";
        System.out.println(SecureUtil.aes(StrUtil.bytes(aesKey)).decryptStr(password));
    }

}
