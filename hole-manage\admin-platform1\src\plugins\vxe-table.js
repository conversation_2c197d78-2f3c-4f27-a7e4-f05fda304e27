import Vue from 'vue'
import 'xe-utils'
import VXETable from 'vxe-table'
import 'vxe-table/lib/index.css'
VXETable.setup({
    zIndex: 1500,
    // medium|small|mini
    size: 'mini',
    icon: {

        // table
        TABLE_SORT_ASC: 'vxe-icon--caret-top',
        TABLE_SORT_DESC: 'vxe-icon--caret-bottom',
        TABLE_FILTER_NONE: 'vxe-icon--funnel',
        TABLE_FILTER_MATCH: 'vxe-icon--funnel',
        TABLE_EDIT: 'vxe-icon--edit-outline',
        TABLE_TREE_LOADED: 'vxe-icon--refresh roll',
        TABLE_TREE_OPEN: 'vxe-icon--caret-right rotate90',
        TABLE_TREE_CLOSE: 'vxe-icon--caret-right',
        TABLE_EXPAND_LOADED: 'vxe-icon--refresh roll',
        TABLE_EXPAND_OPEN: 'vxe-icon--arrow-right rotate90',
        TABLE_EXPAND_CLOSE: 'vxe-icon--arrow-right',

        // toolbar
        TOOLBAR_TOOLS_REFRESH: 'fa fa-refresh',
        TOOLBAR_TOOLS_REFRESH_LOADING: 'vxe-icon--refresh roll',
        TOOLBAR_TOOLS_IMPORT: 'vxe-icon--upload',
        TOOLBAR_TOOLS_EXPORT: 'vxe-icon--download',
        TOOLBAR_TOOLS_ZOOM_IN: 'vxe-icon--zoomin',
        TOOLBAR_TOOLS_ZOOM_OUT: 'vxe-icon--zoomout',
        TOOLBAR_TOOLS_CUSTOM: 'fa fa-cog',

    },
    form: {
        titleAlign: 'right',
        titleWidth: 100,
        titleColon: true, // 是否显示标题冒号
        titleAsterisk: true // 显示必填字段红色星号
    },
    table: {
        showHeaderOverFlow: 'tooltip',
        showOverFlow: 'tooltip',
        showFooterOverFlow: 'tooltip',
        headerAlign: 'center', // 表格的header内容居中显示
        align: 'center', // 表格的body内容居中显示
        border: true,
        round: true,
        stripe: true
    },
    pager: {
        size: "small",
        background: true,
        autoHidden: false,
        perfect: true,
        pageSize: 10,
        pagerCount: 7,
        pageSizes: [10, 15, 20, 50, 100],
        layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']
    }
})

// 自定义全局的格式化处理函数
VXETable.formats.mixin({
    // 格式化下拉选项
    formatSelect({ cellValue }, list) {
        const item = list.find(item => item.value == cellValue);
        return item ? item.label : "";
    }
});


Vue.use(VXETable)
// 给 vue 实例挂载全局窗口对象，属性名称随意定义，例如：$XModal
Vue.prototype.$XModal = VXETable.modal
