<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-api/ruoyi-api-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-api/ruoyi-api-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-auth/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-auth/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-datascope/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-datascope/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-datasource/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-datasource/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-log/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-seata/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-seata/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-sensitive/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-sensitive/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-swagger/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/ruoyi-common-swagger/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-file/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-file/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-gen/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-gen/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/ruoyi-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-modules/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-visual/ruoyi-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-visual/ruoyi-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-visual/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/ruoyi-visual/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/RuoYi-Cloud-master/src/main/resources" charset="UTF-8" />
  </component>
</project>