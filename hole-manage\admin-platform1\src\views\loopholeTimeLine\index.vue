<template>
  <div class="loopholeTimeLine-container">
    <el-button type="primary" @click="drawerShow = true"
    >打开内嵌的时间线
    </el-button
    >
    <el-drawer
      title="漏洞时间线"
      :visible.sync="drawerShow"
      direction="rtl"
      size="40%"
    >
      <Timeline :timeDataList="timeDataList"/>
    </el-drawer>
    <Timeline
      :timeDataList="timeDataList"
      style="width: 300px; margin: 0 auto"
    />
  </div>
</template>

<script>
import Timeline from "./components/Timeline.vue";

export default {
  name: "LoopholeTimeLine",
  components: {Timeline},
  props: {},
  watch: {},
  computed: {},
  data() {
    return {
      drawerShow: false,
      timeDataList: [
        {
          id: 0,
          date: "2022年07月25日",
          role: "",
          operation: "发布",
          matters: "漏洞"
        },
        {
          id: 1,
          date: "2022年07月25日 11:11:11",
          role: "admin",
          operation: "创建",
          matters: "资产"
        },
        {
          id: 2,
          date: "2022年07月25日 11:11:12",
          role: "admin",
          operation: "检测到",
          matters: "漏洞"
        },
        {
          id: 3,
          date: "2022年07月25日 11:11:13",
          role: "admin",
          operation: "检测到",
          matters: "漏洞"
        },
        {
          id: 4,
          date: "2022年07月25日 11:11:14",
          role: "admin",
          operation: "开启",
          matters: "修复",
          action: true
        },
        {
          id: 5,
          date: "2022年07月25日 11:11:15",
          role: "admin",
          operation: "标记为",
          matters: "修复完成",
          action: true
        }
      ]
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.loopholeTimeLine-container {
  /deep/ .el-drawer__body {
    display: flex;
    justify-content: center;
    // margin-right: 10%;
  }
}
</style>
