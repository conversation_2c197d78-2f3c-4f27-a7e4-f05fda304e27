import request from '@/utils/request'
import qs from 'qs'


export function listRolecodes(data) {
    return request({
        url: '/security/role/listRolecodes',
        method: 'post',
        params: data
    })
}

export function listRoles(data) {
    return request({
        url: '/security/role/listRoles',
        method: 'post',
        params: data
    })
}
export function oneRole(data) {
    return request({
        url: '/security/role/oneRole',
        method: 'post',
        params: data
    })
}
export function saveRole(data) {
    return request({
        url: '/security/role/saveRole',
        method: 'post',
        params: data
    })
}
export function roleExists(data) {
    return request({
        url: '/security/role/roleExists',
        method: 'post',
        params: data
    })
}
export function modifyRole(data) {
    return request({
        url: '/security/role/modifyRole',
        method: 'post',
        params: data
    })
}
export function deleteRole(data) {
    return request({
        url: '/security/role/deleteRole',
        method: 'post',
        params: data
    })
}
export function saveRoleResourceRelation(data) {
    return request({
        url: '/security/role/saveRoleResourceRelation',
        method: 'post',
        // params: data
        data: qs.stringify(data)
    })
}
