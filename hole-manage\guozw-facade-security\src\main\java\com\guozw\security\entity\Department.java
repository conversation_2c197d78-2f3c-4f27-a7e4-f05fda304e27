package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 部门表
 * <AUTHOR>
 * @date 2020/7/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "department")
public class Department extends BaseEntity<Department> implements Serializable {
    private static final long serialVersionUID = 5225377389172539453L;


    /**
     * 部门编码
     */
    @TableId
    private String departmentcode;

    /**
     * 部门名称
     */
    private String departmentname;
    /**
     * 部门简称
     */
    private String departmentshortname;
    /**
     * 部门英文名称
     */
    private String departmentenname;
    /**
     * 部门联系人
     */
    private String departmentcontacter;
    /**
     * 部门电话
     */
    private String departmentphone;
    /**
     * 部门传真
     */
    private String departmentfax;
    /**
     * 部门描述
     */
    private String departmentnote;
    /**
     * 部门类型编码
     */
    private String departmenttypecode;
    /**
     * 父部门编码
     */
    private String departmentparentcode;
    /**
     * 是否启用 0-否 1-是
     */
    private Integer isenabled;
    /**
     * 是否删除 0-否 1-是
     */
    private Integer isdelete;

    /**
     * 排序序号
     */
    private Integer ordernumber;

    /**
     * 资产组网段 用户表单输入的网段信息
     */
    private String zczwd;
    /**
     * 默认的角色编码
     * 新建用户的时候，为用户分配默认的角色
     */
    private String defaultrolecode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }


}
