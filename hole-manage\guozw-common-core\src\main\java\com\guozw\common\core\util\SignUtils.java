package com.guozw.common.core.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.constant.SignConstant;

import javax.servlet.http.HttpServletRequest;

/**
 * API接口签名工具类
 */
public class SignUtils {


    /**
     * 校验Header上的参数
     * 验证是否传入值
     * 有个很重要的一点，就是对此请求进行时间验证，如果大于10分钟表示此链接已经超时，防止别人盗取这个链接去再次请求，这个就是防止盗链
     * @param request
     * @return
     */
    public static boolean verifyHeaderParams(HttpServletRequest request) {

        // 应用id
        String appId = request.getParameter(SignConstant.APP_ID);
        // 时间戳
        String timeStamp = request.getParameter(SignConstant.TIME_STAMP);
        // 临时流水号
        String nonce = request.getParameter(SignConstant.NONCE);
        // 签名
        String sign = request.getParameter(SignConstant.SIGN);
        if (StrUtil.hasBlank(appId, timeStamp, nonce, sign)) {
            return Boolean.FALSE;
        }
        // 毫秒
        long diff = DateUtil.current() - Long.parseLong(timeStamp);
        // 大于10分钟
        if (diff > 1000 * 60 * 10) {
            return Boolean.FALSE;
        }


        return Boolean.TRUE;
    }
}
