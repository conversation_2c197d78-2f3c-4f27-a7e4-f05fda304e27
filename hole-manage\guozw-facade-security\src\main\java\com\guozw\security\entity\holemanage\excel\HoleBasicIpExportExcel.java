package com.guozw.security.entity.holemanage.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guozw.common.core.constant.DictEnum;
import com.guozw.security.entity.Dictionary;
import com.guozw.security.vo.holemanage.HoleBasicIpVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class HoleBasicIpExportExcel {

    @ExcelProperty("ip地址")
    private String asset_ip;

    @ExcelProperty("隶属关系")
    private String departmentname;

    @ExcelProperty("设备类型")
    private String device_type;

    @ExcelProperty("漏洞名称")
    private String hole_name;

    @ExcelProperty("漏洞等级")
    private String hole_level;

    @ExcelProperty("发现状态")
    private String hole_discovery_status;

    @ExcelProperty("发现方式")
    private String hole_discovery_method;

    @ExcelProperty("验证状态")
    private String hole_test_status;

    @ExcelProperty("复测状态")
    private String hole_retest_status;

    @ExcelProperty("修复状态")
    private String hole_repair_status;

    @ExcelProperty("关闭状态")
    private String hole_close_status;


    public HoleBasicIpExportExcel(HoleBasicIpVO holeBasicIpVO, Map<String,List<Dictionary>> dictionaryMap) {
        BeanUtils.copyProperties(holeBasicIpVO, this);
        //设备类型
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.DEVICT_TYPE.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getDevice_type())){
                this.setDevice_type(dictionary.getDictionaryname());
                break;
            }
        }
        //漏洞等级
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_LEVEL.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_level())){
                this.setHole_level(dictionary.getDictionaryname());
                break;
            }
        }
        //发现方式
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_DISCOVERY_METHOD.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_discovery_method())){
                this.setHole_discovery_method(dictionary.getDictionaryname());
                break;
            }
        }
        //验证状态
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_TEST_STATUS.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_test_status())){
                this.setHole_test_status(dictionary.getDictionaryname());
                break;
            }
        }
        //发现状态
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_DISCOVERY_STATUS.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_discovery_status())){
                this.setHole_discovery_status(dictionary.getDictionaryname());
                break;
            }
        }
        //复测状态hole_retest_status
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_RETEST_STATUS.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_retest_status())){
                this.setHole_retest_status(dictionary.getDictionaryname());
                break;
            }
        }
        //修复状态 hole_repair_status
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_REPAIR_STATUS.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_repair_status())){
                this.setHole_repair_status(dictionary.getDictionaryname());
                break;
            }
        }
        //关闭状态 hole_close_status
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.HOLE_CLOSE_STATUS.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getHole_close_status())){
                this.setHole_close_status(dictionary.getDictionaryname());
                break;
            }
        }


    }

}
