<template>
  <div>
    <vxe-modal
      :height="myModalHeight"
      :width="modalWidth"
      position="centers"
      resize
      title="整改通知审核"
      :loading="loading"
      v-model="modalShow"
      @close="handleModalClose()"
      showFooter>
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleTabClick">
        <el-tab-pane label="漏洞信息" name="1">
          <el-container>
            <el-header height="auto" style="margin: 1px; display: inline-block;">
              <div style="height: 100%;width: 100%;display: inline-block;padding: 5px 0px">
                <div class="headerText" >
                  <span>{{ formatterHoleLevel }}</span>
                  <span>{{ holeBasicInfo.hole_name }}</span>
                </div>

                <div class="box" style="height: auto">
                  <div class="boxHeader">
                    &emsp;
                    <span style="align-self:center"> 发现方式:{{ formatterHoleDiscoveryMethod }}</span>
                    &emsp;
                    &emsp;
                    <span style="align-self:center">漏洞类型:{{ formatterHoleType }}</span>
                  </div>
                  <el-form label-position="right">
                    <el-row :gutter="20">
                      <el-col :span="6" :offset="3">
                        <div class="grid-content bg-purple">
                          <el-form-item label="影响当前资产:">
                            <span>{{
                                holeReformData.departmentname
                              }}/{{
                                holeReformData.hole_type === '1' ? holeBasicInfo.asset_ip : holeBasicInfo.asset_website_name
                              }}</span>
                          </el-form-item>
                        </div>
                      </el-col>
                      <el-col :span="6" :offset="6">
                        <div class="grid-content bg-purple">
                          <div class="grid-content bg-purple">
                            <el-form-item label="首次发现时间:">
                              <span>{{ holeBasicInfo.createdate }}</span>
                            </el-form-item>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="6" :offset="3">
                        <div class="grid-content bg-purple">
                          <el-form-item label="最近发现时间:">
                            <span>{{ holeBasicInfo.updatedate }}</span>
                          </el-form-item>
                        </div>
                      </el-col>
                      <el-col :span="6" :offset="6">
                        <div class="grid-content bg-purple">
                          <div class="grid-content bg-purple">
                            <el-form-item label="发现人:">
                              <span>admin</span>
                            </el-form-item>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </div>


            </el-header>
            <el-main style=" display: inline-block;padding: 0px 20px;height: auto">
              <div style="margin: 0px 50px">
                <el-tabs v-model="detailActive" type="border-card">
                  <el-tab-pane label="基础信息" name="1">
                    <basic-info-tab ref="basicInfoTab" :hole-basic-ip-info="holeBasicInfo"></basic-info-tab>
                  </el-tab-pane>
                  <!--          <el-tab-pane label="生命周期" name="2">
                              <life-cycle-tab ref="liftCycleTab" :time-data-list="holeBasicIpInfo.holePlanBackVOList"
                                              :plan-back-type-enum="enumData.planBackTypeEnum"
                                              :test-status-enum="enumData.testStatusEnum"
                                              :retest-status-enum="enumData.retestStatusEnum"
                                              :repair-status-enum="enumData.repairStatusEnum"
                                              :close-status-enum="enumData.closeStatusEnum"></life-cycle-tab>
                            </el-tab-pane>
                            <el-tab-pane label="检查信息" name="3">
                              <check-info-tab ref="checkInfoTab" :hole-basic-ip-info="holeBasicIpInfo" :hole-type="holeType"></check-info-tab>
                            </el-tab-pane>-->
                </el-tabs>
              </div>


            </el-main>
          </el-container>
        </el-tab-pane>
        <el-tab-pane label="整改信息" name="2">
          <div style="width: 50%;margin-left: 25%">
            <vxe-form
              align="center"
              title-width="120"
              title-align="right"
              prevent-submit
              span="20"
            >
              <vxe-form-item title="IP/网站名称" field="">
                <template v-slot="scope">
                  {{holeReformData.hole_type === '1' ? holeBasicInfo.asset_ip : holeBasicInfo.asset_website_name}}
                </template>
              </vxe-form-item>

              <vxe-form-item title="整改说明" field="reform_remark">
                <template v-slot="scope">
                  <vxe-input disabled v-model="disableFormData.reform_remark" placeholder="整改说明"></vxe-input>
                </template>
              </vxe-form-item>
              <vxe-form-item title="整改人" field="reform_user" span="10">
                <template v-slot="scope">
                  <el-select
                    disabled
                    v-model="disableFormData.reform_user"
                    clearable
                    filterable
                    placeholder="整改人"
                    style="width:100%"
                  >
                    <el-option
                      v-for="item in userEnum"
                      :key="item.usercode"
                      :label="item.nickname"
                      :value="item.usercode"
                    >
                      <span style="float: left">{{ item.nickname }}</span>
                      <span
                        style="float: right; color: #8492a6; font-size: 13px;margin-left:10px"
                      >{{ item.departmentname }}</span
                      >
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
              <vxe-form-item title="整改人联系方式" field="reform_user_phone" span="10">
                <template v-slot="scope">
                  <vxe-input disabled v-model="disableFormData.reform_user_phone" placeholder="整改人联系方式"></vxe-input>
                </template>
              </vxe-form-item>
              <vxe-form-item title="是否已整改" field="is_reform" span="10">
                <template v-slot="scope">
                  <el-select
                    v-model="disableFormData.is_reform"
                    disabled
                    clearable
                    filterable
                    placeholder="是否已整改"
                    style="width:100%"
                  >
                    <el-option
                      key="0"
                      label="否"
                      value="0"
                    >
                      否
                    </el-option>
                    <el-option
                      key="1"
                      label="是"
                      value="1"
                    >
                      是
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>
              <vxe-form-item title="整改完成时间" field="back_date" span="10">
                <template v-slot="scope">
                  <vxe-input disabled v-model="disableFormData.back_date" clearable placeholder="日期选择" type="date">
                  </vxe-input>
                </template>
              </vxe-form-item>
              <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
                <template v-slot="scope">
                  <el-upload
                    disabled
                    class="upload-demo"
                    ref="upload"
                    :action="''"
                    :on-preview="(file, fileList) => handlePreview(file, 1)"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 1)"
                    :on-change="(file, fileList) => handleChange(file, fileList, 1)"
                    :file-list="planBackFileList"
                    :auto-upload="false">
                    <el-button slot="trigger" size="small" type="primary" disabled>选取文件</el-button>
                    <div slot="tip" class="el-upload__tip" style="color:red">上传文件不超过5M</div>
                  </el-upload>
                  <el-tag
                    type="success"
                    v-if="holeReformData.fileBack"
                    @click="handleDownloadFile(holeReformData.fileBack)"
                  >
                    {{holeReformData.fileBack.file_real_name}}  点击下载
                  </el-tag>
                </template>
              </vxe-form-item>
            </vxe-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="审批信息" name="3">

          <div style="width: 50%;margin-left: 20%">
            <vxe-form
              ref="myForm"
              align="center"
              title-width="120"
              title-align="right"
              :data="myFormData"
              :rules="rules"
              prevent-submit

              span="20"
            >
              <vxe-form-item title="审核状态" field="reform_staus">
                <template v-slot="scope">
                  <el-select
                    :disabled="disableForm"
                    v-model="myFormData.reform_staus"
                    clearable
                    filterable
                    placeholder="审核状态"
                    @change="handleStatusChange"
                    style="width:100%"
                  >
                    <el-option
                      key="2"
                      label="退回"
                      value="2"
                    >
                      退回
                    </el-option>
                    <el-option
                      key="3"
                      label="审核通过"
                      value="3"
                    >
                      审核通过
                    </el-option>
                  </el-select>
                </template>
              </vxe-form-item>

              <vxe-form-item title="退回原因" field="reform_staus_reson" v-if="myFormData.reform_staus === '2'">
                <template v-slot="scope">
                  <vxe-textarea :disabled="disableForm" v-model="myFormData.reform_staus_reson" placeholder="退回原因"></vxe-textarea>
                </template>
              </vxe-form-item>
              <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
                <template v-slot="scope">
                  <el-upload
                    :disabled="disableForm"
                    class="upload-demo"
                    ref="upload"
                    :action="''"
                    :on-preview="(file, fileList) => handlePreview(file, 1)"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 1)"
                    :on-change="(file, fileList) => handleChange(file, fileList, 1)"
                    :file-list="planBackFileList"
                    :auto-upload="false">
                    <el-button slot="trigger" size="small" type="primary" :disabled="disableForm" @click="uploadButtonCheck">选取文件</el-button>
                    <div slot="tip" class="el-upload__tip" style="color:red">上传文件不超过5M</div>
                  </el-upload>
                  <el-tag
                    type="success"
                    v-if="holeReformData.fileAudit"
                    @click="handleDownloadFile(holeReformData.fileAudit)"
                  >
                    {{holeReformData.fileAudit.file_real_name}}  点击下载
                  </el-tag>
                </template>

              </vxe-form-item>

              <vxe-form-item>
                <el-divider content-position="left">通知方式</el-divider>
              </vxe-form-item>
              <vxe-form-item align="left" title="系统通知">
                <vxe-checkbox :disabled="disableForm" v-model="systemCheckBox" @change="(value)=>checkBoxChange(value,1)"></vxe-checkbox>
              </vxe-form-item>

              <vxe-form-item title="系统用户" field="workorderWarnVO.userIdList" v-if="systemCheckBox">
                <template v-slot="scope">
                  <el-select
                    v-model="myFormData.workorderWarnVO.userIdList"
                    clearable
                    filterable
                    multiple
                    placeholder="请选择系统用户"
                    style="width:100%"
                    @change="handleSelectChange"
                  >
                    <el-option
                      v-for="item in userEnum"
                      :key="item.usercode"
                      :label="item.nickname"
                      :value="item.usercode"
                    >
                      <span style="float: left">{{ item.nickname }}</span>
                      <span
                        style="float: right; color: #8492a6; font-size: 13px;margin-left:10px"
                      >{{ item.departmentname }}</span
                      >
                    </el-option>
                  </el-select>

                </template>
              </vxe-form-item>
              <vxe-form-item title="消息标题" field="workorderWarnVO.workorder_warn_name" v-if="systemCheckBox">
                <template v-slot="scope">
                  <vxe-input v-model="myFormData.workorderWarnVO.workorder_warn_name"
                             placeholder="消息标题"></vxe-input>
                </template>
              </vxe-form-item>
              <vxe-form-item title="消息内容" field="workorderWarnVO.workorder_warn_content" v-if="systemCheckBox">
                <template v-slot="scope">
                  <vxe-textarea v-model="myFormData.workorderWarnVO.workorder_warn_content"
                             placeholder="消息内容"></vxe-textarea>
                </template>
              </vxe-form-item>

              <vxe-form-item align="left" title="邮箱通知">
                <vxe-checkbox v-model="emailCheckBox" :disabled="disableForm" @change="(value)=>checkBoxChange(value,2)"></vxe-checkbox>
              </vxe-form-item>
              <vxe-form-item>
                <send-email-form ref="sendEmailForm" :show="emailCheckBox" :user="userList"
                                 :email-template="emailTemplate"></send-email-form>
              </vxe-form-item>

            </vxe-form>
          </div>

        </el-tab-pane>
      </el-tabs>
      <template v-slot:footer>
        <el-button type="" @click="handleModalClose">取消</el-button>
        <el-button type="primary" v-if="activeName === '3' && !disableForm" :loading="loading" @click="submitForm">确定
        </el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import {parseTime} from '@/utils'
import {doReform} from '@/api/holemanage/asset'
import SendEmailForm from "@/views/holemanage/hole/ip/components/sendEmailForm";
import holeBasicIpDetail from "@/views/holemanage/hole/ip/holeBasicIpDetail";
import basicInfoTab from "@/views/holemanage/hole/ip/components/basicIpDetailTabComponents/basicInfoTab";
import {copyProperty} from "@/utils/guozw-core";
import {downloadFile} from "@/utils/file";

export default {
  name: "holePlanBackModal",
  components: {SendEmailForm, holeBasicIpDetail, basicInfoTab},
  data() {
    return {
      myFormData: {
        workorderWarnVO: {
          workorder_warn_name: '',
          workorder_warn_content: '',
          userIdList: [],
        },
        operationType: '',
        reform_staus: '',
        reform_staus_reson: ''
      },
      disableFormData:{
        reform_remark:'',
        reform_user: '',
        reform_user_phone: '',
        is_reform: '',
        back_date: '',
      },
      rules: {
        'workorderWarnVO.userIdList': [{required: true, message: "必填字段", trigger: 'change'}],
        reform_staus: [{required: true, message: "请选择审核状态", trigger: 'change'}],
      },
      loading: false,
      planBackFileList: [],
      systemCheckBox: false,
      emailCheckBox: false,
      activeName: '1',
      myModalHeight: '99%',
      modalWidth: '99%',
      detailActive: '1'
    }
  },
  props: {
    modalShow: {
      type: Boolean,
      default: false
    },
    userList: {
      type: Array,
      require: true
    },
    emailTemplate: {
      type: Array,
      require: true
    },
    holeReformData: {
      type: Object,
    },
    holeLevelEnum: {
      type: Array
    },
    holeTypeEnum: {
      type: Array
    },
    holeDiscoveryMethodEnum: {
      type: Array
    },
    testStatusEnum: {
      type: Array
    },
    retestStatusEnum: {
      type: Array
    },
    closeStatusEnum: {
      type: Array
    },
    repairStatusEnum: {
      type: Array
    },
    disableForm:{
      type: Boolean,
      default: false
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      console.log('giao')
      console.log(this.holeReformData);
      this.disableFormData.reform_user= this.holeReformData.reform_user;
      this.disableFormData.reform_user_phone= this.holeReformData.reform_user_phone;
      this.disableFormData.is_reform= this.holeReformData.is_reform;
      this.disableFormData.back_date=  this.holeReformData.back_date;
      this.disableFormData.reform_remark = this.holeReformData.reform_remark;
      this.myFormData.operationType= '2';
      if (this.disableForm){
        this.myFormData.reform_staus = this.holeReformData.reform_staus;
        this.myFormData.reform_staus_reson = this.holeReformData.reform_staus;
      }
    },
    handleModalClose() {
      this.$refs.myForm.clearValidate();
      this.$refs.myForm.reset();
      this.$refs.sendEmailForm.clearValidate();
      this.$refs.sendEmailForm.reset();
      this.systemCheckBox = false;
      this.emailCheckBox = false;
      this.myFormData.workorderWarnVO.userIdList = [];
      this.planBackFileList = [];
      this.myFormData.request_success_time = new Date();
      this.$emit('close');
    },
    handleRemove(file, fileList, value) {
      console.log(file, fileList);
    },
    handlePreview(file, value) {
      console.log(file);
    },
    handleChange(file, fileList, value) {
      console.log("onChange", file, fileList);
      if (this.checkFile(file)) {
        // 移除校验失败的文件
        this.$refs.upload.uploadFiles.splice(
          this.$refs.upload.uploadFiles
            .length - 1,
          1
        );
        console.log("onChange", fileList);
        fileList.push(file);
        if (value === 1) {
          this.planBackFileList = fileList;
        } else {

        }
        return;
      }
    },
    /* 文件校验 */
    checkFile(file) {
      // 判断文件大小是否符合要求
      if (file.size / 1024 / 1024 > 5) {
        this.$XModal.message({
          message: "单个上传文件大小不能超过 5 M",
          status: "error"
        });
        return false;
      }

      return true;
    },
    checkBoxChange(checked, value) {
      if (checked.checked) {
        this.$set(this.rules, 'workorderWarnVO.userIdList', [{required: true, message: "必填字段", trigger: 'change'}]);
      } else {
        delete this.rules['workorderWarnVO.userIdList'];
      }

      console.log(checked);
      /*if (this.systemCheckBox && this.emailCheckBox) {
        this.myModalHeight = '70%';
      } else if (this.systemCheckBox) {
        this.myModalHeight = '55%';
      } else if (this.emailCheckBox) {
        this.myModalHeight = '65%';
      } else {
        this.myModalHeight = '50%';
      }*/
      if (value == 1) {
        if (checked.checked) {
          let status = '';
          if(this.myFormData.reform_staus === '2'){
            status = '退回'
          }else if (this.myFormData.reform_staus === '3'){
            status = '审核通过'
          }else{
            status = '';
          }
          this.myFormData.workorderWarnVO.workorder_warn_name = this.holeBasicInfo.hole_name;
          this.myFormData.workorderWarnVO.workorder_warn_content = '你好，你单位有一条整改通知' + status;
          this.myFormData.workorderWarnVO.userIdList = [this.holeReformData.reform_user]
        } else {
          this.myFormData.workorderWarnVO.workorder_warn_name = '';
          this.myFormData.workorderWarnVO.workorder_warn_content = '';
          this.myFormData.workorderWarnVO.userIdList = []

        }
      } else {
        if (checked.checked) {
          console.log('giao')
          console.log(this.myFormData.workorderWarnVO.userIdList);
          this.$refs.sendEmailForm.emailForm.userIdList = this.myFormData.workorderWarnVO.userIdList;
          this.$refs.sendEmailForm.selectUserChange(this.myFormData.workorderWarnVO.userIdList);
        } else {
          this.$refs.sendEmailForm.clearValidate();
          this.$refs.sendEmailForm.reset();
        }
      }
    },
    //提交表单
    submitForm() {
      this.loading = true;
      this.$refs.myForm
        .validate()
        .then(async () => {

          let formData = new FormData();
          let myFormData = this.myFormData;
          console.log(myFormData);

          if (this.emailCheckBox) {
            let emailVO = await this.$refs.sendEmailForm.getDate();
            myFormData.holeEmailVO = emailVO.vo;
            if (emailVO.file) {
              formData.append('emailFile', emailVO.file);
            }
          }
          if (!this.systemCheckBox) {
            this.myFormData.workorderWarnVO.workorder_warn_name = '';
            this.myFormData.workorderWarnVO.workorder_warn_content = '';
            this.myFormData.workorderWarnVO.userIdList = [];
            this.myFormData.workorderWarnVO.params = {};
          } else {
            let url = {
              path: '/holemanage/hole/holeRectificationBack/index',
              /*query: {
                asset_ip_id: this.holeBasicInfo.asset_ip_id
              }*/
            }
            this.myFormData.workorderWarnVO.workorder_warn_url = JSON.stringify(url);
          }
          if(this.holeReformData.hole_type === '1'){
            myFormData.hole_basic_id = this.holeBasicInfo.hole_basic_ip_id;
          }else if(this.holeReformData.hole_type === '2'){
            myFormData.hole_basic_id = this.holeBasicInfo.hole_basic_website_id;
          }
          myFormData.hole_type = this.holeReformData.hole_type;
          myFormData.hole_reform_id = this.holeReformData.hole_reform_id;
          console.log(myFormData);
          this.getFormDate(myFormData, formData, null);
          if (this.planBackFileList && this.planBackFileList.length > 0) {
            formData.append('reformFile', this.planBackFileList[0].raw);
          }
          doReform(formData).then(res => {
            this.$XModal.message({
              message: "操作成功",
              status: "success"
            });
            this.handleModalClose()
            this.$emit("refreshTable");
          }).finally(e => {
            this.loading = false;
          })
        })
        .catch(err => {
          this.loading = false;
          console.log(err);
        });
    },
    handleSelectChange(value) {
      if (this.emailCheckBox) {
        console.log(this.myFormData.workorderWarnVO.userIdList);
        this.$refs.sendEmailForm.emailForm.userIdList = this.myFormData.workorderWarnVO.userIdList;
        this.$refs.sendEmailForm.selectUserChange(this.myFormData.workorderWarnVO.userIdList);
      }
    },
    getFormDate(object, formData, prefix) {
      Object.keys(object).forEach(key => {
        const value = object[key]
        if (value == null || value == undefined) {
          return
        }
        if (Array.isArray(value)) {
          if (value.length == 0) {
            return;
          }
          console.log('giao');
          console.log(value)
          value.forEach((subValue, i) => {
              if (prefix) {
                formData.append(prefix + '.' + key + `[${i}]`, subValue)
              } else {
                formData.append(key + `[${i}]`, subValue)
              }
            }
          )
        } else if (Object.prototype.toString.call(object[key]) === '[object Object]') {
          if (Object.keys(value).length == 0) {
            return;
          }
          if (prefix) {
            this.getFormDate(object[key], formData, prefix + '.' + key);
          } else {
            this.getFormDate(object[key], formData, key)
          }
        } else {
          if (value == '') {
            return;
          }
          if (prefix) {
            formData.append(prefix + '.' + key, object[key])
          } else {
            formData.append(key, object[key])
          }
        }
      })
    },
    //处理改变日期的时候改变消息通知的值
    handleDateChange(value) {
      console.log('handleDateChange', value);
      if (this.systemCheckBox) {
        console.log('giao')
        console.log(this.myFormData.workorderWarnVO)
        this.myFormData.workorderWarnVO.workorder_warn_content = '你好，你单位新增漏洞1个，请尽快进行核实和整改，' + (this.myFormData.request_success_time != null ? '并于' + parseTime(this.myFormData.request_success_time, '{y}-{m}-{d}') + '前在系统中' : '请于近期') + '反馈整改情况'
      }
    },
    //状态改变时设置rules
    handleStatusChange(value){
      console.log('handleStatusChange');
      console.log('handleStatusChange',value);
      if (value === '2'){
        this.$set(this.rules, 'reform_staus_reson', [{required: true, message: '请输入退回原因'}]);
      }else {
        this.$set(this.rules, 'reform_staus_reson', null);

      }
      if (this.systemCheckBox) {
        let status = '';
        if(this.myFormData.reform_staus === '2'){
          status = '退回'
        }else if (this.myFormData.reform_staus === '3'){
          status = '审核通过'
        }else{
          status = '';
        }
        this.myFormData.workorderWarnVO.workorder_warn_content = '你好，你单位有一条整改通知' + status;
      }
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      console.log(tab);
      console.log(event);
    },
    //处理下载文件
    handleDownloadFile(value){
      downloadFile(value);
    }
  },
  uploadButtonCheck(){
    if (this.holeReformData.fileAudit){
      this.$XModal.message({
        message: '请注意，上传文件会覆盖之前的文件！',
        status:'warning'
      })
    }

  },
  computed: {
    userEnum() {
      console.log('userEnum')
      console.log(this.userList)
      return this.userList;
    },
    holeBasicInfo() {
      if (this.holeReformData.hole_type === '1' ){
        if (this.holeReformData.holeBasicIp){
          return this.holeReformData.holeBasicIp;
        }else {
          return new Object();
        }
      }else {

        if (this.holeReformData.holeBasicWebsite){
          return this.holeReformData.holeBasicWebsite;
        }else {
          return new Object();
        }
      }
    },
    formatterHoleLevel: function () {
      console.log('1', this);
      let levelEnum = this.holeLevelEnum;
      if (levelEnum && levelEnum.length > 0) {
        for (let levelEnumElement of levelEnum) {
          if (levelEnumElement.value === this.holeBasicInfo.hole_level) {
            return levelEnumElement.label;
          }
        }
      } else {
        return this.holeBasicInfo.hole_level;
      }
    },
    formatterHoleDiscoveryMethod: function () {
      console.log('1', this);
      let discoveryMethodEnum = this.holeDiscoveryMethodEnum;
      if (this.holeBasicInfo.hole_discovery_method) {
        if (discoveryMethodEnum && discoveryMethodEnum.length > 0) {
          for (let d of discoveryMethodEnum) {
            if (d.value === this.holeBasicInfo.hole_discovery_method) {
              return d.label;
            }
          }
        } else {
          return this.holeBasicInfo.hole_discovery_method;
        }
      } else {
        return '无';
      }
    },
    formatterHoleType: function () {
      console.log('1', this);
      let holeTypeEnum = this.holeTypeEnum;
      if (this.holeBasicInfo.hole_type) {
        if (holeTypeEnum && holeTypeEnum.length > 0) {
          for (let d of holeTypeEnum) {
            if (d.value === this.holeBasicInfo.hole_type) {
              return d.label;
            }
          }
        } else {
          return this.holeBasicInfo.hole_type;
        }
      } else {
        return '无';
      }
    },

  },
  watch: {
    holeReformData(newVal, oldVal) {
      console.log("newVal", newVal);

      this.initData();
    }
  }
}
</script>

<style scoped>
.box {
  border: solid 0.2px #EBEEF5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 20px 50px 50px;
}

.headerText {
  margin-left: 50px;
  margin-top: 20px;
  font-weight: bold;
  font-size: 20px
}

.boxHeader {
  height: 50px;
  background: #DCDFE6;
  margin: 30px 30px;
  display: flex;
}
</style>
