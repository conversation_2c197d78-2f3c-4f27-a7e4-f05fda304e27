import request from '@/utils/request'

/**
 * 工具管理
 */

// 列表数据
export function getTableData(data) {
  return request({
    url: '/holemanage/tool/pageToolBasic',
    method: 'post',
    data
  })
}

// 保存
export function saveToolBasic(data) {
  return request({
    url: '/holemanage/tool/saveToolBasic',
    method: 'post',
    data
  })
}

// 删除
export function deleteToolBasic(data) {
  return request({
    url: '/holemanage/tool/deleteToolBasic',
    method: 'post',
    data
  })
}

// 扫描工具状态
export function getToolStatus(data) {
  return request({
    url: '/holemanage/tool/getToolStatusList',
    method: 'post',
    data
  })
}
// 扫描工具状态
export function listToolBasic() {
  return request({
    url: '/holemanage/tool/listToolBasic',
    method: 'post',
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 任务管理
 */

// 表格数据
export function pageTaskBasic(data) {
  return request({
    url: '/holemanage/task/pageTaskBasic',
    method: 'post',
    data
  })
}

//  新增
export function addTaskBasic(data) {
  return request({
    url: '/holemanage/task/addTaskBasic',
    method: 'post',
    data
  })
}

//  编辑
export function modifyTaskBasic(data) {
  return request({
    url: '/holemanage/task/modifyTaskBasic',
    method: 'post',
    data
  })
}

//  获取任务详细信息
export function oneTaskBasic(data) {
  return request({
    url: '/holemanage/task/oneTaskBasic',
    method: 'post',
    data
  })
}

//  删除
export function deleteTaskBasic(data) {
  return request({
    url: '/holemanage/task/deleteTaskBasic',
    method: 'post',
    data
  })
}

//  停止
export function stopTaskBasic(data) {
  return request({
    url: '/holemanage/task/stopTaskBasic',
    method: 'post',
    data
  })
}

//  启动
export function startTask(data) {
  return request({
    url: '/holemanage/task/startTaskBasic',
    method: 'post',
    data
  })
}

//  任务调度历史数据
export function pageTaskPlanDispatch(data) {
  return request({
    url: '/holemanage/task/pageTaskPlanDispatch',
    method: 'post',
    data
  })
}

// 子任务列表
export function listApiTask(data) {
  return request({
    url: '/holemanage/task/listApiTask',
    method: 'post',
    data
  })
}

// 子任务历史数据
export function pageApiTaskDispatch(data) {
  return request({
    url: '/holemanage/task/pageApiTaskDispatch',
    method: 'post',
    data
  })
}
