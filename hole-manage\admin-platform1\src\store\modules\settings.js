import variables from '@/styles/element-variables.scss'
import menu_variables from '@/styles/variables.scss'
import defaultSettings from '@/settings'

const { showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings

const state = {
  locationY: localStorage.getItem("locationY") || 250,
  // theme: variables.theme,
  theme: localStorage.getItem('menuBg') || menu_variables.menuBg,
  showSettings: showSettings,
  tagsView: tagsView,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  sidebarBg: {
    textColor: menu_variables.menuText,
    activeTextColor: menu_variables.menuActiveText,

    menuBg: localStorage.getItem('menuBg') || '#009688',
    activeBgColor: localStorage.getItem('activeBgColor') || '#009688'
  },
  menuStyle: localStorage.getItem('menuStyle') || 'vertical', // vertical horizontal hybrid
  showNavHamburger_IN_hybrid: JSON.parse(localStorage.getItem('showNavHamburger_IN_hybrid')) === null ? true : JSON.parse(localStorage.getItem('showNavHamburger_IN_hybrid')),

  extraHeight: localStorage.getItem('menuStyle') === 'vertical' ? 85
    : localStorage.getItem('menuStyle') === 'horizontal' ? 135
      : (localStorage.getItem('menuStyle') === 'hybrid' && localStorage.getItem('showNavHamburger_IN_hybrid') === 'true') ? 135
        : (localStorage.getItem('menuStyle') === 'hybrid' && localStorage.getItem('showNavHamburger_IN_hybrid') === 'false') ? 50
          : ''
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  CHANGE_SIDEBARBG: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.sidebarBg.hasOwnProperty(key)) {
      state.sidebarBg[key] = value
    }
  },
  TOGGLE_MENUSTYLE: (state, value) => {
    state.menuStyle = value
    localStorage.setItem('menuStyle', value)

    switch (value) {
      case 'vertical':
        state.extraHeight = 84
        break
      case 'horizontal':
        state.extraHeight = 134
        break
      case 'hybrid':
        state.extraHeight = 134
        break
      default:
        state.extraHeight = 84
        break
    }
  },
  CHANGE_MENUOPTCOLOR: (state, { key, value }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.sidebarBg.hasOwnProperty(key)) {
      state.sidebarBg[key] = value
    }
  },
  CHANGE_SHOWNAVHAMBURGER: (state, value) => {
    state.extraHeight = value ? 134 : 50

    state.showNavHamburger_IN_hybrid = value
  }
}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
  changeSidebarBg({ commit }, data) {
    commit('CHANGE_SIDEBARBG', data)
  },
  toggleMeunStyle({ commit }, data) {
    commit('TOGGLE_MENUSTYLE', data)
  },
  changeMenuOptColor({ commit }, data) {
    commit('CHANGE_MENUOPTCOLOR', data)
  },
  changeShowNavHamburger({ commit }, data) {
    commit('CHANGE_SHOWNAVHAMBURGER', data)
  }

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

