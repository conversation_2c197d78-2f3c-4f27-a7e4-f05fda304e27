<template>
  <div>
    <vxe-modal
      height="99%"
      width="30%"
      position="center"
      resize
      :title="modalInfo.title"
      v-model="modalInfo.show"
      @close="handleModalClose()"
      showFooter
    >

      <vxe-form
        ref="myForm"
        title-width="100"
        :data="modalForm"
        :rules="rules"
        title-align="right"
        prevent-submit
        span="24"
      >
        <vxe-form-item title="模板名称" field="template_name">
          <template v-slot="scope">
            <vxe-input
              v-model.trim="modalForm.template_name"
              placeholder="模板名称"
              clearable
            ></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="模板描述" field="template_remark">
          <template v-slot="scope">
            <vxe-input
              v-model.trim="modalForm.template_remark"
              placeholder="模板描述"
              clearable
            ></vxe-input>
          </template>
        </vxe-form-item>

        <vxe-form-item title="模板主题" field="template_theme">
          <template v-slot="scope">
            <vxe-input
              v-model.trim="modalForm.template_theme"
              placeholder="模板主题"
              clearable
            ></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="模板正文" field="template_text">
          <template v-slot="scope">
            <vxe-textarea v-model="modalForm.template_text" placeholder="模板正文" ></vxe-textarea>

          </template>
        </vxe-form-item>
      </vxe-form>

      <template v-slot:footer>
        <el-button type="" @click="handleModalClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm"
        >确定</el-button
        >
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import {clearProperty, copyProperty} from "@/utils/guozw-core";
import {saveConfigEmailTemplate} from "@/api/config/email";


export default {
  name: "emailTemplateEditModal",
  data(){
    return{
      modalForm:{},
      rules:{
        template_name: [{required: true, message: '请输入模板名称！'}],
        template_theme: [{required: true, message: '请输入模板主题！'}],
      },
      loading: false,
    }
  },
  props:{
    modalInfo: {
      type: Object,
      default: () => {}
    }
  },
  created() {

  },
  methods:{
    handleModalClose() {
      this.modalInfo.show = false;
      this.$refs.myForm.clearValidate();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
    },
    // 提交表单
    submitForm() {
      this.$refs.myForm
        .validate()
        .then(() => {
          this.loading = true;


          saveConfigEmailTemplate(this.modalForm)
            .then(response => {
              this.$XModal.message({
                message: this.modalForm.config_email_template_id ? "修改成功" : "新增成功",
                status: "success"
              });
              this.$emit("refreshTable");
              this.modalInfo.show = false;
            })
            .finally(() => {
              this.loading = false;
              clearProperty(this.modalForm);
              this.modalInfo.show = false;
            });
        })
        .catch(err => {
          console.log(err);
          return false;
        });
    }
  },
  watch: {

  },
}
</script>

<style scoped>

</style>
