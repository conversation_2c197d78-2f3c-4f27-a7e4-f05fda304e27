package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资源表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "resourceitem")
public class Resource extends BaseEntity<Resource> implements Serializable {

    private static final long serialVersionUID = -3365132934882153914L;
    /**
     * 资源编码
     */
    @TableId
    private String resourcecode;
    /**
     * 资源名称
     */
    private String resourcename;
    /**
     * 资源描述
     */
    private String resourcenote;
    /**
     * 父资源编码
     */
    private String resourceparentcode;
    /**
     * 资源图标
     */
    private String resourceicon;
    /**
     * 资源请求地址
     */
    private String resourceuri;

    /**
     * 资源类型编码
     */
    private String resourcetypecode;
    /**
     * 权限标识
     */
    private String permissiontag;
    /**
     * 组件名称
     */
    private String componentname;
    /**
     * 是否启用 0-否 1-是
     */
    private Integer isenabled;
    /**
     * 是否删除 0-否 1-是
     */
    private Integer isdelete;

    /**
     * 是否显示
     */
    private String hidden;

    /**
     * 排序序号
     */
    private Integer ordernumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
