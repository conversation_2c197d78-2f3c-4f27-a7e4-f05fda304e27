package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 任务计划调度表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "task_plan_dispatch")
public class TaskPlanDispatch extends BaseEntity<TaskPlanDispatch> implements Serializable {

    private static final long serialVersionUID = 454684289830020628L;

    /**
     * 主键
     */
    @TableId
    private String task_plan_dispatch_id;
    /**
     * task_basic表主键
     */
    private String task_basic_id;
    /**
     * 进入调度时间
     */
    private String dispatch_in_time;
    /**
     * 任务开始时间
     */
    private String dispatch_start_time;
    /**
     * 任务结束时间
     */
    private String dispatch_end_time;
    /**
     * 所用时长（单位：s）
     */
    private String dispatch_run_time;
    /**
     * 任务状态
     */
    private String task_status;
    /**
     * 任务进度
     */
    private String task_progress;

    /**
     * 系统结束运行时的状态
     */
    private String system_end_status;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
