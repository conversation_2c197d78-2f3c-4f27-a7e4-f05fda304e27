package com.guozw.common.core.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ApiCryptoBody implements Serializable {
    private static final long serialVersionUID = -3597285365755871357L;
    private String appId;
    private String data;
    private String nonce;
    private Long timestamp;
    private String sign;
}
