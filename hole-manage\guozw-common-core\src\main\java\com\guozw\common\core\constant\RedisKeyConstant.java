package com.guozw.common.core.constant;

public class RedisKeyConstant {

    public static final String PUBLIC_KEY = "publicKey";
    public static final String PRIVATE_KEY = "privateKey";
    public static final String AES_KEY = "aesKey";
    /**
     * 盛邦资产扫描api获取的token存储在redis中的key值
     */
    public static final String SHENGBANG_ASSETSCAN_API_TOKEN_KEY = "shengbang_assetscan_api_token_key";
    /**
     * 盛邦漏洞扫描api获取的token存储在redis中的key值
     */
    public static final String SHENGBANG_HOLESCAN_API_TOKEN_KEY = "shengbang_holescan_api_token_key";
    /**
     * Foeye漏洞扫描api获取的token存储在redis中的key值
     */
    public static final String FOEYE_HOLESCAN_API_TOKEN_KEY = "foeye_holescan_api_token_key";
    /**
     * 多个任务扫描完成IP漏洞时解析扫描结果需用到的同步锁
     */
    public static final String HOLE_IP_PARSE_LOCK = "HOLE_IP_PARSE_LOCK";
    /**
     * 多个任务扫描完成网站漏洞时解析扫描结果需用到的同步锁
     */
    public static final String HOLE_WEBSITE_PARSE_LOCK = "HOLE_WEBSITE_PARSE_LOCK";
    /**
     * 前端通过按钮操作了任务的状态后，存入redis中，job中的while无限循环获取这个状态判断是否应该中断循环
     */
    public static final String TASK_STATUS_KEY = "{}-TASKSTATUS";
}
