package com.guozw.common.core.constant;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 错误码枚举
 * <AUTHOR>
 */

public enum ErrorCodeEnum {
    SUCCESS("请求成功", 20000),
    PASSWORD_INCORRECT("密码不正确", 20001),

    FAILED("请求失败", 50000),
    REFERER_INVALID("referer请求头无效", 50001),
    FORBIDDEN("无权访问", 50003),
    NOT_FOUND("请求资源不存在", 50004),
    VISITOR_TOO_MUCH("当前访问人数过多，请稍后再试。", 40005),
    USER_NOT_EXISTS("用户不存在", 50006),
    USERNAME_PASSWORD_ERROR("用户名或密码错误", 50007),
    AUTHENTICATION_FAILED("用户认证失败", 50008),
    USER_LOCKED("用户锁定", 50009),
    DIVISIOR_ZERO("除数不能为0", 50010),
    TOKEN_EXPIRED("token无效或过期", 50014),
    CRYPTO_FAILED("加解密失败", 50015),
    NOT_EMAIL_PROPERTIES("当前用户未配置邮箱！", 50016),
    CAPTCHA_ERROR("验证码错误", 50017),


    IP_FORMAT_ERROR("ip格式错误，请重新检查", 50018),
    IP_NULL_ERROR("ip不能为空", 50019),
    IP_NOT_FIND_ERROR("ip不存在", 50020),
    PARAM_VALIDATE_FAIL("参数校验失败", 60000),
    PARAM_APPID_MISSING("缺少必要参数 appId", 60001),
    PARAM_DATA_MISSING("缺少必要参数 data", 60002),
    PARAM_NONCE_MISSING("缺少必要参数 nonce", 60003),
    PARAM_TIMESTAMP_MISSING("缺少必要参数 timestamp", 60004),
    PARAM_SIGN_MISSING("缺少必要参数 sign", 60005),
    SIGNATURE_TIME_OUT("签名已超时", 60006),
    INVALID_APPID("无效的 appId", 60007),
    SIGNATURE_FAILED("签名失败", 60008),
    VERIFY_SIGNATURE_FAILED("签名验证失败", 60009);




    private String message;
    private Integer code;

    ErrorCodeEnum(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
