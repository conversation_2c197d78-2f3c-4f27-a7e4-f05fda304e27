<template>
  <el-container>
    <el-main>
      <!-- region 查询条件表单-->
      <vxe-form ref="queryConditionForm" title-width="100" title-align="right" span="6" :data="queryParams" @submit="handleQueryConditionFormSubmit" @toggle-collapse="handleQueryConditionFormToggleCollapse">
        <vxe-form-item field="hole_patch_remark" title="补丁描述">
          <vxe-input clearable placeholder="补丁描述" v-model="queryParams.hole_patch_remark"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="hole_library_name" title="漏洞名称">
          <vxe-input clearable placeholder="漏洞名称" v-model="queryParams.hole_library_name"></vxe-input>
        </vxe-form-item>

        <vxe-form-item align="center">
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <el-button type="primary" icon="fa fa-plus" v-btnpermission="'hole:holepatch:add'" @click="handleAdd">
            新增</el-button>
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table id="myTable" ref="myTable" v-loading="loading" element-loading-text="拼命加载中" border auto-resize resizable height="auto" show-overflow :sort-config="{ trigger: 'cell', remote: true }" @sort-change="sortChange" :custom-config="{ storage: true }" :data="page.records" :checkbox-config="{ trigger: 'row' }">
          <!-- <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column> -->
          <vxe-table-column title="序号" type="seq" width="60" fixed="left"></vxe-table-column>

          <vxe-table-column field="hole_patch_remark" title="补丁描述"></vxe-table-column>
          <vxe-table-column field="hole_patch_url" title="官方地址"></vxe-table-column>
          <vxe-table-column field="file_real_name" title="附件">
            <template slot-scope="scope">
              <el-link type="primary" @click="goDownload(scope.row)">{{ scope.row.file_real_name }}</el-link>
            </template>
          </vxe-table-column>
          <vxe-table-column field="library_patch_name" title="关联漏洞"></vxe-table-column>
          <vxe-table-column field="" title="操作" fixed="right" width="70px">
            <!-- <template v-slot="{ row }">
              <i class="el-icon-view" style="font-size: 18px; color: #409EFF" @click="handleView(row)"></i>
            </template> -->
            <template slot-scope="scope">
              <i class="el-icon-edit" style="font-size: 18px; color: #409EFF" v-btnpermission="'hole:holepatch:update'" @click="handleUpdate(scope.row)"></i>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager ref="pager" :current-page="queryParams.pagenumber" :page-size="queryParams.pagesize" :total="page.total" @page-change="handlePageChange">
      </vxe-pager>
      <!-- endregion -->
      <HolePathEdit ref="holePathEdit" :modalInfo="editModalInfo" :selectEnum="selectEnum" @refreshTable="loadTable" :brandEnum="brandEnum" :agreementEnum="agreementEnum"></HolePathEdit>
    </el-main>
  </el-container>
</template>

<script>
import {
  modifyWorkorderWarnUser,
} from "@/api/workorder/warn";
import { pageHolePath, downloadFile } from "@/api/holemanage/asset";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import { mapGetters } from "vuex";
import { eventBus } from "@/main";
import HolePathEdit from './compenents/holePathEdit.vue';

export default {
  name: "index",
  components: { DepartmentTree, HolePathEdit },

  data() {
    return {
      activeName: "0",
      tabsHeight: 0,
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      userList: [],
      loading: true,
      disabled: false, // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
      page: {
        total: 0,
        records: [],
      },
      resourceInfo: {
      },
      resourceFormRules: {

      },
      resourceFormModalTitle: "详细信息",
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        workorder_warn_id: "",
        workorder_warn_name: "",
        workorder_warn_from: null,
        workorder_warn_content: "",
        workorder_warn_url: "",
        isdelete: "",
        is_read: "0",
        //日期
        startDate: null,
        endDate: null,
        //阅读日期
        startDateRead: null,
        endDateRead: null,
      },
      // 扫描工具品牌枚举
      brandEnum: [],
      //选择显示枚举
      selectEnum: [],
      // 协议枚举
      agreementEnum: [],

      // 新增 修改 弹窗的表单默认数据
      formDefaultData: {},
      // 新增 修改 弹窗的显示关闭以及标题等信息
      editModalInfo: {},
      batchEditModalInfo: {},
      batchEdit_formDefaultData: {},
    };
  },
  created() {
    this.loadTable();
  },

  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
  },
  watch: {
    $route() {
      this.loadTable();
    },
  },
  methods: {
    // 跳转地址
    gowarn(row) {
      this.handleWarnUserStatus(row);
      const urlJson = JSON.parse(row.workorder_warn_url);
      this.$router.push({ path: urlJson.path, query: urlJson.query });
    },
    handleAdd() {
      //   this.formDefaultData = {};
      this.editModalInfo = {
        title: "新增补丁库",
        show: true,
        type: "add",
        data: {}
      };
    },
    handleUpdate(row) {
      const rows = JSON.parse(JSON.stringify(row));
      if (row.library_patch_id){
        this.$refs.holePathEdit.queryLibraryByIds(row.library_patch_id);
      }
      this.editModalInfo = {
        title: "修改补丁库",
        show: true,
        type: "edit",
        data: rows
      };

    },
    goDownload(rows) {
      const row = {};
      row.file_id = rows.file_id;
      row.file_real_name = rows.file_real_name;
      row.file_storage_name = rows.file_storage_name;
      downloadFile(row).then((res) => {
        let blob = new Blob([res]);
        let objectUrl = URL.createObjectURL(blob);
        let link = document.createElement("a");
        link.href = objectUrl;
        link.download = `${row.file_real_name}-模板文件.${row.file_storage_name.split(".")[1]
          }`;
        link.click();
      });
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      this.queryParams.is_read = this.activeName;
      this.loadTable();
    },
    handleWarnUserStatus(row) {
      if (row.is_read == "0") {
        const query = {};
        query.workorder_warn_id = row.workorder_warn_id;
        var userName = this.$store.state.user.name;
        if (userName != "admin") {
          modifyWorkorderWarnUser(query)
            .then(({ data, msg }) => {
              if (data) {
                // this.$XModal.message({
                //     message: "修改成功",
                //     status: "success"
                // });
                eventBus.$emit("countWork", '总数变化');

              } else {
                this.$XModal.message({
                  message: msg,
                  status: 'warning'
                });
              }
            })
            .finally(() => {

            });
        }
      }

    },
    // 处理查看
    handleView(row) {
      console.log(row)
      this.$refs.resourceFormModal.open()
      this.disabled = true;
      this.resourceInfo = row;
    },
    changeWorkorder_warn_from(row) {
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight =
        this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight;

    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight =
          this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },

    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },

    /* 字段排序 */
    sortChange(e) {
      console.log("sortChange", e);
      const { field, order } = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },

    // 查询条件
    getQueryCondition() {

      // this.queryParams.params = {};
      // this.queryParams.params['beginCreatedTime'] = this.queryParams.startDate;
      // this.queryParams.params['endCreatedTime'] = this.queryParams.endDate;

      // this.queryParams.params['beginReadTime'] = this.queryParams.startDateRead;
      // this.queryParams.params['endReadTime'] = this.queryParams.endDateRead;
      if (this.$route.query != null) {
        const hole_patch_id = this.$route.query && this.$route.query.hole_patch_id;
        this.queryParams.hole_patch_id = hole_patch_id;
      }
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
      console.log(quryCondition)

      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      pageHolePath(this.getQueryCondition())
        .then((response) => {
          this.page.records = response.data.records;
          this.page.total = response.data.total;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },

  },
  computed: {
    ...mapGetters(["extraHeight"]),
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val;
    },
  },
};
</script>
<style scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
