package com.guozw.common.core.bean;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * vue-elementui路由
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class Route implements Serializable {
    private static final long serialVersionUID = -2643938351940632570L;

    private String id;
    private String parentId;
    private String name;
    private String path;
    private String component;
    private String redirect;
    private Integer sort;
    private Boolean alwaysShow;
    private Meta meta;
    private Boolean hidden;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
