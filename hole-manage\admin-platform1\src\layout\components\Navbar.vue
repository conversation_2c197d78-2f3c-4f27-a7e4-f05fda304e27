<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" v-if="menuStyle !== 'horizontal'" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <!-- <search id="header-search" class="right-menu-item" />

        <error-log class="errLog-container right-menu-item hover-effect" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip class="item" effect="dark" content="消息通知" placement="right-start">
          <el-badge :value="warnCount" class="item">
            <el-link type="primary" @click="gowarn"><img :src="require('../../assets/images/notice.png')" width="20px" height="20px" class="user-avatar"></el-link>
             </el-badge>
             <router-link to="/workorder/warn" size="small"><img :src="require('../../assets/images/notice.png')" width="20px" height="20px" class="user-avatar"></router-link> 
       
        </el-tooltip> -->

        <el-tooltip class="avatar-container right-menu-item hover-effect" style="transform:translate(20px);" effect="dark" content="消息通知" placement="right-end">
          <div style="padding-top:10px;" @click="gowarn">
            <el-badge :value="warnCount" class="item" >
              <el-link type="primary" ><img :src="require('../../assets/images/notice.png')" width="20px" height="20px" class="user-avatar"></el-link>
            </el-badge>
          </div>
        </el-tooltip>
      </template>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">

        <div class="avatar-wrapper">
          <img :src="require('../../assets/images/user2.png')" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
           <router-link to="/profile2/userCenter">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <router-link to="/profile/index">
            <el-dropdown-item>修改密码</el-dropdown-item>
          </router-link>
          <!-- <router-link to="/">
            <el-dropdown-item>Dashboard</el-dropdown-item>
          </router-link>
          <a target="_blank" href="https://github.com/PanJiaChen/vue-element-admin/">
            <el-dropdown-item>Github</el-dropdown-item>
          </a>
          <a target="_blank" href="https://panjiachen.github.io/vue-element-admin-site/#/">
            <el-dropdown-item>Docs</el-dropdown-item>
          </a> -->
          <el-dropdown-item @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import {
  mapGetters
} from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import ErrorLog from '@/components/ErrorLog'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import { countWorkorderWarn } from "@/api/workorder/warn";
import {
  eventBus
} from "@/main";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    ErrorLog,
    Screenfull,
    SizeSelect,
    Search
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    ...mapGetters(['menuStyle'])
  },
  data() {
    return {
      warnCount: 0,
    };
  },
  created() {
    this.countWork();

  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar', 'hamburgerClick')
    },
    gowarn() {
      const query = {};
      this.$router.push({ path: '/workorder/warn/index', query: query });
    },
    countWork() {
      const query = {};
      countWorkorderWarn(query)
        .then((response) => {
          this.warnCount = response.data;
        })
        .catch((error) => {
        });
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      this.$router.push(`/login?redirect=/dashboard`)
    }
  },
  mounted() {
    // //定时任务方法
    // this.$nextTick(() => {
    //   setInterval(this.countWork, 10000);
    // });
    eventBus.$on("countWork", (msg) => {
      // A发送来的消息
      //   console.log(msg)
      this.countWork();
    });
  },
}

</script>

<style lang="scss" scoped>
.navbar,
.hybrid-top-container {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
