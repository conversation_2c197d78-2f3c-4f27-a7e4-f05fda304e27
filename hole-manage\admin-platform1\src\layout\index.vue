<template>
    <div :class="classObj" class="app-wrapper">
        <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside"></div>
        <HybridMenuheader />
        <!-- <div class="hybrid-top-container" v-if="menuStyle === 'hybrid'" :style="{ background: sidebarBg.menuBg }">
            <logo v-if="showLogo" :collapse="false" />
            <div class="hybrid-avatar-wrap" v-if="!showNavHamburger_IN_hybrid">
                <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
                    <div class="avatar-wrapper">
                        <img :src="require('../assets/images/user2.png')" class="user-avatar" />
                        <i class="el-icon-caret-bottom" />
                    </div>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="logout">
                            <span style="display: block">退出登录</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div> -->
        <sidebar class="sidebar-container vertical" v-if="menuStyle !== 'horizontal'" :style="{ background: sidebarBg.menuBg, top: sidebarTopNum }" />
        <div :class="{ hasTagsView: needTagsView }" class="main-container">
            <div :class="{ 'fixed-header': fixedHeader }" v-if="showNavHamburger" :style="{ 'top': headerTopNum }">
                <sidebar class="sidebar-container horizontal" v-if="menuStyle === 'horizontal'" :style="{ background: sidebarBg.menuBg }" />
                <navbar />
                <tags-view v-if="needTagsView" />
            </div>
            <app-main />
            <right-panel v-if="showSettings">
                <settings />
            </right-panel>
        </div>
    </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import Logo from '@/layout/components/Sidebar/Logo'
import HybridMenuheader from '@/layout/components/Sidebar/HybridMenuheader'
import {
    AppMain,
    Navbar,
    Settings,
    Sidebar,
    TagsView
} from './components'
import ResizeMixin from './mixin/ResizeHandler'
import {
    mapState,
    mapGetters
} from 'vuex'

export default {
    name: 'Layout',
    components: {
        AppMain,
        Navbar,
        RightPanel,
        Settings,
        Sidebar,
        TagsView,
        Logo,
        HybridMenuheader
    },
    mixins: [ResizeMixin],
    computed: {
        ...mapState({
            sidebar: state => state.app.sidebar,
            device: state => state.app.device,
            showSettings: state => state.settings.showSettings,
            needTagsView: state => state.settings.tagsView,
            fixedHeader: state => state.settings.fixedHeader,
        }),
        ...mapGetters([
            'menuStyle', 'sidebarBg', 'showNavHamburger_IN_hybrid'
        ]),
        classObj() {
            return {
                hideSidebar: !this.sidebar.opened,
                openSidebar: this.sidebar.opened,
                withoutAnimation: this.sidebar.withoutAnimation,
                mobile: this.device === 'mobile',
                appWrapper_vertical: this.menuStyle === 'vertical',
                appWrapper_horizontal: this.menuStyle === 'horizontal',
                appWrapper_hybrid: this.menuStyle === 'hybrid',
            }
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo
        },
        showNavHamburger() {
            if (this.menuStyle !== "hybrid") {
                return true;
            } else {
                return this.showNavHamburger_IN_hybrid;
            }
        },
        sidebarTopNum() {
            return this.menuStyle === 'vertical' ? '0px' : this.menuStyle === 'horizontal' ? '50px' : '50px'
        },
        headerTopNum() {
            return this.menuStyle === 'vertical' ? '0px' : this.menuStyle === 'horizontal' ? '50px' : '50px'
        }
    },
    methods: {
        handleClickOutside() {
            this.$store.dispatch('app/closeSideBar', {
                withoutAnimation: false
            })
        },
        async logout() {
            await this.$store.dispatch('user/logout')
            this.$router.push(`/login?redirect=${this.$route.fullPath}`)
        }
    }
}

</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
        position: fixed;
        top: 0;
    }
}

.drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
}

.fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
}

.hideSidebar .fixed-header {
    width: calc(100% - 54px);
}

.mobile .fixed-header {
    width: 100%;
}
</style>
