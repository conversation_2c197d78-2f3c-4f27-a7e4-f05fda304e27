$plr-20: 0 20px;

#app {
    .main-container {
        min-height: 100%;
        transition: margin-left 0.28s;
        margin-left: $sideBarWidth;
        position: relative;
    }

    .sidebar-container {
        transition: width 0.28s;
        width: $sideBarWidth !important;
        background-color: $menuBg;
        height: 100%;
        position: fixed;
        font-size: 0px;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1001;
        overflow: hidden;

        // reset element-ui css
        .horizontal-collapse-transition {
            transition: 0s width ease-in-out, 0s padding-left ease-in-out,
                0s padding-right ease-in-out;
        }

        .scrollbar-wrapper {
            overflow-x: hidden !important;
        }

        .el-scrollbar__bar.is-vertical {
            right: 0px;
        }

        .el-scrollbar {
            height: 100%;
        }

        &.has-logo {
            .el-scrollbar {
                height: calc(100% - 50px);
            }
        }

        .is-horizontal {
            display: none;
        }

        a {
            display: inline-block;
            width: 100%;
            overflow: hidden;
        }

        .svg-icon {
            margin-right: 5px;
        }

        .sub-el-icon {
            margin-right: 12px;
            margin-left: -2px;
        }

        .el-menu {
            border: none;
            height: 100%;
            width: 100% !important;
        }

        // menu hover
        .submenu-title-noDropdown,
        .el-submenu__title {
            &:hover {
                background-color: $menuHover !important;
            }
        }

        .is-active > .el-submenu__title {
            color: $subMenuActiveText !important;
        }

        & .nest-menu .el-submenu > .el-submenu__title,
        & .el-submenu .el-menu-item {
            min-width: $sideBarWidth !important;
            // background-color: $subMenuBg !important;

            &:hover {
                background-color: $subMenuHover !important;
            }
        }
    }

    .openSidebar.appWrapper_vertical,
    .openSidebar.appWrapper_hybrid {
        .el-menu {
            padding: 0 10px;

            > div {
                // margin-bottom: 10px;
                background: none !important;
                // margin-bottom: 5px;

                &:last-of-type {
                    margin-bottom: 0;
                }

                .el-menu-item {
                    height: auto;
                    line-height: unset;
                    // display: flex;
                    // align-items: center;
                    overflow: hidden;
                    border-radius: 100px;
                    padding: 0 !important;

                    > div {
                        padding: 8px 20px;
                        width: 100%;
                        display: flex;
                        align-items: center;
                    }
                }

                .el-menu-item.is-active {
                    font-weight: bold;
                    // border: 5px solid #fff;

                    > div {
                        box-shadow: inset 6px 6px 12px 0px #06443e;
                    }
                }

                .el-submenu {
                    overflow: hidden;
                    border-radius: 100px;

                    .el-submenu__title {
                        height: auto;
                        line-height: unset;
                        padding: 8px 15px;
                    }
                }

                .el-submenu.is-opened {
                    border-radius: 0;

                    .el-submenu__title {
                        &:hover {
                            border-radius: 100px;
                        }
                    }

                    .el-menu {
                        padding: 0;

                        > .nest-menu {
                            margin-bottom: 5px;
                            margin-left: 20px;
                            &:last-child{
                                margin-bottom: 0;
                            }

                            .el-menu-item {
                                min-width: unset !important;
                            }
                        }
                    }
                }

                .el-submenu.is-active {
                    .el-submenu__title {
                        box-shadow: inset 6px 6px 12px 0px #06443e;
                    }
                }

                .el-submenu.is-active.is-opened {
                    border-radius: 0;

                    &:hover {
                        border-radius: 0 !important;
                    }

                    .el-submenu__title {
                        box-shadow: none;

                        &:hover {
                            border-radius: 100px;
                        }
                    }

                    .el-menu {
                        padding: 0;

                        > div {
                            margin-bottom: 5px;

                            .el-menu-item {
                                min-width: unset !important;
                            }
                        }
                    }
                }
            }
        }
    }

    .hideSidebar {
        .sidebar-container {
            width: 54px !important;
        }

        .main-container {
            margin-left: 54px;
        }

        .submenu-title-noDropdown {
            padding: 0 !important;
            position: relative;

            .el-tooltip {
                padding: 0 !important;

                .svg-icon {
                    margin-left: 20px;
                }

                .sub-el-icon {
                    margin-left: 19px;
                }
            }
            span{
                display: none;
            }
        }

        .el-submenu {
            overflow: hidden;

            & > .el-submenu__title {
                padding: 0 !important;

                .svg-icon {
                    margin-left: 20px;
                }

                .sub-el-icon {
                    margin-left: 19px;
                }

                .el-submenu__icon-arrow {
                    display: none;
                }
            }
            span{
                display: none;
            }
        }
        .el-menu-item {
            .svg-icon {
                margin-left: 20px;
            }

        }

        .el-menu--collapse {
            .el-submenu {
                & > .el-submenu__title {
                    & > span {
                        height: 0;
                        width: 0;
                        overflow: hidden;
                        visibility: hidden;
                        display: inline-block;
                    }
                }
            }
        }
    }

    .el-menu--collapse .el-menu .el-submenu {
        min-width: $sideBarWidth !important;
    }

    // mobile responsive
    .mobile {
        .main-container {
            margin-left: 0px;
        }

        .sidebar-container {
            transition: transform 0.28s;
            width: $sideBarWidth !important;
        }

        &.hideSidebar {
            .sidebar-container {
                pointer-events: none;
                transition-duration: 0.3s;
                transform: translate3d(-$sideBarWidth, 0, 0);
            }
        }
    }

    .withoutAnimation {
        .main-container,
        .sidebar-container {
            transition: none;
        }
    }

    .appWrapper_horizontal {
        .main-container {
            margin-left: 0;

            .fixed-header {
                width: 100%;
                // top: 50px;

                // 菜单样式
                .sidebar-container {
                    width: 100% !important;
                    height: auto;
                    bottom: unset;
                    display: flex;
                    align-items: center;
                    padding: $plr-20;

                    .sidebar-logo-container {
                        min-width: 160px;
                        width: auto;
                        background: transparent;
                    }

                    .el-menu {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex: 1;

                        > div {
                            margin-right: 10px;
                            border-radius: 100px;
                            overflow: hidden;
                            // background: inherit !important;

                            .el-menu-item {
                                display: block !important;
                                height: auto;
                                line-height: unset;
                                display: flex;
                                flex-direction: column;
                                overflow: hidden;
                                border-radius: 100px;
                                padding: 0;

                                > div {
                                    padding: 8px 20px;
                                    width: 100%;
                                    display: flex;
                                    align-items: center;
                                }
                            }

                            .el-menu-item.is-active {
                                font-weight: bold;
                                > div {
                                    box-shadow: inset 6px 6px 12px 0px #06443e;
                                }
                            }

                            .el-submenu {
                                overflow: hidden;
                                border-radius: 100px;

                                .el-submenu__title {
                                    height: auto;
                                    line-height: unset;
                                    padding: 8px 15px;
                                }
                            }

                            .el-submenu.is-active {
                                font-weight: bold;

                                .el-submenu__title {
                                    box-shadow: inset 6px 6px 12px 0px #06443e;
                                    background: inherit !important;
                                }
                            }
                        }
                        > div.notSelectMenu {
                            background: inherit !important;
                        }
                    }
                }

                .navbar {
                    padding: $plr-20;

                    .hamburger-container {
                        padding-left: 0 !important;
                    }
                }

                .tags-view-container {
                    padding: $plr-20;

                    .tags-view-item:first-of-type {
                        margin-left: 0;
                    }
                }
            }
        }
    }

    .appWrapper_hybrid {
        .hybrid-top-container {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            // padding-left: 20px;
            // padding: 10px 0 10px 20px;
            padding: 0 0 0 20px;
            height: 50px;
            background: #2b2f3a;
            z-index: 10;
            * {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .sidebar-logo-container {
                width: auto;
                height: auto;
                line-height: unset;
                .sidebar-logo {
                    height: 28px;
                    width: auto;
                }
                .sidebar-title {
                    font-size: 18px;
                }
            }

            .hybrid-avatar-wrap {
                width: auto;

                .right-menu-item {
                    display: inline-block;
                    padding: 0 8px;
                    height: 100%;
                    font-size: 18px;
                    color: #5a5e66;
                    vertical-align: text-bottom;

                    &.hover-effect {
                        cursor: pointer;
                        transition: background 0.3s;

                        &:hover {
                            background: rgba(0, 0, 0, 0.025);
                        }
                    }
                }

                .avatar-container {
                    margin-right: 30px;

                    .avatar-wrapper {
                        // margin-top: 5px;
                        position: relative;

                        .user-avatar {
                            cursor: pointer;
                            width: 40px;
                            height: 40px;
                            border-radius: 10px;
                        }

                        .el-icon-caret-bottom {
                            cursor: pointer;
                            position: absolute;
                            right: -20px;
                            top: 25px;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        .sidebar-container {
            .el-menu {
                .el-submenu.is-opened {
                    .nest-menu {
                        margin-left: 30px;
                    }
                }
            }
        }
    }
}

// when menu collapsed
.el-menu--vertical {
    & > .el-menu {
        .svg-icon {
            margin-right: 16px;
        }

        .sub-el-icon {
            margin-right: 12px;
            margin-left: -2px;
        }
    }

    .nest-menu .el-submenu > .el-submenu__title,
    .el-menu-item {
        &:hover {
            // you can use $subMenuHover
            background-color: $menuHover !important;
        }
    }

    // the scroll bar appears when the subMenu is too long
    > .el-menu--popup {
        max-height: 100vh;
        overflow-y: auto;

        &::-webkit-scrollbar-track-piece {
            background: #d3dce6;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: #99a9bf;
            border-radius: 20px;
        }
    }
}

// 二级菜单样式
.el-menu--horizontal[x-placement="bottom-start"] {
    top: 50px !important;

    .nest-menu {
        .el-menu-item.is-active {
            padding: 0;

            > div {
                padding: 0 10px;
            }
        }
    }
}

// 二级菜单样式
.el-menu--vertical[x-placement="right-start"] {
    .nest-menu {
        .el-menu-item.is-active {
            padding: 0 !important;

            > div {
                padding-left: 10px;
            }
        }
    }
}
