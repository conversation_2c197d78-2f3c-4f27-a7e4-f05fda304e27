/**
 * Created by guozw on 2020/08/08.
 */

/**
 * 将普通列表转换为树结构的列表
 * @param list
 * @param id
 * @param parentId
 * @param children
 * @returns {{length}|*|*[]}
 */
export function listToTreeList(
    list,
    id = "id",
    parentId = "parentId",
    children = "children"
) {
    if (!list || !list.length) {
        return [];
    }
    let treeListMap = {};
    for (let item of list) {
        treeListMap[item[id]] = item;
    }
    for (let i = 0; i < list.length; i++) {
        if (list[i][parentId] && treeListMap[list[i][parentId]]) {
            if (!treeListMap[list[i][parentId]][children]) {
                treeListMap[list[i][parentId]][children] = [];
            }
            treeListMap[list[i][parentId]][children].push(list[i]);
            list.splice(i, 1);
            i--;
        }
    }
    return list;
}
/**
 * 清除对象自身所有属性
 * @param {Object} obj
 */
export function clearProperty(obj) {
    Object.keys(obj).forEach(key => {
        if (obj.hasOwnProperty(key)) {
            obj[key] = null;
        }
    });
}
/**
 * 复制对象属性
 * @param {Object} source
 * @param {Object} target
 * @param {Array} ignore 忽略的属性
 */
export function copyProperty(source, target, ignore=[]) {
    Object.keys(target).forEach(key => {
        if (target.hasOwnProperty(key) && ignore.indexOf(key) == -1) {
            target[key] = source[key];
        }
    });
}

/* 获取当前时间 */
export function getCurrentDate() {
    let current = new Date();

    let currentData = `${current.getFullYear()}-${current.getMonth() > 8 ? current.getMonth() + 1 : '0' + (current.getMonth() + 1)}-${current.getDate() > 9 ? current.getDate() : '0' + current.getDate()}`

    console.log("当前时间", currentData);
    return currentData;
}


/* 替换所有字符 */
export function replaceAll(content, searchValue, replaceValue) {
    if (content) {
        while (content.indexOf(searchValue) > -1) {
            content = content.replace(searchValue, replaceValue)
        }
        return content
    }
}

/* 格式化下拉选项 */
export function formatSelect(cellValue, list) {
    const item = list.find(item => item.value == cellValue);
    return item ? item.label : "";
}

export const patterns = {
     /* 同时验证手机号和座机号的正则表达式（支持400电话号码） */
     phoneOrTel: /^(1[3|4|5|6|7|8|9])\d{9}$|^0\d{2,3}-?\d{7,8}$|^400[016789]\d{6}$|^400-[016789]\d{2}-\d{4}$/,
   
    phone: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/,
    email: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    url: /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
}