<template>
  <div :style="{ 'background-color': menuBg }">
    <template v-if="icon">
      <i :class="[icon, 'sub-el-icon']" v-if="icon.includes('el-icon')" />
      <svg-icon :icon-class="icon" v-else />
    </template>
    <span slot="title" v-if="title">{{ title }}</span>
  </div>
</template>


<script>
import {
  mapGetters
} from 'vuex'

export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    path: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters([
      'activeMenu', 'sidebarBg'
    ]),
    menuBg () {
      let url;
      const activeMenu = this.activeMenu.split('/')
      if (activeMenu.length == 4) {
        // 配变改造  输电线路改造
        url = activeMenu.slice(1, 3).join(',').replace(',', '/')
      }
      if (activeMenu.length == 5) {
        url = activeMenu.slice(-2).join(',').replace(',', '/')
      }


      if (this.activeMenu === '/dashboard' && this.path === '/') return this.sidebarBg.activeBgColor


      if (this.path.includes(url)) {
        return this.sidebarBg.activeBgColor
      }
    }
  },
  data () {

    return {}
  },
  methods: {}
}

</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
</style>
