package com.guozw.common.core.test.util;

import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;

public class WriteExcel {

    public static void writeExcel(String sheetName, List<List<String>> list , String filePath, List<String> header){
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet(sheetName);


        //循环设置表头信息
        if (!CollectionUtils.isEmpty(header)) {
            XSSFRow firstRow = sheet.createRow(0);//第一行表头

            for (int i = 0; i < header.size(); i++){
                firstRow.createCell(i).setCellValue(header.get(i));
            }
        }

        //遍历list,将数据写入Excel中
        for (int i=0, len = list.size(); i < len; i++){
            XSSFRow row = sheet.createRow(i+1);
            List<String> rowData = list.get(i);

            for (int k = 0, len2 = rowData.size(); k < len2; k++) {
                XSSFCell cell = row.createCell(k);
                cell.setCellValue(rowData.get(k));
            }

        }
        OutputStream out = null;
        try {
            out = new FileOutputStream(filePath);
            workbook.write(out);
            out.close();
        } catch (Exception e){
            e.printStackTrace();
        }
    }
}
