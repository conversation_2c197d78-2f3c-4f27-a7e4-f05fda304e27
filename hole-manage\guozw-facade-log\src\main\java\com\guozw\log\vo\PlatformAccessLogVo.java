package com.guozw.log.vo;

import com.guozw.log.entity.PlatformAccessLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class PlatformAccessLogVo extends PlatformAccessLog implements Serializable {

    private String beginCreatedTime;
    private String endCreatedTime;

}
