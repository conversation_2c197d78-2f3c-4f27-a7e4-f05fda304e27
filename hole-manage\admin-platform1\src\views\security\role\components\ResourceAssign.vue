<template>
    <vxe-modal
        ref="resourceAssignModal"
        maxHeight="90%"
        width="40%"
        position="center"
        resize
        :title="modalInfo.title"
        v-model="modalInfo.show"
        @close="handleClose"
        showFooter
    >
        <el-tree
            ref="resourceTree"
            node-key="resourcecode"
            :data="resourceTreeNodes"
            show-checkbox
            check-on-click-node
            default-expand-all
            :expand-on-click-node="false"
            check-strictly
            :props="{ children: 'children', label: 'resourcenote' }"
        >
        </el-tree>
        <template v-slot:footer>
            <vxe-button
                type="button"
                content="取消"
                @click="handleClose"
            ></vxe-button>
            <vxe-button
                type="button"
                status="primary"
                content="确定"
                @click="handleSubmit"
            ></vxe-button>
        </template>
    </vxe-modal>
</template>

<script>
import {
    listResources,
    listRoleResourceRelations
} from "@/api/security/resource";
import { saveRoleResourceRelation } from "@/api/security/role";
import { listToTreeList } from "@/utils/guozw-core";
export default {
    name: "ResourceAssign",
    data() {
        return {
            roleInfo: {
                rolecode: null,
                rolename: null,
                rolenote: null,
                ordernumber: null
            },
            resourceTreeConfig: {
                id: "resourcecode",
                parentId: "resourceparentcode",
                children: "children"
            },
            resourceTreeNodes: []
        };
    },
    props: {
        modalInfo: {
            type: Object,
            default: () => {
                return { show: null, title: null, type: null, rolecode: null };
            }
        }
    },
    watch: {
        "modalInfo.rolecode": {
            immediate: true,
            handler(newVal, oldVal) {
                if (newVal) {
                    this.roleInfo.rolecode = newVal;
                    this.loadResourceInfo();
                }
            }
        }
    },
    mounted() {},
    methods: {
        // 加载资源树
        loadResourceInfo() {
            listResources()
                .then(response => {
                    this.resourceTreeNodes = listToTreeList(
                        response.data,
                        this.resourceTreeConfig.id,
                        this.resourceTreeConfig.parentId
                    );
                })
                .then(() => {
                    listRoleResourceRelations({
                        rolecode: this.modalInfo.rolecode
                    }).then(response => {
                        const roleResourceRelations = response.data;
                        const resourcecodes = roleResourceRelations.map(
                            (item, index, arr) => {
                                return item.resourcecode;
                            }
                        );
                        this.$refs.resourceTree.setCheckedKeys(resourcecodes);
                    });
                });
        },

        // 处理提交
        handleSubmit() {
            // 获取选中的资源编码
            const checkedKeys = this.$refs.resourceTree.getCheckedKeys();
            saveRoleResourceRelation({
                rolecode: this.modalInfo.rolecode,
                resourcecodes: checkedKeys
            }).then(response => {
                this.$refs.resourceAssignModal.close();
                this.$XModal.alert({
                    message: "分配成功",
                    position: "center",
                    status: "success"
                });
            });
        },

        // 处理窗口关闭
        handleClose() {
            this.$refs.resourceAssignModal.close();
            this.modalInfo.show = false;
        }
    }
};
</script>
