package com.guozw.common.gateway.handler;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerFunction;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CaptchaImageHandler implements HandlerFunction<ServerResponse> {
    //随机数code_key
    @Autowired
    private DefaultKaptcha defaultKaptcha;

    @Override
    public Mono<ServerResponse> handle(ServerRequest serverRequest) {
        // 生成验证码
        String capText = defaultKaptcha.createText();
        BufferedImage image = defaultKaptcha.createImage(capText);
        // 获取前端传递的随机值作为redis的key，保存验证码信息
        Optional<String> random_code = serverRequest.queryParam(CommonConstant.RANDOMCODE_PARAMETER);
        random_code.ifPresent(s ->
                RedisUtils.setEx(StrUtil.format("{}_{}", CommonConstant.RANDOMCODE_PARAMETER, s), capText,60, TimeUnit.SECONDS));
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            log.error("ImageIO write error", e);
            return Mono.error(e);
        }

        return ServerResponse.status(HttpStatus.OK)
                .contentType(MediaType.IMAGE_JPEG)
                .body(BodyInserters.fromResource(new ByteArrayResource(os.toByteArray())));
    }
}
