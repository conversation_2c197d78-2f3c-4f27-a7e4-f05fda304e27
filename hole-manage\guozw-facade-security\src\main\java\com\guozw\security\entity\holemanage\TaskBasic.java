package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 检测任务基础信息表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "task_basic")
public class TaskBasic extends BaseEntity<TaskBasic> implements Serializable {

    private static final long serialVersionUID = -6077709085822839569L;

    @TableId
    private String task_basic_id;
    private String task_name;
    private String task_scene;
    private String task_repeat;
    private String task_immediately;
    /**
     * 检测资产类型 1-IP资产 2-网站资产
     */
    private String task_asset_type;
    /**
     * 检测目标类型 1资产 2-分组
     */
    private String task_target_type;
    /**
     * 备注
     */
    private String task_remark;
    /**
     * 任务关联的工具id
     */
    private String tool_basic_id;
    /**
     * 检测目标结果 计算出的结果
     */
    private String task_target_list;
    /**
     * 创建任务的用户id
     */
    private String create_task_userid;
    /**
     * 创建任务的部门id
     */
    private String create_task_deptid;

    /**
     * 任务类型
     */
    private String task_type;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
