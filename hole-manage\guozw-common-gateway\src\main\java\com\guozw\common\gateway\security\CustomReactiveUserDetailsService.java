package com.guozw.common.gateway.security;

import cn.hutool.core.util.ObjectUtil;
import com.guozw.security.facade.RoleService;
import com.guozw.security.facade.UserService;
import com.guozw.security.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.core.userdetails.ReactiveUserDetailsService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Component
public class CustomReactiveUserDetailsService implements ReactiveUserDetailsService {
    @DubboReference
    private UserService userService;
    @DubboReference
    private RoleService roleService;

    @Override
    public Mono<UserDetails> findByUsername(String username) {
        log.info("从数据库中查找用户【{}】", username);
        UserVO userVO = new UserVO();
        userVO.setUsername(username);
        userVO = userService.oneUser(userVO);

        if (ObjectUtil.isNotEmpty(userVO)) {
            return Mono.just(new CustomUserDetails(userVO));
        }

        return Mono.error(new UsernameNotFoundException("User Not Fount"));
    }
}
