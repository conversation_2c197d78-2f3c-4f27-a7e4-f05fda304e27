<template>
  <div :id="id" style="width: 100%; height: 100%"></div>
</template>

<script>
import echarts from '@/utils/echarts'
import { SVGRenderer, CanvasRenderer } from 'echarts/renderers'
// echarts.use(render === 'SVG' ? SVGRenderer : CanvasRenderer)
echarts.use(SVGRenderer)
import 'echarts/theme/macarons'

export default {
  name: 'EchartComponents',
  props: {
    id: {
      type: String,
      required: true,
    },
    chartOption: {
      type: Object,
      required: true,
      default: () => { },
    },
    chartType: {
      type: String,
      required: true,
    },
    chartData: {
      type: Object,
      required: true,
      default: () => { },
    },
    dataKey: {
      type: String,
    }
  },
  data() {
    return {
      chartInstance: null,
    }
  },
  watch: {
    chartData: {
      handler(n) {
        this.loadChartData()
        this.initCharts(this.id)
      },
      deep: true,
    },
  },
  mounted() {
    this.loadChartData()
    this.initCharts(this.id)

    window.addEventListener('resize', this.resize)
    if (this.autoChartSize) watchEl()
  },
  methods: {

    loadChartData() {
      if (this.chartType === 'bar' || this.chartType === 'line') {
        const { xAxis, series } = this.chartOption
        xAxis[0].data = [...this.chartData.XData]
        for (const item of series) {
          if (this.dataKey) {
            item.name = this.dataKey
            item.data = this.chartData.seriesData
          } else {
            item.data = this.chartData[item.name]
          }
        }
      }
      if (this.chartType === 'verticalBar') {
        const { yAxis, series } = this.chartOption
        yAxis[1].data = [...this.chartData.XData]
        for (const item of series) {
          if (this.dataKey) {
            item.name = this.dataKey
            item.data = this.chartData.seriesData
          } else {
            item.data = this.chartData[item.name]
          }
        }
      }
      if (this.chartType == 'gauge') {
        const { series } = this.chartOption
        series[0].data = [{ ...this.chartData }]
      }
      if (this.chartType === 'pie') {
        const { series } = this.chartOption
        series[0].data = [...this.chartData.data]
      }
    },
    initCharts(id) {
      if (!id) return
      this.chartInstance = echarts.init(
        document.getElementById(id),
        'macarons'
      )
      this.chartInstance.setOption(this.chartOption)
    },

    // 更新/设置配置
    setOption(option) {
      this.chartInstance.showLoading()
      this.chartInstance.setOption(option)
      this.chartInstance.hideLoading()

      // this.$nextTick(() => {
      //   if (!this.chartInstance) {
      //     this.initCharts(this.id)
      //     // if (!chartInstance) return
      //   }
      // })
    },

    // 获取echart实例
    getInstance() {
      if (!this.chartInstance) {
        return
        // initCharts()
      }
      return this.chartInstance
    },

    // 更新大小
    resize() {
      this.chartInstance.resize()
    },

    // 监听元素大小
    watchEl() {
      // 给元素添加过渡
      if (this.animation) {
        document.getElementById(id).style.transition =
          'width 1s, height 1s'
      }
      const resizeObserver = new ResizeObserver(() => this.resize())
      resizeObserver.observe(document.getElementById(id))
    },

    // 显示加载状态
    showLoading() {
      if (!this.chartInstance) {
        this.initCharts()
      }
      this.chartInstance.showLoading()
    },

    // 隐藏加载状态
    hideLoading() {
      if (!this.chartInstance) {
        return
        // initCharts()
      }
      this.chartInstance.hideLoading()
    },
  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
  },
}
</script>

<style lang="scss" scoped></style>
