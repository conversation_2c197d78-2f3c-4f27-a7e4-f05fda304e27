<template>
    <el-container>
        <el-aside width="200px">
            <department-tree
                ref="departmentTree"
                @department-tree-node-click="handleDepartmentTreeNodeClick"
                :department-tree-nodes="departmentTreeNodes"
                :showActionButton="false"
                :treeHeight="leftDeptTreeHeight"
            ></department-tree>
        </el-aside>
        <el-main>
            <!-- region 查询条件表单-->
            <vxe-form
                ref="queryConditionForm"
                title-width="100"
                title-align="right"
                span="8"
                :data="queryParams"
                @submit="handleQueryConditionFormSubmit"
                @toggle-collapse="handleQueryConditionFormToggleCollapse"
            >
                <vxe-form-item field="username" title="用户名">
                    <vxe-input
                        clearable
                        placeholder="用户名"
                        v-model="queryParams.username"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="nickname" title="用户昵称">
                    <vxe-input
                        clearable
                        placeholder="用户昵称"
                        v-model="queryParams.nickname"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item align="left" collapse-node>
                    <vxe-button
                        type="submit"
                        status="primary"
                        icon="fa fa-search"
                        >查询</vxe-button
                    >
                    <vxe-button type="reset" icon="fa fa-refresh"
                        >重置</vxe-button
                    >
                </vxe-form-item>
                <vxe-form-item
                    field="createdatestart"
                    title="创建时间开始"
                    folding
                >
                    <vxe-input
                        clearable
                        placeholder="创建时间开始"
                        readonly
                        type="date"
                        v-model="queryParams.createdatestart"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item
                    field="createdateend"
                    title="创建时间结束"
                    folding
                >
                    <vxe-input
                        clearable
                        placeholder="创建时间结束"
                        readonly
                        type="date"
                        v-model="queryParams.createdateend"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="isenabled" title="是否启用" folding>
                    <vxe-select
                        clearable
                        placeholder="是否启用"
                        readonly
                        v-model="queryParams.isenabled"
                    >
                        <vxe-option value="1" label="启用"></vxe-option>
                        <vxe-option value="0" label="禁用"></vxe-option>
                    </vxe-select>
                </vxe-form-item>
            </vxe-form>
            <!-- endregion-->

            <!-- region 表格工具栏 -->
            <vxe-toolbar
                ref="toolbar"
                :refresh="{ query: loadUserTable }"
                custom
            >
                <template v-slot:buttons>
                    <el-button
                        type="primary"
                        icon="fa fa-plus"
                        @click="handleUserAdd"
                        v-btnpermission="'security:user:add'"
                    >
                        新增</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-edit"
                        @click="handleUserModify"
                         v-btnpermission="'security:user:modify'"
                    >
                        修改</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-trash"
                        @click="handleUserDelete"
                         v-btnpermission="'security:user:del'"
                    >
                        删除</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-link"
                        @click="handleDepartmentAssign"
                         v-btnpermission="'security:user:departmentassign'"
                    >
                        分配部门</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-paperclip"
                        @click="handleRoleAssign"
                         v-btnpermission="'security:user:roleassign'"
                    >
                        分配角色</el-button
                    >
                    <el-button
                        type="primary"
                        icon="el-icon-refresh-left"
                        @click="handlePassword"
                         v-btnpermission="'security:user:resetpasswd'"
                    >
                        重置密码</el-button
                    >
                </template>
            </vxe-toolbar>
            <!-- endregion -->

            <!-- region 表格 -->
            <div v-bind:style="{ height: tableHeight + 'px' }">
                <vxe-table
                    id="userTable"
                    ref="userTable"
                    v-loading="loading"
                    element-loading-text="拼命加载中"
                    border
                    auto-resize
                    resizable
                    height="auto"
                    :custom-config="{ storage: true }"
                    :data="page.records"
                    :checkbox-config="{ trigger: 'row' }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                    ></vxe-table-column>
                    <vxe-table-column
                        title="序号"
                        type="seq"
                        width="60"
                        fixed="left"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="username"
                        title="用户名"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="nickname"
                        title="用户昵称"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="createdate"
                        title="创建时间"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="departmentname"
                        title="所属部门"
                    ></vxe-table-column>
                    <vxe-table-column field="isenabled" title="是否启用">
                        <template v-slot="{ row }">
                            <vxe-switch
                                v-model="row.isenabled"
                                on-label="是"
                                off-label="否"
                                :on-value="'1'"
                                :off-value="'0'"
                                @change="
                                    ({ value, $event }) =>
                                        handleEnabledSwitch(
                                            value,
                                            $event,
                                            row.usercode
                                        )
                                "
                            ></vxe-switch>
                        </template>
                    </vxe-table-column>
                </vxe-table>
            </div>
            <!-- endregion-->

            <!-- region 分页-->
            <vxe-pager
                ref="pager"
                :current-page="queryParams.pagenumber"
                :page-size="queryParams.pagesize"
                :total="page.total"
                @page-change="handlePageChange"
            >
            </vxe-pager>
            <!-- endregion -->

            <!-- region 弹窗 分配部门-->
            <vxe-modal
                ref="departmentTreeModal"
                height="99%"
                width="300"
                position="center"
                resize
                title="分配部门"
                showFooter
            >
                <department-tree
                    ref="departmentTree"
                    show-checkbox
                    :node-key="departmentTreeConfig.id"
                    :department-tree-nodes="departmentTreeNodes2"
                    :default-checked-keys="defaultCheckedDepartmentCodes"
                    :showActionButton="false"
                ></department-tree>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.departmentTreeModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleDepartmentTreeModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->

            <!-- region 弹窗 分配角色 -->
            <vxe-modal
                ref="roleTreeModal"
                height="90%"
                width="300"
                position="center"
                resize
                title="分配角色"
                showFooter
            >
                <el-tree
                    ref="roleTree"
                    node-key="rolecode"
                    :data="roleTreeNodes"
                    show-checkbox
                    check-on-click-node
                    :props="{ children: 'children', label: 'rolenote' }"
                >
                </el-tree>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.roleTreeModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleRoleTreeModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->

            <!-- region 弹窗 用户新增|修改-->
            <vxe-modal
                ref="userFormModal"
                height="99%"
                width="600"
                position="center"
                resize
                :title="userFormModalTitle"
                showFooter
            >
                <vxe-form
                    ref="userForm"
                    title-align="right"
                    title-width="100"
                    :data="userInfo"
                    :rules="userFormRules"
                    prevent-submit
                >
                    <vxe-form-item title="用户名称" field="username" span="24">
                        <template v-slot="scope">
                            <vxe-input
                                v-model="userInfo.username"
                                placeholder="请输入用户名称"
                                clearable
                                :disabled="!!userInfo.usercode"
                                @input="$refs.userForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item title="用户昵称" field="nickname" span="24">
                        <template v-slot="scope">
                            <vxe-input
                                v-model="userInfo.nickname"
                                placeholder="请输入用户昵称"
                                clearable
                                @input="$refs.userForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item title="邮箱" field="usermail" span="24">
                        <template v-slot="scope">
                            <vxe-input
                                v-model="userInfo.usermail"
                                placeholder="请输入邮箱"
                                clearable
                                @input="$refs.userForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                    <vxe-form-item
                        title="用户排序"
                        field="ordernumber"
                        span="24"
                    >
                        <template v-slot="scope">
                            <vxe-input
                                v-model="userInfo.ordernumber"
                                type="number"
                                placeholder="请输入用户排序"
                                clearable
                                @input="$refs.userForm.updateStatus(scope)"
                            ></vxe-input>
                        </template>
                    </vxe-form-item>
                </vxe-form>
                <template v-slot:footer>
                    <vxe-button
                        type="button"
                        content="取消"
                        @click="$refs.userFormModal.close()"
                    ></vxe-button>
                    <vxe-button
                        type="button"
                        status="primary"
                        content="确定"
                        @click="handleUserFormModalConfirm"
                    ></vxe-button>
                </template>
            </vxe-modal>
            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import {
    pageUsers,
    userExists,
    saveUser,
    modifyUser,
    enabledUser,
    deleteUser,
    oneUser,
    saveUserDepartmentRelation,
    saveUserRoleRelation,
    resetPassword
} from "@/api/security/user";
import { listDepartments } from "@/api/security/department";
import { listToTreeList } from "@/utils/guozw-core";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import { listRolecodes, listRoles } from "@/api/security/role";
import { mapGetters } from "vuex";
export default {
    name: "index",
    components: { DepartmentTree },
    data() {
        return {
            queryConditionFormHeight: 0, //查询条件表单高度
            tableHeight: 0, // 表格高度
            leftDeptTreeHeight: 400, //左侧部门树的高度
            loading: true,
            departmentTreeConfig: {
                id: "departmentcode",
                parentId: "departmentparentcode",
                children: "children"
            },
            departmentTreeNodes: [], // 左侧部门树节点
            departmentTreeNodes2: [], // modal中的部门树节点
            roleTreeNodes: [],
            defaultCheckedDepartmentCodes: [], // 默认选中的部门编码
            userFormModalTitle: null,
            checkedNode: null, // 当前选中的左侧部门树节点
            page: {
                total: 0,
                records: []
            },
            // 查询条件
            queryParams: {
                pagenumber: 1,
                pagesize: 10,
                username: null,
                nickname: null,
                createdatestart: null,
                createdateend: null,
                isenabled: null,
                departmentcode: null
            },
            userInfo: {
                usercode: null,
                username: null,
                nickname: null,
                ordernumber: null
            },
            userFormRules: {
                username: [
                    { required: true, message: "必填字段" },
                    { min: 1, max: 30, message: "长度在 1 到 30 个字符" }
                ],
                nickname: [
                    { required: true, message: "必填字段" },
                    { min: 1, max: 30, message: "长度在 1 到 30 个字符" }
                ],
                ordernumber: [{ required: true, message: "必填字段" }]
            }
        };
    },
    created() {
        this.loadDepartmentTree();
        this.loadUserTable();
    },

    mounted() {
        this.setQueryConditionFormHeight();
        this.setTableHeight();
        this.windowResize();
    },
    methods: {
        // 设置查询条件表单的高度
        setQueryConditionFormHeight() {
            this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
        },
        // 设置表格的高度
        setTableHeight() {
            console.log("extraHeight=", this.extraHeight);
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.queryConditionFormHeight -
                this.$refs.toolbar.$el.offsetHeight -
                this.$refs.pager.$el.offsetHeight;
            console.log("tableHeight=", this.tableHeight);
            
            // 设置左侧部门树的高度 减去过滤框的高度
            this.leftDeptTreeHeight = window.innerHeight - this.extraHeight - 35
        },
        // 监听窗口改变
        windowResize() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    that.setTableHeight();
                    console
                        .log
                        // "窗口resize-----------------" + that.tableHeight
                        ();
                })();
            };
        },
        // 处理查询条件表单折叠按钮折叠
        handleQueryConditionFormToggleCollapse(collapse, data) {
            this.$nextTick(function() {
                this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
            });
        },
        // 处理查询条件表单提交
        handleQueryConditionFormSubmit({ data }) {
            this.queryParams.pagenumber = 1;
            this.loadUserTable();
        },
        // 加载左侧部门树
        loadDepartmentTree() {
            listDepartments({ isenabled: 1 }).then(response => {
                const treeList = listToTreeList(
                    response.data,
                    this.departmentTreeConfig.id,
                    this.departmentTreeConfig.parentId
                );
                this.departmentTreeNodes = [
                    {
                        departmentcode: this.$guozwSettings.rootId,
                        departmentname: "部门树",
                        children: treeList
                    }
                ];
                this.departmentTreeNodes2 = treeList;
            });
        },
        // 加载右侧用户表格
        loadUserTable() {
            this.loading = true;
            pageUsers(this.queryParams)
                .then(response => {
                    this.page.records = response.data.records;
                    this.page.total = response.data.total;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        // 处理页码变化
        handlePageChange({ currentPage, pageSize }) {
            this.queryParams.pagenumber = currentPage;
            this.queryParams.pagesize = pageSize;
            this.loadUserTable();
        },
        // 处理用户启用|禁用开关
        handleEnabledSwitch(value, $event, usercode) {
            enabledUser({ usercode: usercode, isenabled: value }).then(
                response => {
                    this.$XModal.message({
                        message: value === 1 ? "启用成功" : "禁用成功",
                        status: "success"
                    });
                }
            );
        },

        // 处理左侧部门树节点点击
        handleDepartmentTreeNodeClick(node) {
            if (
                node[this.departmentTreeConfig.id] ===
                this.$guozwSettings.rootId
            ) {
                this.queryParams.departmentcode = null;
            } else {
                this.queryParams.departmentcode =
                    node[this.departmentTreeConfig.id];
            }
            this.checkedNode = node
            this.loadUserTable();
        },
        handlePassword() {
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                const userInfo = {};
                userInfo.usercode = checkedRecords[0].usercode;
                this.$confirm("是否确认重置密码?", "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(function() {
                        return resetPassword(userInfo);
                    })
                    .then(res => {
                        if (res.status) {
                            console.log(res);
                            this.$refs.userFormModal.close();
                            this.loadUserTable();
                            this.$XModal.alert({
                                message: `密码重置成功，密码为：${
                                    res.data
                                } 请妥善保管。`,
                                status: "success"
                            });
                        }
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },
        // 处理新增用户按钮点击
        handleUserAdd() {
            if (!this.checkedNode) {
                this.$XModal.message({
                    message: "请选中左侧部门",
                    status: "warning"
                });
                return
            }
            Object.keys(this.userInfo).forEach(
                key => (this.userInfo[key] = "")
            );

            this.userFormModalTitle = "新增用户";
            this.$refs.userFormModal.open();

            this.$nextTick(() => {
                this.$refs.userForm.clearValidate();
            });
        },

        // 处理用户修改按钮点击
        handleUserModify() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                oneUser({ usercode: checkedRecords[0].usercode }).then(
                    response => {
                        const userInfo = response.data;
                        Object.keys(this.userInfo).forEach(
                            key => (this.userInfo[key] = userInfo[key])
                        );
                        this.userFormModalTitle = "修改用户";
                        this.$refs.userFormModal.open();

                        this.$nextTick(() => {
                            this.$refs.userForm.clearValidate();
                        });
                    }
                );
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理用户删除按钮点击
        handleUserDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                this.$XModal
                    .confirm({
                        message: "确定要删除吗？",
                        position: "center",
                        status: "warning"
                    })
                    .then(type => {
                        if (type === "confirm") {
                            deleteUser({
                                usercode: checkedRecords[0].usercode
                            }).then(response => {
                                this.$XModal.message({
                                    message: "删除成功",
                                    status: "success"
                                });
                                this.loadUserTable();
                            });
                        }
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理用户表单确定按钮点击
        handleUserFormModalConfirm() {
            this.$refs.userForm
                .validate()
                .then(() => {
                    if (this.userInfo.usercode) {
                        // 修改用户
                        modifyUser(this.userInfo).then(response => {
                            this.$refs.userFormModal.close();
                            this.$XModal.message({
                                message: "修改成功",
                                status: "success"
                            });
                            this.loadUserTable();
                        });
                    } else {
                        // 新增用户
                        userExists({ username: this.userInfo.username })
                            .then(response => {
                                return response.data;
                            })
                            .then(response => {
                                // 用户已经存在
                                if (response) {
                                    this.$XModal.message({
                                        message: "用户已存在",
                                        status: "warning"
                                    });
                                } else {
                                    saveUser(
                                        Object.assign(
                                            {
                                                departmentcode: this.queryParams
                                                    .departmentcode
                                            },
                                            this.userInfo
                                        )
                                    ).then(res => {
                                        if (res.status) {
                                            console.log(res);
                                            this.$refs.userFormModal.close();
                                            this.loadUserTable();
                                            this.$XModal.alert({
                                                message: `用户新增成功，密码为：${
                                                    res.data.userinitpassword
                                                } 请妥善保管。`,
                                                status: "success"
                                            });
                                        }
                                    });
                                }
                            });
                    }
                })
                .catch(err => {
                    console.log(err);
                });
        },

        // 处理分配部门按钮点击
        handleDepartmentAssign() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                this.$refs.departmentTreeModal.open();

                this.$nextTick(() => {
                    this.$refs.departmentTree
                        .getElTree()
                        .setCheckedKeys([checkedRecords[0].departmentcode]);
                });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理分配角色按钮点击
        handleRoleAssign() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            if (checkedRecords && checkedRecords.length === 1) {
                listRoles()
                    .then(response => {
                        this.roleTreeNodes = response.data;
                    })
                    .then(() => {
                        listRolecodes({
                            usercode: checkedRecords[0].usercode
                        }).then(response => {
                            this.$refs.roleTreeModal.open();
                            this.$nextTick(() => {
                                this.$refs.roleTree.setCheckedKeys(
                                    response.data
                                );
                            });
                        });
                    });
            } else {
                this.$XModal.message({
                    message: "请选择一条记录",
                    status: "warning"
                });
            }
        },

        // 处理部门树弹窗确定按钮点击
        handleDepartmentTreeModalConfirm() {
            // 获取选中的节点
            const checkedNodes = this.$refs.departmentTree
                .getElTree()
                .getCheckedNodes();
            console.log(checkedNodes);

            if (checkedNodes && checkedNodes.length === 1) {
                // 获取表格选中的记录
                const checkedRecords = this.$refs.userTable.getCheckboxRecords();
                saveUserDepartmentRelation({
                    userdepartmentrelationcode:
                        checkedRecords[0].userdepartmentrelationcode,
                    usercode: checkedRecords[0].usercode,
                    departmentcode:
                        checkedNodes[0][this.departmentTreeConfig.id]
                }).then(response => {
                    this.$refs.departmentTreeModal.close();
                    this.$XModal.message({
                        message: "分配成功",
                        status: "success"
                    });
                    this.loadUserTable();
                });
            } else {
                this.$XModal.message({
                    message: "请选择部门",
                    status: "warning"
                });
            }
        },
        // 处理角色树弹窗确定按钮点击
        handleRoleTreeModalConfirm() {
            // 获取选中的角色编码
            const checkedKeys = this.$refs.roleTree.getCheckedKeys();
            console.log(`选中的角色编码【${JSON.stringify(checkedKeys)}】`);
            // 获取表格选中的记录
            const checkedRecords = this.$refs.userTable.getCheckboxRecords();
            saveUserRoleRelation({
                usercode: checkedRecords[0].usercode,
                rolecodes: checkedKeys
            }).then(response => {
                this.$refs.roleTreeModal.close();
                this.$XModal.message({
                    message: "分配成功",
                    status: "success"
                });
            });
        }
    },
    computed: {
        ...mapGetters(["extraHeight"])
    },
    watch: {
        extraHeight() {
            this.setTableHeight();
        },
        // 监听查询条件表单高度变化
        queryConditionFormHeight(val) {
            console.log("监听查询条件表单高度变化--" + val);
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.$refs.toolbar.$el.offsetHeight -
                this.$refs.pager.$el.offsetHeight -
                val;
        }
    }
};
</script>
