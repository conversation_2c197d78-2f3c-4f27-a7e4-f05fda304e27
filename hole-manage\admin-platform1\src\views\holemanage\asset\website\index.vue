<template>
    <el-container>
        <el-aside width="200px">
            <DepartmentTree
                ref="departmentTree"
                :treeHeight="leftDeptTreeHeight"
                :showAssetWebsiteCount="true"
                :assetType="2"
                @department-tree-node-click="handleDepartmentTreeNodeClick"
                v-on:switchAssetStatus="switchAssetStatus"
            ></DepartmentTree>
        </el-aside>
        <el-main>
            <el-tabs
                ref="tabs"
                v-model="activeName"
                @tab-click="handleTabClick"
            >
                <el-tab-pane label="在线状态" name="1"></el-tab-pane>
                <el-tab-pane label="沉默状态" name="2"></el-tab-pane>
                <el-tab-pane label="未定义状态" name="0"></el-tab-pane>
                <el-tab-pane label="全部" name="99"></el-tab-pane>
            </el-tabs>
            <!-- region 查询条件表单-->
            <vxe-form
                ref="queryConditionForm"
                title-width="100"
                title-align="right"
                span="8"
                :data="queryParams"
                @submit="handleQueryConditionFormSubmit"
                @toggle-collapse="handleQueryConditionFormToggleCollapse"
            >
                <vxe-form-item field="asset_website_name" title="网站名称">
                    <vxe-input
                        clearable
                        placeholder="网站名称"
                        v-model="queryParams.asset_website_name"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="device_type" title="设备类型">
                    <el-select
                        v-model="queryParams.device_type"
                        clearable
                        filterable
                        placeholder="设备类型"
                        style="width:100%"
                        @change="loadTable"
                    >
                        <el-option
                            v-for="item in deviceTypeEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>
                <vxe-form-item align="right" collapse-node>
                    <vxe-button
                        type="submit"
                        status="primary"
                        icon="fa fa-search"
                        >查询</vxe-button
                    >
                    <vxe-button type="reset" icon="fa fa-refresh"
                        >重置</vxe-button
                    >
                </vxe-form-item>
                <vxe-form-item field="system_type" title="系统类型" folding>
                    <el-select
                        v-model="queryParams.system_type"
                        clearable
                        filterable
                        placeholder="系统类型"
                        style="width:100%"
                        @change="loadTable"
                    >
                        <el-option
                            v-for="item in systemTypeEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>
                <vxe-form-item field="webtype" title="WEB服务类型" folding>
                    <vxe-input
                        clearable
                        placeholder="WEB服务类型"
                        v-model="queryParams.webtype"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item
                    field="asset_web_product"
                    title="产品名称"
                    folding
                >
                    <vxe-input
                        clearable
                        placeholder="多个用逗号分隔"
                        v-model="queryParams.asset_web_product"
                    ></vxe-input>
                </vxe-form-item>
                <vxe-form-item field="contact_name" title="联系人" folding>
                    <vxe-input
                        clearable
                        placeholder="多个用逗号分隔"
                        v-model="queryParams.contact_name"
                    >
                    </vxe-input>
                </vxe-form-item>
                <vxe-form-item field="asset_status" title="资产状态" folding>
                   <el-select
                        v-model="queryParams.asset_status"
                        clearable
                        filterable
                        placeholder="资产状态"
                        style="width:100%"
                        @change="loadTable"
                    >
                        <el-option
                            v-for="item in assetStatusEnum"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </vxe-form-item>
            </vxe-form>
            <!-- endregion-->

            <!-- region 表格工具栏 -->
            <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
                <template v-slot:buttons>
                    <el-button
                        type="primary"
                        icon="fa fa-plus"
                        @click="handleAdd"
                         v-btnpermission="'holemanage:asset:website:add'"
                    >
                        新增</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-edit"
                        @click="handleBatchEdit"
                         v-btnpermission="'holemanage:asset:website:batchmodify'"
                    >
                        批量修改</el-button
                    >
                    <el-button
                        type="danger"
                        icon="fa fa-trash"
                        @click="handleBatchDelete"
                         v-btnpermission="'holemanage:asset:website:batchdel'"
                    >
                        批量删除</el-button
                    >
                </template>
            </vxe-toolbar>
            <!-- endregion -->

            <!-- region 表格 -->
            <div v-bind:style="{ height: tableHeight + 'px' }">
                <vxe-table
                    id="myTable"
                    ref="myTable"
                    v-loading="loading"
                    element-loading-text="拼命加载中"
                    border
                    auto-resize
                    resizable
                    height="auto"
                    show-overflow
                    :sort-config="{ trigger: 'cell', remote: true }"
                    @sort-change="sortChange"
                    :custom-config="{ storage: true }"
                    :data="page.records"
                    :checkbox-config="{ trigger: 'row' }"
                >
                    <vxe-table-column
                        type="checkbox"
                        width="50"
                        fixed="left"
                    ></vxe-table-column>
                    <vxe-table-column
                        title="序号"
                        type="seq"
                        width="60"
                        fixed="left"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="asset_website_name"
                        title="网站名称"
                        sortable
                    ></vxe-table-column>
                    <vxe-table-column
                        field="asset_status"
                        title="资产状态"
                        width="100"
                        sortable
                    >
                        <template v-slot="{ row }">
                            <el-tag
                                v-if="row.asset_status == 1"
                                type="success"
                                size="mini"
                                effect="dark"
                            >
                                在线
                            </el-tag>
                            <el-tag
                                v-if="row.asset_status == 2"
                                type="info"
                                size="mini"
                                effect="dark"
                            >
                                沉默
                            </el-tag>
                            <el-tag
                                v-if="row.asset_status == 0"
                                type="info"
                                size="mini"
                                effect="dark"
                            >
                                未定义
                            </el-tag>
                        </template>
                    </vxe-table-column>
                      <vxe-table-column
                        field="departmentname"
                        title="资产单位"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="device_type"
                        title="设备类型"
                        :formatter="['formatSelect', deviceTypeEnum]"
                    ></vxe-table-column>
                    <vxe-table-column
                        field="system_type"
                        title="系统类型"
                        :formatter="['formatSelect', systemTypeEnum]"
                    >
                    </vxe-table-column>
                    <vxe-table-column field="webtype" title="WEB服务类型">
                    </vxe-table-column>
                    <vxe-table-column
                        field="asset_web_product"
                        title="产品名称"
                    >
                        <template v-slot="{ row }">
                            <el-tag
                                style="margin-left:5px"
                                v-for="item in row.assetSoftWebList"
                                :key="item.asset_web_id"
                                type=""
                                effect="plain"
                            >
                                {{ item.asset_web_product }}
                            </el-tag>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="contact_name" title="联系人">
                        <template v-slot="{ row }">
                            <el-tag
                                style="margin-left:5px"
                                v-for="item in row.sysContactList"
                                :key="item.contact_id"
                                type=""
                                effect="plain"
                            >
                                {{ item.contact_name }}
                            </el-tag>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column
                        field=""
                        title="操作"
                        fixed="right"
                        width="110"
                    >
                        <template v-slot="{ row }">
                            <i
                                class="el-icon-edit"
                                style="font-size: 18px; color: #409EFF"
                                @click="handleEdit(row)"
                                 v-btnpermission="'holemanage:asset:website:modify'"
                            ></i>
                            <i
                                class="el-icon-view"
                                style="font-size: 18px; color: #409EFF"
                                @click="handleView(row)"
                            ></i>
                            <i
                                class="el-icon-delete"
                                style="font-size: 18px; color: #F56C6C"
                                @click="handleDel(row)"
                                 v-btnpermission="'holemanage:asset:website:del'"
                            ></i>
                        </template>
                    </vxe-table-column>
                </vxe-table>
            </div>
            <!-- endregion-->

            <!-- region 分页-->
            <vxe-pager
                ref="pager"
                :current-page="queryParams.pagenumber"
                :page-size="queryParams.pagesize"
                :total="page.total"
                @page-change="handlePageChange"
            >
            </vxe-pager>
            <!-- endregion -->

            <AssetWebsiteEdit
                :modalInfo="editModalInfo"
                :formDefaultData="formDefaultData"
                @refreshTable="refreshTable"
                :deviceTypeEnum="deviceTypeEnum"
                :systemTypeEnum="systemTypeEnum"
                :systemLoadEnum="systemLoadEnum"
                :protectionEnum="protectionEnum"
                :importanceEnum="importanceEnum"
                :secretEnum="secretEnum"
                :assetStatusEnum="assetStatusEnum"
            ></AssetWebsiteEdit>
            <AssetWebsiteBatchEdit
                :modalInfo="batchEditModalInfo"
                :formDefaultData="batchEdit_formDefaultData"
                @refreshTable="refreshTable"
                :deviceTypeEnum="deviceTypeEnum"
                :protectionEnum="protectionEnum"
                :importanceEnum="importanceEnum"
                :secretEnum="secretEnum"
            ></AssetWebsiteBatchEdit>
        </el-main>
    </el-container>
</template>

<script>
import {
    pageAssetWebsite,
    deleteAssetWebsite,
    batchDeleteAssetWebsite
} from "@/api/holemanage/asset";
import { listDictionarys } from "@/api/security/dictionary";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import AssetWebsiteEdit from "@/views/holemanage/asset/website/components/AssetWebsiteEdit";
import AssetWebsiteBatchEdit from "@/views/holemanage/asset/website/components/AssetWebsiteBatchEdit";
import { mapGetters } from "vuex";
export default {
    name: "index",
    components: { DepartmentTree, AssetWebsiteEdit, AssetWebsiteBatchEdit },
    data() {
        return {
            activeName: "1",
            tabsHeight: 54,
            queryConditionFormHeight: 0, //查询条件表单高度
            tableHeight: 0, // 表格高度
            leftDeptTreeHeight: 400, //左侧部门树的高度
            loading: true,
            page: {
                total: 0,
                records: []
            },
            // 查询条件
            queryParams: {
                pagenumber: 1,
                pagesize: 10,
                // 资产状态  0-未定义  1-在线 2-沉默
                asset_status: "1",
                // 资源隶属部门id
                asset_department_id: "",
                asset_website_name: "",
                device_type: "",
                system_type: "",
                webtype: "",

                asset_web_product: "",
                contact_name: ""
            },
            // 选中的部门节点
            selectedNode: {},

            // 设备类型
            deviceTypeEnum: [],
            // 系统类型
            systemTypeEnum: [],
            // 等级保护
            protectionEnum: [],
            // 重要程度
            importanceEnum: [],
            // 涉密状态
            secretEnum: [],
            // 资产状态枚举
            assetStatusEnum: [],
            // 系统负载枚举
            systemLoadEnum: [],

            // 新增 修改 弹窗的表单默认数据
            formDefaultData: {},
            // 新增 修改 弹窗的显示关闭以及标题等信息
            editModalInfo: {},
            batchEditModalInfo: {},
            batchEdit_formDefaultData: {}
        };
    },
    created() {
        this.initData();
        this.loadTable();
    },

    mounted() {
        this.setQueryConditionFormHeight();
        this.setTableHeight();
        this.windowResize();
    },
    methods: {
        initData() {
            // 加载字典
            listDictionarys().then(res => {
                res.data.forEach(item => {
                    const dict = {
                        label: item.dictionaryname,
                        value: item.dictionaryvalue
                    };
                    // 资产状态
                    if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[0]
                            .dictionarytypecode
                    ) {
                        this.assetStatusEnum.push(dict);
                    }
                    // 设备类型
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[1]
                            .dictionarytypecode
                    ) {
                        this.deviceTypeEnum.push(dict);
                    }
                    // 系统负载
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[2]
                            .dictionarytypecode
                    ) {
                        this.systemLoadEnum.push(dict);
                    }
                    // 等级保护
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[3]
                            .dictionarytypecode
                    ) {
                        this.protectionEnum.push(dict);
                    }
                    // 涉密状态
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[4]
                            .dictionarytypecode
                    ) {
                        this.secretEnum.push(dict);
                    }
                    // 重要程度
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[5]
                            .dictionarytypecode
                    ) {
                        this.importanceEnum.push(dict);
                    }
                    // 系统类型
                    else if (
                        item.dictionarytypecode ==
                        this.$guozwSettings.dictionarytypelist[6]
                            .dictionarytypecode
                    ) {
                        this.systemTypeEnum.push(dict);
                    }
                });
            });
        },
        // 处理tabs点击
        handleTabClick(tab, event) {
            this.queryParams.asset_status = this.getActiveName();
            this.loadTable();
                  // 刷新左侧部门树节点中展示的在线资产数量
             this.$refs.departmentTree.refreshAssetCount(this.getActiveName(), this.selectedNode.departmentcode)
       
        },
        // 设置查询条件表单的高度
        setQueryConditionFormHeight() {
            this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
        },
        // 设置表格的高度
        setTableHeight() {
            // console.log("extraHeight=", this.extraHeight);
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.queryConditionFormHeight -
                this.$refs.toolbar.$el.offsetHeight -
                this.$refs.pager.$el.offsetHeight -
                this.tabsHeight;

            // console.log("tableHeight=", this.tableHeight);

            // 设置左侧部门树的高度 减去过滤框的高度和操作按钮组的高度
            this.leftDeptTreeHeight = window.innerHeight - this.extraHeight - 35 - 46
        },
        // 监听窗口改变
        windowResize() {
            const that = this;
            window.onresize = () => {
                return (() => {
                    that.setTableHeight();
                    console
                        .log
                        // "窗口resize-----------------" + that.tableHeight
                        ();
                })();
            };
        },
        // 处理查询条件表单折叠按钮折叠
        handleQueryConditionFormToggleCollapse(collapse, data) {
            this.$nextTick(function() {
                this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight;
            });
        },
        // 处理查询条件表单提交
        handleQueryConditionFormSubmit({ data }) {
            this.queryParams.pagenumber = 1;
            this.loadTable();
        },

        // 处理页码变化
        handlePageChange({ currentPage, pageSize }) {
            this.queryParams.pagenumber = currentPage;
            this.queryParams.pagesize = pageSize;
            this.loadTable();
        },

        /* 字段排序 */
        sortChange(e) {
            console.log("sortChange", e);
            const { field, order } = e;

            this.queryParams.sort_field = field;
            this.queryParams.sort_order = order;

            this.loadTable();
        },

        getActiveName() {
            return this.activeName == 99 ? "" : this.activeName;
        },
        // 查询条件
        getQueryCondition() {
            let quryCondition = JSON.parse(JSON.stringify(this.queryParams));
            if (!quryCondition.asset_status) {
                quryCondition.asset_status = this.getActiveName();
            }
            return quryCondition;
        },
        // 加载表格
        loadTable() {
            this.loading = true;
            pageAssetWebsite(this.getQueryCondition())
                .then(response => {
                    this.page.records = response.data.records;
                    this.page.total = response.data.total;
                    this.loading = false;
                })
                .catch(error => {
                    this.loading = false;
                });
        },
        refreshTable() {
            this.loadTable();
            // 刷新左侧部门树节点中展示的在线资产数量
            this.$refs.departmentTree.refreshAssetCount();
        },
        // 新增部门之后，切换到未定义状态
        switchAssetStatus(departmentcode) {
            console.log("孙子组件传来消息", departmentcode);
            this.queryParams.asset_department_id = departmentcode;
            this.queryParams.asset_status = "";
            this.activeName = "0";
            // 同步左侧树的选中节点
            this.selectedNode = this.$refs.departmentTree.getElTree().getNode(departmentcode)
            this.loadTable();
        },
        // 处理左侧部门树节点点击
        handleDepartmentTreeNodeClick(node) {
            this.selectedNode = node
            this.queryParams.asset_department_id = node.departmentcode;
            this.loadTable();
        },

        // 处理新增按钮点击
        handleAdd() {
            this.formDefaultData = {
                asset_department_id: this.selectedNode.departmentcode,
                departmentname: this.selectedNode.departmentname
            };

            this.editModalInfo = {
                title: "新增网站资产",
                show: true
            };
        },

        // 处理删除
        handleDel(row) {
            this.$XModal
                .confirm({
                    message: "确定要删除吗？",
                    position: "center",
                    status: "warning"
                })
                .then(type => {
                    if (type === "confirm") {
                        let assetWebsiteIdList = [];
                        assetWebsiteIdList.push(row.asset_website_id);
                        deleteAssetWebsite({ assetWebsiteIdList }).then(res => {
                            this.$XModal.message({
                                message: "删除成功",
                                status: "success"
                            });
                            // 刷新左侧部门树节点中展示的在线资产数量
                            this.$refs.departmentTree.refreshAssetCount();
                            this.loadTable();
                        });
                    }
                });
        },

        // 处理修改
        handleEdit(row) {
            this.formDefaultData = row;
            this.editModalInfo = {
                title: "修改资产",
                show: true
            };
        },

        // 处理查看
        handleView(row) {
            this.formDefaultData = row;
            this.editModalInfo = {
                title: "查看资产",
                show: true,
                isActionView: true
            };
        },

        // 处理批量修改按钮点击
        handleBatchEdit() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.myTable.getCheckboxRecords();
            let queryCondition = null;
            let assetWebsiteIdList = [];
            let content = "";
            if (!checkedRecords || checkedRecords.length == 0) {
                content = "确定要批量修改当前查询条件下的所有记录吗？";
                queryCondition = this.getQueryCondition();
            } else {
                content = "确定要批量修改当前选中的记录吗？";
                checkedRecords.forEach(item => {
                    assetWebsiteIdList.push(item.asset_website_id);
                });
            }
            this.$XModal
                .confirm({
                    message: content,
                    position: "center",
                    status: "warning"
                })
                .then(type => {
                    if (type === "confirm") {
                        this.batchEdit_formDefaultData = {
                            assetWebsiteIdList,
                            queryCondition
                        };
                        this.batchEditModalInfo = {
                            title: "批量修改资产",
                            show: true
                        };
                    }
                });
        },

        // 处理批量删除按钮点击
        handleBatchDelete() {
            // 获取表格选中的记录
            const checkedRecords = this.$refs.myTable.getCheckboxRecords();
            let queryCondition = null;
            let assetWebsiteIdList = [];
            let content = "";
            if (!checkedRecords || checkedRecords.length == 0) {
                content = "确定要批量删除当前查询条件下的所有记录吗？";
                queryCondition = JSON.stringify(this.getQueryCondition());
            } else {
                content = "确定要批量删除当前选中的记录吗？";
                checkedRecords.forEach(item => {
                    assetWebsiteIdList.push(item.asset_website_id);
                });
            }
            this.$XModal
                .confirm({
                    message: content,
                    position: "center",
                    status: "warning"
                })
                .then(type => {
                    if (type === "confirm") {
                        batchDeleteAssetWebsite({
                            assetWebsiteIdList,
                            queryCondition
                        }).then(res => {
                            this.$XModal.message({
                                message: "批量删除成功",
                                status: "success"
                            });
                            // 刷新左侧树的资产数量
                             this.$refs.departmentTree.refreshAssetCount(
                                this.getActiveName(),
                                this.selectedNode.departmentcode
                            );
                            this.loadTable();
                        });
                    }
                });
        }
    },
    computed: {
        ...mapGetters(["extraHeight"])
    },
    watch: {
        extraHeight() {
            this.setTableHeight();
        },
        // 监听查询条件表单高度变化
        queryConditionFormHeight(val) {
            console.log("监听查询条件表单高度变化--" + val);
            this.tableHeight =
                window.innerHeight -
                this.extraHeight -
                this.$refs.toolbar.$el.offsetHeight -
                this.$refs.pager.$el.offsetHeight -
                this.tabsHeight -
                val;
        }
    }
};
</script>
<style scoped>
i {
    cursor: pointer;
    margin-right: 5px;
}
</style>
