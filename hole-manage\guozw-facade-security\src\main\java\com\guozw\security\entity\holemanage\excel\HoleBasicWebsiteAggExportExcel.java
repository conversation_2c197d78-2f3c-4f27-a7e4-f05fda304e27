package com.guozw.security.entity.holemanage.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guozw.common.core.constant.DictEnum;
import com.guozw.security.entity.Dictionary;
import com.guozw.security.vo.holemanage.HoleBasicWebsiteVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class HoleBasicWebsiteAggExportExcel {
    @ExcelProperty("网站名称")
    private String asset_website_name;
    @ExcelProperty("隶属关系")
    private String departmentname;
    @ExcelProperty("设备类型")
    private String device_type;

    @ExcelProperty("漏洞影响url数")
    private int holeUrlCount;
    @ExcelProperty("需关注漏洞总数")
    private int needNotice;
    @ExcelProperty("超危漏洞数")
    private int hole_level5;
    @ExcelProperty("高危漏洞数")
    private int hole_level4;
    @ExcelProperty("中危漏洞数")
    private int hole_level3;
    @ExcelProperty("低危漏洞数")
    private int hole_level2;


    public HoleBasicWebsiteAggExportExcel(HoleBasicWebsiteVO vo, Map<String, List<Dictionary>> dictionaryMap){
        BeanUtils.copyProperties(vo, this);
        //设备类型
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.DEVICT_TYPE.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getDevice_type())){
                this.setDevice_type(dictionary.getDictionaryname());
                break;
            }
        }
    }

}
