package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资源角色关联表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "roleresourcerelation")
public class RoleResourceRelation extends Model<RoleResourceRelation> implements Serializable {


    private static final long serialVersionUID = 7092240755267590284L;

    /**
     * 资源角色关联编码
     */
    @TableId
    private String roleresourcerelationcode;

    /**
     * 资源编码
     */
    private String resourcecode;

    /**
     * 角色编码
     */
    private String rolecode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
