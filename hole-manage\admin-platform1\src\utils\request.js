import axios from "axios";
import { MessageBox, Message } from "element-ui";
import 'xe-utils'
import VXETable from 'vxe-table'
import store from "@/store";
import { getToken } from "@/utils/auth";
import { aesUtil } from "@/utils/aes";
import { rsaUtil } from "@/utils/rsa";
import { guozwSettings } from "@/utils/guozw-settings.js";


// create an axios instance
const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
    // withCredentials: true, // send cookies when cross-domain requests
    // headers: { "Content-Type": "application/json" },
    timeout: 3000000 // request timeout
});

// request interceptor
service.interceptors.request.use(
    config => {
        // do something before request is sent
        if (store.getters.token) {
            // let each request carry token
            // ['X-Token'] is a custom headers key
            // please modify it according to the actual situation
            config.headers[guozwSettings.tokenHeaderKey] = getToken();
        }

        return config;
    },
    error => {
        // do something with request error
        console.log(error); // for debug
        return Promise.reject(error);
    }
);

// response interceptor
service.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    response => {
        const res = response.data;
        // if the custom code is not 20000, it is judged as an error.

        if (res instanceof Blob || res.code === 20000) {
            return res;
        } else {
            VXETable.modal.message({
                message: res.message,
                status: "error",
                className: "z-index-9999"
            });
            // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
            if (
                res.code === 50008 ||
                res.code === 50012 ||
                res.code === 50014
            ) {
                // to re-login
                store.dispatch("user/resetToken").then(() => {
                    location.reload();
                });
            }
            return Promise.reject(new Error(res.message || "Error"));
        }
    },
    error => {
        console.error("拦截器err" + error); // for debug
        //网络超时异常处理
        if (
            error.code === "ECONNABORTED" ||
            error.message === "Network Error" ||
            error.message.includes("timeout")
        ) {
            VXETable.modal.message({
                message: "请求超时，请稍后重试。",
                status: "error",
                className: "z-index-9999"
            });
        } else {
            VXETable.modal.message({
                message: "系统出小差了，请稍后再试，或者联系系统管理员。",
                status: "error",
                className: "z-index-9999"
            });
        }
        return Promise.reject(error);
    }
);

export default service;
