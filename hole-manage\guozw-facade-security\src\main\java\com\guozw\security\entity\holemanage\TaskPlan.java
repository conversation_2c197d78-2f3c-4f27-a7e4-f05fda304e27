package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 任务执行计划
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "task_plan")
public class TaskPlan extends BaseEntity<TaskPlan> implements Serializable {

    private static final long serialVersionUID = -5024638287145011005L;

    /**
     * 主键
     */
    @TableId
    private String task_plan_id;
    /**
     * 任务计划类型 0-关闭 1-小时 2-天 3持续监测 4闲时检测
     */
    private String task_plan_type;
    /**
     * 任务启动时间
     */
    private String task_plan_start;
    /**
     * 重复间隔
     */
    private String repeat_interval;
    /**
     * 任务计划类型为闲时检测时 检测时间段开始时间
     */
    private String task_time_begin;
    /**
     * 任务计划类型为闲时检测时 检测时间段结束时间
     */
    private String task_time_end;
    /**
     * 任务计划类型为闲时检测时 任务是否循环启动 0-不循环 1-循环
     */
    private String task_plan_loop;
    /**
     * 关联task_basic表主键
     */
    private String task_basic_id;
    /**
     * 任务执行cron表达式
     */
    private String task_cron;
    private String week;
    private String dayofmonth;
    private String timingdate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
