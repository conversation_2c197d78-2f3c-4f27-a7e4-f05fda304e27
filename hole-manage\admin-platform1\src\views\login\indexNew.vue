<template>
    <div class="login-container">
        <!-- <div class="password">
            <div id="yonghu">
                <img :src="require('../../assets/images/mm.png')" />
            </div>
            <input ref="password" type="password" placeholder="输入登录密码 " v-model.trim="loginForm.password" @keyup.enter.native="handleLogin" />
        </div>
        <button class="btn" type="button" @click="handleLogin" v-loading="loading">
            登录
        </button> -->

        <!-- <el-image style="width: 100%; height: 100%" :src="src" fit="cover"></el-image> -->

        <!-- <div class="img-item"></div> -->
        <div class="content-wrap">
            <img src="@/assets/images/login-item.png" alt="" class="bg-item" />
            <div class="form-wrap">
                <!-- <img src="@/assets/images/login-platform-name.png" alt="" class="platform-name"> -->
                <p
                    style="font-weight: bold; color:#009688;font-size:35px;letter-spacing:5px"
                >
                    漏洞管理平台
                </p>
                <el-form
                    :model="loginForm"
                    status-icon
                    ref="loginForm"
                    class="loginForm"
                >
                    <el-form-item prop="username">
                        <i class="icon el-icon-user"></i>
                        <el-input
                            ref="username"
                            v-model="loginForm.username"
                            autocomplete="off"
                            clearable
                            placeholder="请输入登录账号"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <i class="icon el-icon-lock"></i>
                        <el-input
                            ref="password"
                            type="password"
                            v-model="loginForm.password"
                            autocomplete="off"
                            show-password
                            clearable
                            placeholder="请输入登录密码"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="code" class="code-wrap">
                        <i class="icon el-icon-key"></i>
                        <el-input
                            type=""
                            v-model="loginForm.code"
                            autocomplete="off"
                            clearable
                            placeholder="请输入验证码"
                        ></el-input>
                        <img :src="kaptchaUrl" @click="refreshCode" class="code-img" />
                    </el-form-item>
                </el-form>
                <el-button @click="handleLogin">登录</el-button>
            </div>
        </div>
        <p class="company-name">技术支持：江西泰正科技有限公司</p>
    </div>
</template>

<script>
import { getBackendPublickey, getAesKey } from "@/api/security/user";
import { rsaUtil } from "@/utils/rsa";
import { aesUtil } from "@/utils/aes";
export default {
    name: "Login",
    components: {},
    props: {},
    data() {
        return {
            src: require("../../assets/images/loginBg.png"),
            kaptchaUrl: "",
            loginForm: {
                username: "",
                password: "",
                random_code: "",
                code: ""
            },
            redirect: undefined,
            otherQuery: {},
            loading: false
        };
    },

    watch: {
        $route: {
            handler: function(route) {
                const query = route.query;
                if (query) {
                    this.redirect = query.redirect;

                    this.otherQuery = this.getOtherQuery(query);
                }
            },
            immediate: true
        }
    },
    created() {
        this.getRandomCode();
        this.initCrypto();
        this.enterSearch();
    },
    mounted() {
        if (this.loginForm.username === "") {
            this.$nextTick(() => {
                this.$refs.username.focus();
            });
        } else if (this.loginForm.password === "") {
            this.$nextTick(() => {
                this.$refs.password.focus();
            });
        }
    },
    methods: {
        // 加密初始化
        async initCrypto() {
            const keyPair = rsaUtil.genKeyPair();
            const frontendPublickey = keyPair.publicKey;
            const frontendPrivatekey = keyPair.privateKey;
            console.log(`前端公钥【${frontendPublickey}】`);
            console.log(`前端私钥【${frontendPrivatekey}】`);
            await this.$store.dispatch(
                "crypto/setFrontendPublickey",
                frontendPublickey
            );
            await this.$store.dispatch(
                "crypto/setFrontendPrivatekey",
                frontendPrivatekey
            );
            const res = await getBackendPublickey();
            const backendPublickey = res.data;
            console.log(`后端公钥【${backendPublickey}】`);
            await this.$store.dispatch(
                "crypto/setBackendPublickey",
                backendPublickey
            );
            const encryptFrontendPublickey = rsaUtil.encrypt(
                frontendPublickey,
                backendPublickey
            );
            console.log(
                `使用后端公钥加密后的前端公钥【${encryptFrontendPublickey}】`
            );

            const res2 = await getAesKey({ encryptFrontendPublickey });
            let aesKey = res2.data;
            aesKey = rsaUtil.decrypt(aesKey, frontendPrivatekey);
            console.log(`AES密钥【${aesKey}】`);
            await this.$store.dispatch("crypto/setAeskey", aesKey);
        },
        getRandomCode() {
            this.loginForm.random_code = new Date().getTime();

            this.kaptchaUrl =
                process.env.VUE_APP_BASE_API +
                "/kaptcha?random_code=" +
                this.loginForm.random_code;
        },
        refreshCode() {
            this.getRandomCode();
        },
        // 表单校验
        validateForm() {
            if (!this.loginForm.username || !this.loginForm.password) {
                this.$XModal.message({
                    message: "用户名或密码不能为空",
                    status: "error"
                });
                return false;
            }

            return true;
        },

        // 处理登录
        handleLogin() {
            const valid = this.validateForm();
            if (valid) {
                this.loading = true;
                let loginForm = JSON.parse(JSON.stringify(this.loginForm));
                // AES对称加密 对密码加密
                loginForm.password = aesUtil.encrypt(
                    loginForm.password,
                    this.$store.getters.aesKey
                );
                this.$store
                    .dispatch("user/login", loginForm)
                    .then(() => {
                        this.$router.push({
                            path: this.redirect || "/",
                            query: this.otherQuery
                        });
                        this.loading = false;

                        //登录成功后取消键盘监听事件
                        document.onkeydown = null;
                    })
                    .catch(err => {
                        this.refreshCode()
                        this.loginForm.code = ''
                        this.loading = false;
                    });
            } else {
                this.$XModal.message({
                    message: "用户名和密码不能为空",
                    status: "error"
                });
                return false;
            }
        },


        // 回车登录
        enterSearch() {
            document.onkeydown = e => {
                if (e.keyCode === 13) {
                    this.handleLogin();
                }
            };
        },

        getOtherQuery(query) {
            return Object.keys(query).reduce((acc, cur) => {
                if (cur !== "redirect") {
                    acc[cur] = query[cur];
                }
                return acc;
            }, {});
        }
    }
};
</script>

<style lang="scss" scoped>
* {
    font-family: "微软雅黑";
    font-size: 16px;
    border: 0;
    padding: 0;
    margin: 0;
    color: #666;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

@media screen and (max-width: 993px) {
    .bg-item {
        display: none !important;
    }

    .content-wrap {
        justify-content: center !important;
    }
}

.login-container {
    background: url("../../assets/images/loginBg.png") no-repeat center center /
        cover;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

    .content-wrap {
        width: 1200px;
        padding: 0 50px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .bg-item {
            height: 490px;
            display: inline-block;
            max-width: 100%;
        }

        .form-wrap {
            height: 400px;
            padding: 67px 45px;
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: space-between;

            .platform-name {
                height: 36px;
            }

            .loginForm {
                width: 100%;

                .el-form-item {
                    position: relative;
                    border-bottom: 1px solid #009688;
                    margin-bottom: 10px;
                    padding-left: 25px;
                    border-radius: 0;

                    &:last-of-type {
                        margin-bottom: 0;
                    }

                    /deep/ .el-form-item__content {
                        position: unset;

                        .icon {
                            position: absolute;
                            top: 50%;
                            left: 0;
                            transform: translateY(-50%);
                            font-size: 20px;
                        }

                        .el-input__inner {
                            border: none;
                            color: #333;
                            font-size: 16px;

                            &:-webkit-autofill {
                                -webkit-box-shadow: 0 0 0px 1000px #fff inset;
                                border: none !important;
                            }
                        }
                    }
                }
                .code-wrap{
                    /deep/ .el-form-item__content{
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .el-input{
                            width: 60%;
                        }
                        .code-img{
                            flex: 1;
                            height: 35px;
                            margin-bottom: 5px;
                        }
                    }
                }
            }

            .el-button {
                width: 100%;
                padding: 18px 0;
                color: #fff;
                font-weight: bold;
                background: #009688;
                text-align: center;
            }
        }
    }

    .company-name {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 20px;
        text-align: center;
        font-size: 18px;
        color: #fff;
    }
}
</style>
