package com.guozw.common.core.aspect;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.annotation.PlatformAccessLogAnno;
import com.guozw.common.core.util.CommonUtils;
import com.guozw.common.core.util.JwtTokenUtils;
import com.guozw.log.entity.PlatformAccessLog;
import com.guozw.log.facade.PlatformAccessLogService;
import com.guozw.security.facade.UserService;
import com.guozw.security.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.NamedThreadLocal;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 平台访问日志切面
 */
@Component
@Aspect
@Slf4j
public class PlatformAccessLogAspect {
    private final ThreadLocal<DateTime> startDateThreadLocal = new NamedThreadLocal<>("ThreadLocal startDate");

    @DubboReference
    private PlatformAccessLogService platformAccessLogService;
    @DubboReference
    private UserService userService;



    /**
     * controller层切点
     */
    @Pointcut("@annotation(com.guozw.common.core.annotation.PlatformAccessLogAnno)")
    public void logAspect() {
    }

    @Before("logAspect()")
    public void doBefore(JoinPoint joinPoint) {
        log.info("进入【{}】平台访问日志切面前置通知", this.getClass().getName());
        // 线程绑定变量（该数据只有当前请求的线程可见）
        startDateThreadLocal.set(DateUtil.date());
    }

    /**
     * 结果正常返回通知
     *
     * @param res
     * @throws Throwable
     */
    @AfterReturning(returning = "res", pointcut = "logAspect()")
    public void doAfterReturning(JoinPoint joinPoint, Object res) {
        log.debug("进入【{}】平台访问日志切面AfterReturning通知", this.getClass().getName());
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 获取request
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        // 请求开始时间
        DateTime startDate = startDateThreadLocal.get();
        // 请求结束时间
        DateTime endDate = DateUtil.date();
        try {
            PlatformAccessLog platformAccessLog = new PlatformAccessLog();
            setPlatformAccessLog(joinPoint, platformAccessLog);
            platformAccessLog.setPlatformaccesslogcode(IdUtil.getSnowflakeNextId())
                    .setClientip(CommonUtils.getIpAddr(request))
                    .setRequesturi(request.getRequestURI())
                    .setRequeststartdate(startDate)
                    .setRequestenddate(endDate)
                    .setRequestcostdate(DateUtil.between(startDate, endDate, DateUnit.MS))
                    .setResultparams(JSONUtil.toJsonStr(res));
            platformAccessLog.setCreatedate(DateUtil.date());
            platformAccessLog.setModifydate(DateUtil.date());
            platformAccessLogService.savePlatformAccessLogAsync(platformAccessLog);
        } catch (Exception e) {
            log.error("异步保存平台访问日志响应参数异常【{}】", e.getMessage());
        } finally {
            removeThreadLocal();
        }
    }

    /**
     * 异常通知
     *
     * @param joinPoint
     * @param throwable
     */
    @AfterThrowing(pointcut = "logAspect()", throwing = "throwable")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable throwable) {
        log.debug("进入【{}】平台访问日志切面异常通知【{}】", this.getClass().getName(), throwable.getMessage());
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 获取request
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        // 请求开始时间
        DateTime startDate = startDateThreadLocal.get();
        // 请求结束时间
        DateTime endDate = DateUtil.date();
        try {
            PlatformAccessLog platformAccessLog = new PlatformAccessLog();
            setPlatformAccessLog(joinPoint, platformAccessLog);
            platformAccessLog.setPlatformaccesslogcode(IdUtil.getSnowflakeNextId())
                    .setClientip(CommonUtils.getIpAddr(request))
                    .setRequesturi(request.getRequestURI())
                    .setRequeststartdate(startDate)
                    .setRequestenddate(endDate)
                    .setRequestcostdate(DateUtil.between(startDate, endDate, DateUnit.MS))
                    .setExceptionname(throwable.getClass().getName())
                    .setExceptioninfo(ExceptionUtil.stacktraceToString(throwable));
            platformAccessLog.setCreatedate(DateUtil.date());
            platformAccessLog.setModifydate(DateUtil.date());
            platformAccessLogService.savePlatformAccessLogAsync(platformAccessLog);
        } catch (Exception e) {
            log.error("异步保存平台访问日志异常信息异常【{}】", e.getMessage());
        } finally {
            removeThreadLocal();
        }
    }

    private void setPlatformAccessLog(JoinPoint joinPoint, PlatformAccessLog platformAccessLog) {
        // 获得请求的类名
        String className = joinPoint.getTarget().getClass().getName();
        // 方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        // 请求的方法名
        String methodName = method.getName();
        methodName = className + StrUtil.DOT + methodName;

        if (ObjectUtil.isNull(platformAccessLog)) {
            platformAccessLog = new PlatformAccessLog();
        }

        // 设置方法名和请求参数
        platformAccessLog
                .setRequestmethod(methodName)
                .setRequestparams(JSONUtil.toJsonStr(joinPoint.getArgs()));

        // 获取方法上注解
        PlatformAccessLogAnno platformAccessLogAnno = method.getAnnotation(PlatformAccessLogAnno.class);
        if (ObjectUtil.isNotNull(platformAccessLogAnno)) {
            platformAccessLog.setAccessmodule(platformAccessLogAnno.accessModule())
                    .setAccesstype(platformAccessLogAnno.accessType())
                    .setAccessdesc(platformAccessLogAnno.accessDesc());
        }

        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            // 获取登录用户信息
            String username = JwtTokenUtils.getUsername(getHttpServletRequest());
            UserVO userInfo = userService.getUserInfoByUsername(username);
            platformAccessLog.setUsercode(userInfo.getUsercode())
                    .setUsername(userInfo.getUsername())
                    .setDepartmentcode(userInfo.getDepartmentcode())
                    .setDepartmentname(userInfo.getDepartmentname());


        } catch (Exception e) {
            log.error("获取用户信息失败【{}】", ExceptionUtil.stacktraceToString(e));
        }

    }

    private HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

    }

    private HttpServletResponse getHttpServletResponse() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
    }

    /**
     * 移除本地线程变量
     */
    private void removeThreadLocal() {
        startDateThreadLocal.remove();
    }

}
