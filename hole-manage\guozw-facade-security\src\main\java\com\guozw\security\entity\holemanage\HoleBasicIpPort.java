package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * ip资产漏洞端口信息
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "hole_basic_ip_port")
public class HoleBasicIpPort extends BaseEntity<HoleBasicIpPort> implements Serializable {


    private static final long serialVersionUID = 1679593739144365463L;

    @TableId
    private String hole_basic_ip_port_id;
    private String hole_basic_ip_id;
    private String asset_ip;
    private String asset_service_port_protocol;
    private String asset_service_port_num;
    private String asset_service_port_service;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
