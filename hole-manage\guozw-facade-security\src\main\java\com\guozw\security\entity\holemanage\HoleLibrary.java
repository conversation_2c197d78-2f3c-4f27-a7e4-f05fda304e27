package com.guozw.security.entity.holemanage;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guozw.common.core.base.BaseEntity;
import com.guozw.security.entity.WorkorderWarn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 漏洞库
 * @TableName hole_library
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value ="hole_library")
public class HoleLibrary extends BaseEntity<HoleLibrary> implements Serializable {
    /**
     * 漏洞库主键
     */
    private String hole_library_id;

    /**
     * 漏洞名称
     */
    private String hole_library_name;

    /**
     * 漏洞级别
     */
    private String hole_library_level;

    /**
     * 漏洞类型
     */
    private String hole_library_type;

    /**
     * cve
     */
    private String hole_library_cve;

    /**
     * vkb
     */
    private String hole_library_vkb;

    /**
     * cwe
     */
    private String hole_library_cwe;

    /**
     * cnnvd
     */
    private String hole_library_cnnvd;

    /**
     * cvss分值
     */
    private Double hole_library_cvss;

    /**
     * 攻击途径
     */
    private String hole_library_attack_route;

    /**
     * 攻击复杂度
     */
    private String hole_library_attack_diff;

    /**
     * 攻击认证
     */
    private String hole_library_attack_auth;

    /**
     * 机密性影响
     */
    private String hole_library_privacy;

    /**
     * 完整性影响
     */
    private String hole_library_integrity;

    /**
     * 可用性影响
     */
    private String hole_library_usability;

    /**
     * 公布时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date hole_library_public_time;

    /**
     * 漏洞补丁id，英文逗号分隔
     */
    private String hole_patch_id;

//    private Date createtime;
    /**
     * 更新时间
     */
//    private Date modifytime;

    /**
     * 漏洞说明
     */
    private byte[] hole_library_remark;

    /**
     * 修复建议
     */
    private byte[] hole_library_repair;

    /**
     * 参考信息
     */
    private byte[] hole_library_referenceinfo;



}