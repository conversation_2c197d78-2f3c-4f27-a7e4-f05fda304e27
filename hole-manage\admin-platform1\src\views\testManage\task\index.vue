<template>
  <el-container>
    <el-main>
      <!-- region 查询条件表单-->
      <vxe-form
        ref="queryConditionForm"
        title-width="100"
        title-align="right"
        span="6"
        :data="queryParams"
        @submit="handleQueryConditionFormSubmit"
        @toggle-collapse="handleQueryConditionFormToggleCollapse"
      >
        <vxe-form-item field="task_name" title="检测任务">
          <vxe-input
            v-model="queryParams.task_name"
            clearable
            placeholder="任务名称"
          />
        </vxe-form-item>
        <!-- <vxe-form-item field="task_asset_type" title="资产类型">
            <el-select
                v-model="queryParams.task_asset_type"
                clearable
                filterable
                placeholder="资产类型"
                style="width:100%"
                @change="loadTable"
            >
                <el-option
                    v-for="item in assetTypeEnum_praent"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
        </vxe-form-item> -->
        <vxe-form-item field="task_status" title="任务状态">
          <el-select
            v-model="queryParams.task_status"
            clearable
            filterable
            placeholder="任务状态"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in statusEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="task_plan_type" title="任务计划">
          <el-select
            v-model="queryParams.task_plan_type"
            clearable
            filterable
            placeholder="任务计划"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in task_plan_typeEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="task_type" title="任务类型">
          <el-select
            v-model="queryParams.task_type"
            clearable
            filterable
            placeholder="任务类型"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in taskTypeEnum"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="create_task_userid" title="创建人">
          <el-select
            v-model="queryParams.create_task_userid"
            clearable
            filterable
            placeholder="创建人"
            style="width:100%"
            @change="loadTable"
          >
            <el-option
              v-for="item in userEnum"
              :key="item.usercode"
              :label="item.nickname"
              :value="item.usercode"
            />
          </el-select>
        </vxe-form-item>
        <vxe-form-item field="reformDateGt" title="创建时间开始">
          <vxe-input
            clearable
            placeholder="创建时间开始"
            type="date"
            v-model="queryParams.create_date_start"
            @change="reformDateGtChange"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="reformDateLt" title="创建时间结束">
          <vxe-input
            clearable
            placeholder="创建时间结束"
            type="date"
            v-model="queryParams.create_date_end"
            @change="reformDateLtChange"
          ></vxe-input>
        </vxe-form-item>
        <vxe-form-item align="right">
          <vxe-button
            type="submit"
            status="primary"
            icon="fa fa-search"
          >查询
          </vxe-button>
          <vxe-button
            type="reset"
            icon="fa fa-refresh"
          >重置
          </vxe-button>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <el-button
            v-btnpermission="'testmanage:task:add'"
            type="primary"
            icon="fa fa-plus"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            v-btnpermission="'testmanage:task:start'"
            type="primary"
            icon="fa fa-hourglass-start"
            @click="handleStart"
          >
            启动
          </el-button>
          <el-button
            v-btnpermission="'testmanage:task:stop'"
            type="primary"
            icon="fa fa-stop-circle"
            @click="handleStop"
          >
            停止
          </el-button>
          <el-button
            v-btnpermission="'testmanage:task:del'"
            v-loading.fullscreen="operateBtnLoading"
            type="danger"
            icon="fa fa-trash"
            :element-loading-text="loadingText"
            @click="handleDelete"
          >
            删除
          </el-button>
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div :style="{ height: tableHeight + 'px' }">
        <vxe-table
          id="myTable"
          ref="myTable"
          v-loading="loading"
          :element-loading-text="loadingText"
          border
          auto-resize
          resizable
          height="auto"
          show-overflow
          :sort-config="{ trigger: 'cell', remote: true }"
          :custom-config="{ storage: true }"
          :data="page.records"
          :checkbox-config="{ trigger: 'row' }"
          @sort-change="sortChange"
        >
          <vxe-table-column
            type="checkbox"
            width="50"
            fixed="left"
          />
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          />
          <vxe-table-column
            field="createTaskUserName"
            title="创建人"
            width="120"
          />
          <vxe-table-column field="task_name" title="任务名称" sortable>
            <template v-slot="{ row }">
              <el-button
                type="text"
                @click="
                  $router.push({
                    path: '/testManage/childTask/index',
                    query: { id: row.task_basic_id, tool_basic_id: row.tool_basic_id }
                  })
                "
              >{{ row.task_name }}
              </el-button>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="tool_name"
            title="工具名称"
          />
          <vxe-table-column
            field=""
            title="任务计划"
            :formatter="formatterTaskPlan"
          />
          <vxe-table-column
            field="task_type"
            title="任务类型"
            :formatter="['formatSelect', taskTypeEnum]"/>
          <vxe-table-column
            field="taskResultInfo"
            title="任务结果"
          />
          <vxe-table-column
            field="task_status"
            title="任务状态"
            width="120"
            :formatter="['formatSelect', statusEnum]"
          >
            <template v-slot="{ row }">
              <el-tag
                :type="statusTagType(row.task_status)"
                size="mini"
                effect="dark"
              >{{ task_statusText(row.task_status) }}
              </el-tag>
            </template>
          </vxe-table-column>
          <vxe-table-column field="task_progress" title="任务进度">
            <template v-slot="{ row }">
              <el-progress
                :stroke-width="5"
                :percentage="parseInt(row.task_progress)"
              />
            </template>
          </vxe-table-column>
          <vxe-table-column field="createdate" title="任务创建时间">

          </vxe-table-column>
          <vxe-table-column
            field="dispatch_run_time"
            title="已用时间"
            width="120"
          />

          <vxe-table-column field title="操作" fixed="right" width="120">
            <template v-slot="{ row }">
              <i
                class="el-icon-s-operation"
                style="font-size: 18px; color: #409eff"
                @click="handlePushAsset(row)"
              />
              <i
                class="el-icon-folder-delete"
                style="font-size: 18px; color: #409eff"
                @click="handlePushBug(row)"
              />
              <i
                v-btnpermission="'testmanage:task:modify'"
                class="el-icon-edit"
                style="font-size: 18px; color: #409eff"
                @click="handleEdit(row)"
              />
              <i
                class="el-icon-s-promotion"
                style="font-size: 18px; color: #409eff"
                @click="handleRunHistory(row)"
              />
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <!-- region 分页-->
      <vxe-pager
        ref="pager"
        :current-page="queryParams.pagenumber"
        :page-size="queryParams.pagesize"
        :total="page.total"
        @page-change="handlePageChange"
      />
      <!-- endregion -->

      <TaskDialog
        :modal-info="modalInfo"
        :asset-type-enum="assetTypeEnum"
        :status-enum="statusEnum"
        :asset-ip-type-enum="asset_ip_typeEnum"
        :test-scenario-enum="testScenarioEnum"
        :test-target-enum="testTargetEnum"
        :subordination-enum="subordinationEnum"
        :user-data="userData"
        @refreshTable="loadTable"
      />

      <!-- 运行历史弹窗 -->
      <vxe-modal
        ref="operationHistoryModal"
        width="70%"
        position="center"
        resize
        title="运行历史"
        show-footer
      >
        <vxe-table
          id="operationHistoryTable"
          v-loading="operationHistoryTableoading"
          :element-loading-text="loadingText"
          border
          auto-resize
          resizable
          height="400"
          show-overflow
          :custom-config="{ storage: true }"
          :data="operationHistoryTableData.data"
        >
          <vxe-table-column
            title="序号"
            type="seq"
            width="60"
            fixed="left"
          />
          <vxe-table-column
            field="dispatch_start_time"
            title="任务开始时间"
          />
          <vxe-table-column
            field="dispatch_end_time"
            title="任务结束时间"
          />
          <vxe-table-column
            field="dispatch_run_time"
            title="所用时长"
          />
          <vxe-table-column
            field="task_status"
            title="任务状态"
            :formatter="['formatSelect', statusEnum]"
          >
            <template v-slot="{ row }">
              <el-tag :type="statusTagType(row.task_status)" size="mini" effect="dark">{{
                  task_statusText(row.task_status)
                }}
              </el-tag>
            </template>
          </vxe-table-column>
          <vxe-table-column field="task_progress" title="任务进度">
            <template v-slot="{ row }">
              <el-progress
                :stroke-width="5"
                :percentage="!row.task_progress ? 0 : row.task_progress"
              />
            </template>
          </vxe-table-column>
        </vxe-table>
        <template v-slot:footer>
          <vxe-button
            type="button"
            content="关闭"
            @click="$refs.operationHistoryModal.close()"
          />
        </template>

        <vxe-pager
          ref="operationHistoryPager"
          :current-page="operationHistoryTableData.pagenumber"
          :page-size="operationHistoryTableData.pagesize"
          :total="operationHistoryTableData.total"
          @page-change="handleHistoryPageChange"
        />
      </vxe-modal>
    </el-main>
  </el-container>
</template>

<script>
import {listDictionarys} from '@/api/security/dictionary'
import TaskDialog from './components/TaskDialog.vue'
import {mapGetters} from 'vuex'
import {listUsers, pageUsers} from '@/api/security/user'
import {listDepartments} from '@/api/security/department'
import {deleteTaskBasic, pageTaskBasic, pageTaskPlanDispatch, startTask, stopTaskBasic} from '@/api/testManage'
import {listToTreeList} from '@/utils/guozw-core'

export default {
  name: 'Task',
  components: {TaskDialog},
  props: {},
  watch: {
    extraHeight() {
      this.setTableHeight()
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        val
    }
  },
  computed: {
    ...mapGetters(['extraHeight'])
  },
  data() {
    return {
      queryConditionFormHeight: 0, // 查询条件表单高度
      tableHeight: 0, // 表格高度
      loadingText: '拼命加载中',
      loading: false,
      operationHistoryTableoading: false,
      operateBtnLoading: false,

      page: {
        total: 0,
        records: []
      },
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        task_name: '',
        task_asset_type: '',
        task_status: '',
        create_task_userid: '',
        create_date_start: '',
        create_date_end: '',
      },
      departmentTreeConfig: {
        id: 'departmentcode',
        parentId: 'departmentparentcode',
        children: 'children'
      },
      // 资产类型枚举
      assetTypeEnum_praent: [{label: '全部', value: ''}],
      assetTypeEnum: [],

      // 任务状态枚举
      statusEnum: [],
      task_plan_typeEnum: [
        {label: '立即执行', value: '1'},
        {label: '定时执行', value: '2'},
        {label: '每天执行', value: '3'},
        {label: '每周执行', value: '4'},
        {label: '每月执行', value: '5'}
      ],
      // ip类型枚举
      asset_ip_typeEnum: [],
      // 检测场景
      testScenarioEnum: [],
      // 检测目标
      testTargetEnum: [],
      //   隶属关系
      subordinationEnum: [],
      // 检测任务枚举  -- 工具组配置
      taskNameEnum: [],
      userEnum: [],
      taskTypeEnum: [
        {value: '0', label: '漏洞扫描'},
        {value: '1', label: '弱口令扫描'},
        {value: '2', label: '资产扫描'},
      ],

      // 共享用户
      userData: [],

      // 新增 修改 弹窗的表单默认数据
      formDefaultData: {},
      // 新增 修改 弹窗的显示关闭以及标题等信息
      modalInfo: {},

      //   运行历史弹窗表格数据
      operationHistoryTableData: {
        total: 0,
        pagenumber: 1,
        pagesize: 10,
        data: []
      },
      operationHistoryTableData_row: {}

    }
  },
  created() {
    this.initData()
    this.loadTable()
  },
  mounted() {
    this.setQueryConditionFormHeight()
    this.setTableHeight()
    this.windowResize()
  },
  methods: {
    initData() {
      // 加载字典
      listDictionarys().then(res => {
        res.data.forEach(item => {
          const dict = {
            label: item.dictionaryname,
            value: item.dictionaryvalue
          }
          if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[9]
              .dictionarytypecode
          ) {
            this.assetTypeEnum_praent.push(dict)
            this.assetTypeEnum.push(dict)
          } else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[10]
              .dictionarytypecode
          ) {
            this.statusEnum.push(dict)
          }
          //
          else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[11]
              .dictionarytypecode
          ) {
            this.asset_ip_typeEnum.push(dict)
          } else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[12]
              .dictionarytypecode
          ) {
            this.testScenarioEnum.push(dict)
          } else if (
            item.dictionarytypecode ==
            this.$guozwSettings.dictionarytypelist[13]
              .dictionarytypecode
          ) {
            this.testTargetEnum.push(dict)
          }
        })
      })
      //   获取共享用户数据
      pageUsers({
        pagenumber: 1,
        pagesize: 1000
      }).then(({code, data}) => {
        this.userData = data.records
      })

      //获取用户枚举
      listUsers().then(({code, data}) => {
        this.userEnum = data
      })
      //   隶属数据
      listDepartments({isenabled: 1}).then(({code, data}) => {
        if (code === 20000) {
          const treeList = listToTreeList(
            data,
            this.departmentTreeConfig.id,
            this.departmentTreeConfig.parentId
          )
          this.subordinationEnum = treeList
        }
      })
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight

      // console.log("tableHeight=", this.tableHeight);
    },
    // 监听窗口改变
    windowResize() {
      const that = this
      window.onresize = () => {
        return (() => {
          that.setTableHeight()
          console
            .log()
        })()
      }
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight = this.$refs.queryConditionForm.$el.offsetHeight
      })
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({data}) {
      this.queryParams.pagenumber = 1
      this.loadTable()
    },
    // 处理页码变化
    handlePageChange({currentPage, pageSize}) {
      this.queryParams.pagenumber = currentPage
      this.queryParams.pagesize = pageSize
      this.loadTable()
    },
    /* 字段排序 */
    sortChange(e) {
      const {field, order} = e

      this.queryParams.sort_field = field
      this.queryParams.sort_order = order

      this.loadTable()
    },
    // 查询条件
    getQueryCondition() {
      const quryCondition = JSON.parse(JSON.stringify(this.queryParams))
      return quryCondition
    },
    // 加载表格
    loadTable() {
      this.loading = true
      pageTaskBasic(this.getQueryCondition())
        .then(response => {
          response.data.records.forEach(i => {
            if (!i.task_progress) i.task_progress = 0
          })
          this.page.records = response.data.records
          this.page.total = response.data.total
          this.loading = false

          this.taskNameEnum = response.data.records.map(i => {
            return {value: i.task_basic_id, label: i.task_name}
          })
        })
        .catch(error => {
          this.loading = false
        })
    },
    formatterTaskPlan({row}) {
      let plan = ''
      console.log('格式化任务计划', row)
      if (row.taskPlan.task_plan_type == 1) {
        plan = '立即执行'
      } else if (row.taskPlan.task_plan_type == 2) {
        plan = `${row.taskPlan.timingdate} ${row.taskPlan.task_plan_start} 定时执行`
      } else if (row.taskPlan.task_plan_type == 3) {
        plan = `每天 ${row.taskPlan.task_plan_start} 定时执行`
      } else if (row.taskPlan.task_plan_type == 4) {
        plan = `每周${row.taskPlan.week} ${row.taskPlan.task_plan_start} 定时执行`
      } else if (row.taskPlan.task_plan_type == 5) {
        plan = `每月${row.taskPlan.dayofmonth}日 ${row.taskPlan.task_plan_start} 定时执行`
      }
      return plan
    },
    // 新增按钮点击
    handleAdd() {
      this.modalInfo = {
        title: '添加任务',
        show: true,
        type: 'add',
        data: {}
      }
    },
    handleStart() {
      const checkedRecords = this.$refs.myTable.getCheckboxRecords()
      if (!checkedRecords.length) {
        return this.$XModal.message({
          message: '请至少选择一条记录',
          status: 'warning'
        })
      }

      const flg = checkedRecords.some(i => i.task_status === '1')
      if (flg) {
        return this.$XModal.message({
          message: '正在运行的任务不能启动',
          status: 'warning'
        })
      }

      const taskBasicIdList = checkedRecords.map(i => i.task_basic_id)
      startTask({taskBasicIdList}).then(({code, data}) => {
        if (code === 20000) {
          this.$XModal.message({
            message: '启动成功',
            status: 'success'
          })
          this.loadTable()
        }
      })
    },
    handleStop() {
      const checkedRecords = this.$refs.myTable.getCheckboxRecords()
      if (!checkedRecords.length) {
        return this.$XModal.message({
          message: '请选择要停止的任务',
          status: 'warning'
        })
      }

      const flg = checkedRecords.some(i => i.task_status !== '1')
      if (flg) {
        return this.$XModal.message({
          message: '只能停止运行中的任务',
          status: 'warning'
        })
      }

      const taskBasicIdList = checkedRecords.map(i => i.task_basic_id)
      stopTaskBasic({taskBasicIdList}).then(({code, data}) => {
        if (code === 20000) {
          this.$XModal.message({
            message: '停止成功',
            status: 'success'
          })
          this.loadTable()
        }
      })
    },
    handleDelete() {
      const checkedRecords = this.$refs.myTable.getCheckboxRecords()
      if (!checkedRecords.length) {
        return this.$XModal.message({
          message: '请选择要删除的数据',
          status: 'warning'
        })
      }
      this.$XModal
        .confirm({
          message: '确定要删除吗？',
          position: 'center',
          status: 'warning'
        })
        .then(type => {
          if (type === 'confirm') {
            this.operateBtnLoading = true
            const taskBasicIdList = checkedRecords.map(
              i => i.task_basic_id
            )
            deleteTaskBasic({taskBasicIdList}).then(
              ({code, data}) => {
                if (code === 20000) {
                  this.$XModal.message({
                    message: '删除成功',
                    status: 'success'
                  })
                  this.loadTable()
                }
              }
            ).catch(err => {
              log.error(err)
            }).finally(() => {
              this.operateBtnLoading = false
            })
          }
        })
    },

    // 处理修改
    handleEdit(row) {
      if (row.task_status == 1) {
        return this.$XModal.message({
          message: '运行中的任务不能修改',
          status: 'warning'
        })
      }
      this.modalInfo = {
        title: '修改任务',
        show: true,
        type: 'edit',
        data: row
      }
    },

    statusTagType(val) {
      let type
      switch (val) {
        case '5':
          type = 'danger'
          break
        case '4':
          type = ''
          break
        case '3':
          type = 'info'
          break
        case '2':
          type = 'info'
          break
        case '1':
          type = 'success'
          break
        case '0':
          type = 'info'
          break
        default:
          type = 'info'
          break
      }
      return type
    },
    task_statusText(val) {
      const item = this.statusEnum.find(i => i.value === val)
      return item && item.label ? item.label : '未运行'
    },
    task_typeText(val) {
      return this.taskTypeEnum.find(i => i.value === val)
    },

    // 运行历史相关

    // 请求列表数据
    loadHistoryTableData(row) {
      pageTaskPlanDispatch({
        task_basic_id: row.task_basic_id,
        pagenumber: this.operationHistoryTableData.pagenumber,
        pagesize: this.operationHistoryTableData.pagesize
      })
        .then(({code, data}) => {
          if (code === 20000) {
            data.records.forEach(i => {
              if (!i.task_progress) i.task_progress = 0
            })
            this.operationHistoryTableData.total = data.total
            this.operationHistoryTableData.data = data.records
          }
        })
        .finally(() => (this.operationHistoryTableoading = false))
    },
    // 运行历史弹窗按钮点击
    handleRunHistory(row) {
      this.operationHistoryTableData.data = []
      this.$refs.operationHistoryModal.open()
      this.operationHistoryTableoading = true
      this.loadHistoryTableData(row)
      this.operationHistoryTableData_row = row
    },
    // 运行历史页码变化
    handleHistoryPageChange({currentPage, pageSize}) {
      this.operationHistoryTableData.pagenumber = currentPage
      this.operationHistoryTableData.pagesize = pageSize
      this.loadHistoryTableData(this.operationHistoryTableData_row)
    },
    // 跳转到漏洞页面查看漏洞
    handlePushBug(data) {
      this.$router.push({
        path: '/holemanage/hole/ip/ipDetail',
        query: {
          taskId: data.task_basic_id
        }
      })
    },
    handlePushAsset(data) {
      this.$router.push( {
        path: '/holemanage/asset/ip/index',
        query: {
          taskId: data.task_basic_id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
