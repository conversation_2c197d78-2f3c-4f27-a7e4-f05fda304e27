/**
 * Created by guozw on 2020/08/08.
 */

export const guozwSettings = {
  tokenHeaderKey: 'Authorization',
  aesKeyHeaderKey: 'AesKey',
  // 数据库中具有上下级关系的表的根节点的父id
  rootId: 0,
  // 页面的头部高度+vxe-table的toolbar高度+vxe-table的底部的分页高度
  extraHeight: 85,
  // 保留小数位数
  floatScale: 4,
  // 后台错误码
  errorCode: {
    success: { message: '请求成功', code: 20000 }
  },
  loadingId: 'loading',
  // 按钮操作类别
  operationType: [
    { title: '新增', type: 'add' },
    { title: '修改', type: 'edit' },
    { title: '查看', type: 'look' },
    { title: '权限分配', type: 'resourceAssign' }
  ],
  enumCommon: [{ label: '是', value: '1' }, { label: '否', value: '0' }],
  dictionarytypelist: [
    { dictionarytypename: '资产状态', dictionarytypecode: '1583363886477099010' },
    { dictionarytypename: '设备类型', dictionarytypecode: '1583638119824404482' },
    { dictionarytypename: '系统负载', dictionarytypecode: '1583656102483374082' },
    { dictionarytypename: '等级保护', dictionarytypecode: '1582319253844295681' },
    { dictionarytypename: '涉密状态', dictionarytypecode: '1582329732339761154' },
    { dictionarytypename: '重要程度', dictionarytypecode: '1582319959779213313' },
    { dictionarytypename: '系统类型', dictionarytypecode: '1583662568103653378' },
    { dictionarytypename: '工具品牌', dictionarytypecode: '1586912095090642946' },
    { dictionarytypename: '协议', dictionarytypecode: '1586911193533386754' },
    { dictionarytypename: '资产类型', dictionarytypecode: '1588915174602330113' },
    { dictionarytypename: '任务状态', dictionarytypecode: '1588854552235651074' },
    { dictionarytypename: 'IP类型', dictionarytypecode: '1589135759626358786' },
    { dictionarytypename: '检测场景', dictionarytypecode: '1589136265329397762' },
    { dictionarytypename: '检测目标', dictionarytypecode: '1589136684164206594' },
    { dictionarytypename: '漏洞级别', dictionarytypecode: '1590273196141015041' },
    { dictionarytypename: '发现方式', dictionarytypecode: '1590516317624668162' },
    { dictionarytypename: '发现状态', dictionarytypecode: '1590516502912241666' },
    { dictionarytypename: '验证状态', dictionarytypecode: '1590516763378520066' },
    { dictionarytypename: '复测状态', dictionarytypecode: '1590516980953845762' },
    { dictionarytypename: '修复状态', dictionarytypecode: '1590517272701243394' },
    { dictionarytypename: '关闭状态', dictionarytypecode: '1590517552696201217' },
    { dictionarytypename: '白名单状态', dictionarytypecode: '1590518230176960513' },
    { dictionarytypename: '漏洞类别', dictionarytypecode: '1592752586292015106' },
    { dictionarytypename: '处置反馈类型', dictionarytypecode: '1592057522003034114' },
    { dictionarytypename: '品牌选择隐藏', dictionarytypecode: '1598205479952367618' }
  ],
  dictTypeData:{
    holeType:{
      dictionarytypename: '漏洞类别', dictionarytypecode: '1592752586292015106'
    },
    holePlanBackType:{
      dictionarytypename: '处置反馈类型', dictionarytypecode: '1592057522003034114'
    },
    holeWhiteStatus:{
      dictionarytypename: '白名单状态', dictionarytypecode: '1590518230176960513'
    },
    holeCloseStatus:{
      dictionarytypename: '关闭状态', dictionarytypecode: '1590517552696201217'
    },
    holeRepairStatus:{
      dictionarytypename: '修复状态', dictionarytypecode: '1590517272701243394'
    },
    holeRetestStatus:{
      dictionarytypename: '复测状态', dictionarytypecode: '1590516980953845762'
    },
    holeTestStatus:{
      dictionarytypename: '验证状态', dictionarytypecode: '1590516763378520066'
    },
    holeDiscoveryMethod:{
      dictionarytypename: '发现方式', dictionarytypecode: '1590516317624668162'
    },
    holeLevel:{
      dictionarytypename: '漏洞级别', dictionarytypecode: '1590273196141015041'
    },
    deviceType:{
      dictionarytypename: '设备类型', dictionarytypecode: '1583638119824404482'
    },
    holeDiscoveryStatus:{
      dictionarytypename: '发现状态', dictionarytypecode: '1590516502912241666'
    },
    systemType:{
      dictionarytypename: '系统类型', dictionarytypecode: '1583662568103653378'
    },
    systemLoad:{
      dictionarytypename: '系统负载', dictionarytypecode: '1583656102483374082'
    },
    importance:{
      dictionarytypename: '重要程度', dictionarytypecode: '1582319959779213313'
    },
    protection:{
      dictionarytypename: '等级保护', dictionarytypecode: '1582319253844295681'
    },
    secret:{
      dictionarytypename: '涉密状态', dictionarytypecode: '1582329732339761154'
    },
    assetStatus:{
      dictionarytypename: '资产状态', dictionarytypecode: '1583363886477099010'
    },
    holeReformStatus:{
      dictionarytypename: '漏洞整改下发状态', dictionarytypecode: '1600310610623111170'
    },
    holeSourceStatus:{
      dictionarytypename: '漏洞来源', dictionarytypecode: '1626403519298490370'
    },





  }
}
