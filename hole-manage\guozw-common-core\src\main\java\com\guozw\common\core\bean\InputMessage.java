package com.guozw.common.core.bean;

import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

@Setter
@NoArgsConstructor
public class InputMessage implements HttpInputMessage {
    private InputStream body;
    private HttpHeaders headers;

    public InputMessage(InputStream body, HttpHeaders headers) {
        this.body = body;
        this.headers = headers;
    }

    @Override
    public InputStream getBody() throws IOException {
        return body;
    }

    @Override
    public HttpHeaders getHeaders() {
        return headers;
    }

    public static InputMessage of(byte[] bytes, HttpHeaders headers) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        return new InputMessage(inputStream, headers);
    }
}
