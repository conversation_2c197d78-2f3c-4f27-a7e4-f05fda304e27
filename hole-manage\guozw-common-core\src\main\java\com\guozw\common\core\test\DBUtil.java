package com.guozw.common.core.test;

import com.alibaba.druid.pool.DruidDataSourceFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by guozw on 2020/6/17.
 */
public class DBUtil {
    static DataSource dataSource;

    static {
        try {
            // 设置数据库连接信息
            Map<String, Object> map = new HashMap<>();
            map.put("driverClassName", "com.mysql.jdbc.Driver");
            map.put("url", "********************************************************************************");
            map.put("username", "root");
            map.put("password", "Guozw@0320");

            dataSource = DruidDataSourceFactory.createDataSource(map);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取数据库连接池
     * @return
     */
    public static DataSource getDataSource() {
        return dataSource;
    }

    /**
     * 获取数据库连接
     * @return
     */
    public static Connection getConnection() {
        Connection conn = null;
        try {
             conn = dataSource.getConnection();
        } catch (SQLException e) {
             e.printStackTrace();
        }
        return conn;
    }


     public static void close(Connection connection, Statement statement, ResultSet resultSet) {
         if(resultSet != null) {
             try {
                 resultSet.close();
             } catch (SQLException e) {
                 e.printStackTrace();
             }
         }
         close(connection, statement);
     }

    public static void close(Connection connection, Statement statement) {
        if (statement != null) {
            try {
                statement.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        close(connection);
    }

    public static void close(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static void rollback(Connection connection) {
        if (connection != null) {
            try {
                connection.rollback();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        Connection connection = getConnection();
        close(connection);
    }

}
