<template>
  <div class="visualization-container">
    <!-- 概述 -->
    <div class="summary-wrap">
      <ul>
        <li class="summary-item" style="background:linear-gradient(45deg,#196BE5,#2775E8)" v-loading="assetIpCountLoading">
          <img src="@/assets/visualization/ipv4Num.png" alt="" class="icon" />
          <div class="info">
            <p class="title">IPv4资产数</p>
            <p class="num">{{ summaryObj.num1 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#009688,#0F9C8F)" v-loading="assetIpOnLineCountLoading">
          <img src="@/assets/visualization/ipv4Num.png" alt="" class="icon" />
          <div class="info">
            <p class="title">在线资产数</p>
            <p class="num">{{ summaryObj.num2 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#E69F10,#EAAA28)" v-loading="vulnerableAssetsCountLoading">
          <img src="@/assets/visualization/loophole.png" alt="" class="icon" />
          <div class="info">
            <p class="title">有漏洞资产数</p>
            <p class="num">{{ summaryObj.num3 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#D42A2A,#D84040)" v-loading="leakCountLoading">
          <img src="@/assets/visualization/loopholeNum.png" alt="" class="icon" />
          <div class="info">
            <p class="title">漏洞数</p>
            <p class="num">{{ summaryObj.num4 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#009688,#0F9C8F)" v-loading="fixedLeakCountLoading">
          <img src="@/assets/visualization/repair.png" alt="" class="icon" />
          <div class="info">
            <p class="title">已修复漏洞数</p>
            <p class="num">{{ summaryObj.num5 }}</p>
          </div>
        </li>
        <li class="summary-item todayIcon" style="background:linear-gradient(45deg,transparent,transparent)">
          <div class="info">
            <p class="title">今日新增</p>
            <p class="num"></p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#009688,#0F9C8F)" v-loading="todayAssetIpOnLineCountLoading">
          <img src="@/assets/visualization/onLineAsset.png" alt="" class="icon" />
          <div class="info">
            <p class="title">在线资产数</p>
            <p class="num">{{ summaryObj.num6 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#E69F10,#EAAA28)" v-loading="todayVulnerableAssetsCountLoading">
          <img src="@/assets/visualization/loophole.png" alt="" class="icon" />
          <div class="info">
            <p class="title">有漏洞资产数</p>
            <p class="num">{{ summaryObj.num7 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#D42A2A,#D84040)" v-loading="todayLeakCountLoading">
          <img src="@/assets/visualization/loopholeNum.png" alt="" class="icon" />
          <div class="info">
            <p class="title">漏洞数</p>
            <p class="num">{{ summaryObj.num8 }}</p>
          </div>
        </li>
        <li class="summary-item" style="background:linear-gradient(45deg,#009688,#0F9C8F)" v-loading="todayFixedLeakCountLoading">
          <img src="@/assets/visualization/repair.png" alt="" class="icon" />
          <div class="info">
            <p class="title">已修复漏洞数</p>
            <p class="num">{{ summaryObj.num9 }}</p>
          </div>
        </li>
      </ul>
    </div>
    <!-- 资产分析_增加按服务排名 -->
    <div class="assetAnalysis-wrap">
      <div class="part-wrap">资产分析_增加按服务排名</div>
      <div class="chart-wrap">
        <div class="top-wrap">
          <!-- 资产排名 -->
          <div class="assetRank-chart-wrap" v-loading="loadingAssetAnalysis">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>资产排名</p>
            </div>
            <Echart id="assetRank" chartType="bar" :chartData="assetRankChartData" :chartOption="assetRankChartOption" />
          </div>
          <!-- 资产新增趋势 -->
          <div class="additionTrend-chart-wrap" v-loading="loadingAdditionTrend">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>资产新增趋势（2023年）</p>
            </div>
            <Echart id="additionTrend" chartType="bar" :chartData="additionTrendChartData" :chartOption="additionTrendChartOption" />
          </div>
          <!-- 资产设备类型排名 -->
          <div class="deviceTypeRank-chart-wrap" v-loading="loadingDeviceTypeRank">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>资产设备类型排名</p>
            </div>
            <Echart id="deviceTypeRank" chartType="bar" dataKey="资产设备类型排名" :chartData="deviceTypeRankChartData" :chartOption="deviceTypeRankChartOption" />
          </div>
          <!-- 资产按操作系统排名 -->
          <div class="operatingSystemRank-chart-wrap" v-loading="loadingOperatingSystemRank">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>资产按操作系统排名</p>
            </div>
            <Echart id="operatingSystemRank" chartType="verticalBar" dataKey="资产按操作系统排名" :chartData="operatingSystemRankChartData" :chartOption="operatingSystemRankChartOption" />
          </div>
        </div>
        <div class="bottom-wrap">
          <!-- 服务排名 -->
          <div class="servicerank-chart-wrap" v-loading="loadingServicerank">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>服务排名</p>
            </div>
            <Echart id="servicerank" chartType="bar" dataKey="服务排名" :chartData="servicerankChartData" :chartOption="servicerankChartOption" />
          </div>
        </div>
      </div>
    </div>
    <!-- 漏洞数量排名 5 IP -->
    <div class="holeNumberRankTop-wrap" v-loading="loadingHoleNumberRankTop">
      <div class="part-wrap">漏洞数量排名 5 IP</div>
      <div class="topList-wrap">
        <ul>
          <li class="item" v-for="item of IPList" :key="item.rank">
            <p class="ip">{{ item.ip }}</p>
            <p class="rank">{{ item.rank }}</p>
            <div class="num-wrap">
              <p class="text">漏洞数量</p>
              <p class="num">{{ item.num }}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 已修复漏洞分析 -->
    <div class="fixedHoleAnalysis-wrap">
      <div class="part-wrap">已修复漏洞分析</div>
      <div class="content-wrap">
        <div class="top-wrap">
          <div class="top-left-wrap">
            <ul class="box" v-loading="loadingFixedHoleAnalysis">
              <li class="item" v-for="item in analysisTotal" :key="item.id" :style="{
                  background: `linear-gradient(45deg, ${item.bg[0]}, ${item.bg[1]})`,
                }">
                <img :src="item.icon" alt="" class="icon" />
                <div class="info">
                  <p class="title">{{ item.title }}</p>
                  <p class="num">{{ item.num }}</p>
                </div>
              </li>
            </ul>
            <div class="chart-box" v-loading="loadingChart">
              <div class="chart-title-wrap">
                <img src="@/assets/visualization/chart-title-icon.png" alt="" />
                <p>漏洞类型分布</p>
              </div>
              <Echart id="holeTypeDistribute" chartType="pie" :chartData="holeTypeDistributeChartData" :chartOption="holeTypeDistributeChartOption" />
            </div>
          </div>
          <div class="top-right-wrap">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>漏洞排行</p>
            </div>
            <div class="chart-box" v-loading="loadingFixedLoopholeRank">
              <Echart id="fixedLoopholeRank" chartType="verticalBar" dataKey="漏洞排行" :chartData="fixedLoopholeRankChartData" :chartOption="fixedLoopholeRankChartOption" />
            </div>
          </div>
        </div>
        <div class="bottom-wrap">
          <div class="bottom-left-wrap">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>修复率</p>
            </div>
            <div class="chart-box" v-loading="loadingRepairRate">
              <Echart id="repairRate" chartType="verticalBar" dataKey="修复率" :chartData="repairRateChartData" :chartOption="repairRateChartOption" />
            </div>
          </div>
          <div class="bottom-right-wrap">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>漏洞变化趋势（2023年）</p>
            </div>
            <div class="chart-box" v-loading="loadingHoleChangeTrend">
              <Echart id="holeChangeTrend" chartType="line" dataKey="漏洞变化趋势" :chartData="holeChangeTrendChartData" :chartOption="holeChangeTrendChartOption" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 未修复漏洞分析 -->
    <div class="unFixedHoleAnalysis-wrap">
      <div class="part-wrap">未修复漏洞分析</div>
      <div class="content-wrap">
        <div class="top-wrap">
          <div class="top-left-wrap">
            <ul class="box" v-loading="loadingUnFixedHoleAnalysis">
              <li class="item" v-for="item in analysisTotal_unFix" :key="item.id" :style="{
                  background: `linear-gradient(45deg, ${item.bg[0]}, ${item.bg[1]})`,
                }">
                <img :src="item.icon" alt="" class="icon" />
                <div class="info">
                  <p class="title">{{ item.title }}</p>
                  <p class="num">{{ item.num }}</p>
                </div>
              </li>
            </ul>
            <div class="chart-box" v-loading="loadingUnFixHoleTypeDistribute">
              <div class="chart-title-wrap">
                <img src="@/assets/visualization/chart-title-icon.png" alt="" />
                <p>漏洞类型分布</p>
              </div>
              <Echart id="unFixHoleTypeDistribute" chartType="pie" :chartData="unFixHoleTypeDistributeChartData" :chartOption="unFixHoleTypeDistributeChartOption" />
            </div>
          </div>
          <div class="top-right-wrap">
            <div class="chart-title-wrap">
              <img src="@/assets/visualization/chart-title-icon.png" alt="" />
              <p>漏洞排行</p>
            </div>
            <div class="chart-box" v-loading="loadingUnFixedLoopholeRank">
              <Echart id="unFixedLoopholeRank" chartType="verticalBar" dataKey="漏洞排行" :chartData="unFixedLoopholeRankChartData" :chartOption="unFixedLoopholeRankChartOption" />
            </div>
          </div>
        </div>
        <div class="bottom-wrap">
          <div class="chart-title-wrap">
            <img src="@/assets/visualization/chart-title-icon.png" alt="" />
            <p>漏洞变化趋势（2023年）</p>
          </div>
          <div class="chart-box" v-loading="loadingUnFixHoleChangeTrend">
            <Echart id="unFixHoleChangeTrend" chartType="line" dataKey="未修复漏洞分析" :chartData="unFixHoleChangeTrendChartData" :chartOption="unFixHoleChangeTrendChartOption" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from '@/utils/echarts'
import Echart from "./Echart.vue";
import {
  homePageCount,
  todayHoleBasicIpCount,
  homePageRanking,
  homePageMont,
  homePageDeviceType,
  homePageOperatingSystem,
  homePagePortService,
  homePageAssetIpRate,
  homePageRepairRate,
  homePageVulnerabilityRate,
  homePageRepairLimit,
  homePageRepairDepartment,
  homePageRepairMont,
  homePageNotRepairRate,
  homePageNotVulnerabilityRate,
  homePageNotRepairLimit,
  homePageNotRepairMont,
  holeBasicIpCount,
  holeBasicVulnerableAssetsCount,
  holeBasicFixedLeakCount,
  todayHoleBasicIpCountTodayAssetIpOnLineCount,
  todayHoleBasicIpCountTodayVulnerableAssetsCount,
  todayHoleBasicIpCountTodayLeakCount,
  todayHoleBasicIpCountTodayFixedLeakCount,
} from "@/api/holemanage/homepage";
import { listDictionarys } from "@/api/security/dictionary";

const constant = {
  assetsTotalNum: "资产总数",
  onlineNum: "在线资产数",
  loopholeNum: "有漏洞资产数",
};
export default {
  name: "Login",
  components: { Echart },
  data() {
    return {
      summaryObj: {
        num1: '52653',
        num2: '52653',
        num3: '52653',
        num4: '52653',
        num5: '52653',
        num6: '52653',
        num7: '52653',
        num8: '52653',
        num9: '52653',
      },
      summaryList: [
        {
          id: 0,
          bg: ["#196BE5", "#2775E8"],
          icon: require("@/assets/visualization/ipv4Num.png"),
          title: "IPv4资产数",
          num: "52653",
        },
        {
          id: 1,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/onLineAsset.png"),
          title: "在线资产数",
          num: "52653",
        },
        {
          id: 2,
          bg: ["#E69F10", "#EAAA28"],
          icon: require("@/assets/visualization/loophole.png"),
          title: "有漏洞资产数",
          num: "52653",
        },
        {
          id: 3,
          bg: ["#D42A2A", "#D84040"],
          icon: require("@/assets/visualization/loopholeNum.png"),
          title: "漏洞数",
          num: "52653",
        },
        {
          id: 4,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/repair.png"),
          title: "已修复漏洞数",
          num: "52653",
        },
        {
          id: 5,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/onLineAsset.png"),
          title: "在线资产数",
          num: "52653",
        },
        {
          id: 6,
          bg: ["#E69F10", "#EAAA28"],
          icon: require("@/assets/visualization/loophole.png"),
          title: "有漏洞资产数",
          num: "52653",
        },
        {
          id: 7,
          bg: ["#D42A2A", "#D84040"],
          icon: require("@/assets/visualization/loopholeNum.png"),
          title: "漏洞数",
          num: "52653",
        },
        {
          id: 8,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/repair.png"),
          title: "已修复漏洞数",
          num: "52653",
        },
      ],
      loading: false,
      assetIpCountLoading: false,
      assetIpOnLineCountLoading: false,
      vulnerableAssetsCountLoading: false,
      leakCountLoading: false,
      fixedLeakCountLoading: false,
      todayAssetIpOnLineCountLoading: false,
      todayVulnerableAssetsCountLoading: false,
      todayLeakCountLoading: false,
      todayFixedLeakCountLoading: false,
      loadingAssetAnalysis: false,
      loadingAdditionTrend: false,
      loadingDeviceTypeRank: false,
      loadingOperatingSystemRank: false,
      loadingServicerank: false,
      loadingHoleNumberRankTop: false,
      loadingFixedHoleAnalysis: false,
      loadingChart: false,
      loadingFixedLoopholeRank: false,
      loadingRepairRate: false,
      loadingHoleChangeTrend: false,
      loadingUnFixedHoleAnalysis: false,
      loadingUnFixHoleTypeDistribute: false,
      loadingUnFixedLoopholeRank: false,
      loadingUnFixHoleChangeTrend: false,
      systemTypeList: [],
      deviceTypeList: [],
      vulnerabilityList: [],
      // 资产排名
      assetRankChartData: {
        XData: [
          "南昌",
          "赣州",
          "南昌",
          "宜春",
          "九江",
          "上饶",
          "吉安",
          "抚州",
          "新余",
          "萍乡",
          "景德镇",
          "鹰潭",
        ],
        [constant.assetsTotalNum]: [25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41],
        [constant.onlineNum]: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        [constant.loopholeNum]: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
      },
      assetRankChartOption: {
        legend: {
          right: "13%",
          top: "3%",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "auto",
          },
        },
        dataZoom: [
          {
            show: true,
            height: 7,
            xAxisIndex: [0],
            bottom: "8%",
            start: 0,
            end: 70,
            handleIcon:
              "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
            handleSize: "110%",
            handleStyle: {
              color: "#d3dee5",
            },
            textStyle: {
              color: "#000",
            },
            borderColor: "#90979c",
          },
          {
            type: "inside",
            show: true,
            height: 15,
            start: 1,
            end: 35,
          },
        ],
        tooltip: {
          show: true,
          trigger: "axis",
          backgroundColor: "#091637",
          textStyle: {
            color: "#FFF",
          },
          axisPointer: {
            show: true,
            type: "shadow",
          },
        },
        grid: {
          top: "15%",
          left: "1%",
          right: "1%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#000",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: "#000",
                margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            nameTextStyle: {
              color: "#000",
            },
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: false,
              lineStyle: {
                color: "#4a6f91",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#000",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: constant.assetsTotalNum,
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            color: "rgba(25,108,231,0.50)",
            tooltip: {
              // show: false,
            },
            label: {
              show: false,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
          {
            name: constant.onlineNum,
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            color: "rgba(0,150,136,0.50)",
            tooltip: {
              // show: false,
            },
            label: {
              show: false,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
          {
            name: constant.loopholeNum,
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            color: "rgba(231,159,16,0.50)",
            tooltip: {
              // show: false,
            },
            label: {
              show: false,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
        ],
      },
      // 资产新增趋势
      additionTrendChartData: {
        XData: [
          "01-23",
          "01-24",
          "01-25",
          "01-26",
          "01-27",
          "01-28",
          "01-29",
          "01-30",
          "01-31",
          "02-01",
          "02-02",
        ],
        [constant.onlineNum]: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        [constant.loopholeNum]: [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
      },
      additionTrendChartOption: {
        legend: {
          right: "13%",
          top: "3%",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "auto",
          },
        },
        dataZoom: [
          {
            show: true,
            height: 7,
            xAxisIndex: [0],
            bottom: "8%",
            start: 0,
            end: 70,
            handleIcon:
              "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
            handleSize: "110%",
            handleStyle: {
              color: "#d3dee5",
            },
            textStyle: {
              color: "#000",
            },
            borderColor: "#90979c",
          },
          {
            type: "inside",
            show: true,
            height: 15,
            start: 1,
            end: 35,
          },
        ],
        tooltip: {
          show: true,
          trigger: "axis",
          backgroundColor: "#091637",
          textStyle: {
            color: "#FFF",
          },
          axisPointer: {
            show: true,
            type: "shadow",
          },
        },
        grid: {
          top: "15%",
          left: "1%",
          right: "1%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#000",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: "#000",
                margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            nameTextStyle: {
              color: "#000",
            },
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: false,
              lineStyle: {
                color: "#4a6f91",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#000",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: constant.onlineNum,
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            color: "rgba(25,108,231,0.50)",
            tooltip: {
              // show: false,
            },
            label: {
              show: false,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
          {
            name: constant.loopholeNum,
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            color: "rgba(231,159,16,0.50)",
            tooltip: {
              // show: false,
            },
            label: {
              show: false,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
        ],
      },
      // 资产设备类型排名
      deviceTypeRankChartData: {
        XData: [
          "通用设备",
          "未定义",
          "数据库",
          "云",
          "服务器",
          "防护墙",
          "Web服务器",
          "笔记本",
          "宽带路由器",
          "网桥",
          "集中器",
        ],
        seriesData: [21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11],
      },
      deviceTypeRankChartOption: {
        dataZoom: [
          {
            show: true,
            height: 7,
            xAxisIndex: [0],
            bottom: "8%",
            start: 0,
            end: 70,
            handleIcon:
              "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
            handleSize: "110%",
            handleStyle: {
              color: "#d3dee5",
            },
            textStyle: {
              color: "#000",
            },
            borderColor: "#90979c",
          },
          {
            type: "inside",
            show: true,
            height: 15,
            start: 1,
            end: 35,
          },
        ],
        tooltip: {
          show: true,
          trigger: "axis",
          backgroundColor: "#091637",
          textStyle: {
            color: "#FFF",
          },
          axisPointer: {
            show: true,
            type: "shadow",
          },
        },
        grid: {
          top: "15%",
          left: "1%",
          right: "1%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#000",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: "#000",
                margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            nameTextStyle: {
              color: "#000",
            },
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: false,
              lineStyle: {
                color: "#4a6f91",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#000",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "",
            type: "bar",
            barWidth: "30%",
            barGap: "0%",
            itemStyle: {
              color: function ({ dataIndex }) {
                let color;
                switch (dataIndex) {
                  case 0:
                    color = "rgba(212,42,42,0.50)";
                    break;
                  case 1:
                    color = " rgba(231,159,16,0.50)";
                    break;
                  case 2:
                    color = "rgba(25,108,231,0.50)";
                    break;
                  default:
                    color = "rgba(0,150,136,0.50)";
                    break;
                }
                return color;
              },
            },
            tooltip: {
              // show: false,
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
        ],
      },
      // 资产按操作系统排名
      operatingSystemRankChartData: {
        XData: ['Windows', 'Linux', 'Centos', 'Embedded', '其他'],
        seriesData: [72, 60, 52, 50, 44],
      },
      operatingSystemRankChartOption: {
        grid: {
          left: '1%',
          top: '10%',
          right: '10%',
          bottom: '1%',
          containLabel: false
        },
        xAxis: [{
          show: false,
        }],
        yAxis: [{
          axisTick: 'none',
          axisLine: 'none',
          inverse: true,
          offset: '27',
          axisLabel: {
            textStyle: {
              color: '#000',
            }
          },
          data: []
        }, { //名称
          type: 'category',
          offset: -10,
          position: "left",
          axisLabel: {
            color: `#000`,
          },
          axisLine: {
            show: false
          },
          inverse: false,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: ["#000"],
            align: "left",
            verticalAlign: "bottom",
            lineHeight: 52,
          },
          data: []
        },
        {
          type: "category",
          inverse: true,
          axisTick: "none",
          axisLine: "none",
          show: true,
          axisLabel: {
            textStyle: {
              color: "#000",
            }
          },
          data: []
        },
        ],
        series: [{
          name: '',
          type: 'bar',
          data: [],
          barWidth: "30%",
          barGap: "15%",
          label: {
            normal: {
              show: true,
              position: ['103%', 0],
              textStyle: {
                color: 'auto',
              }
            }
          },
          itemStyle: {
            normal: {
              color: ({ dataIndex }) => {
                if (dataIndex == 0) return '#E99494'
                if (dataIndex == 1) return '#F3CF87'
                if (dataIndex == 2) return '#8CB5F3'
                return '#7FCAC3'
              }
            },
          }
        }
        ]
      },
      // 服务排名
      servicerankChartData: {
        XData: [
          "tcp",
          "http",
          "https",
          "tomcat",
          "ng",
          "telnet",
          "weblogic",
          "oracle",
          "mysql",
          "ping",
        ],
        seriesData: [21, 20, 19, 18, 17, 16, 15, 14, 13, 12],
      },
      servicerankChartOption: {
        tooltip: {
          trigger: "axis",
          backgroundColor: "#091637",
          textStyle: {
            color: "#FFF",
          },
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: "20%",
          left: "1%",
          right: "1%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: "#000",
              },
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              // rotate: 40,
              textStyle: {
                color: "#000",
                // margin: 15,
              },
            },
            axisTick: {
              show: false,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            // name: "降损电量（kWh）",
            nameTextStyle: {
              color: "#000",
            },
            min: 0,
            // max: 140,
            splitNumber: 7,
            splitLine: {
              show: false,
              lineStyle: {
                color: "#4a6f91",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: "#000",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "",
            type: "bar",
            barWidth: "13",
            itemStyle: {
              color: function ({ dataIndex }) {
                let color;
                switch (dataIndex) {
                  case 0:
                    color = "rgba(212,42,42,0.50)";
                    break;
                  case 1:
                    color = " rgba(231,159,16,0.50)";
                    break;
                  case 2:
                    color = "rgba(25,108,231,0.50)";
                    break;
                  default:
                    color = "rgba(0,150,136,0.50)";
                    break;
                }
                return color;
              },
            },
            tooltip: {
              // show: false,
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#000",
              },
            },
            data: [],
          },
        ],
      },

      // 漏洞数量排名 5 IP
      IPList: [
        { ip: "0", rank: "01", num: "0" },
        { ip: "0", rank: "02", num: "0" },
        { ip: "0", rank: "03", num: "0" },
        { ip: "0", rank: "04", num: "0" },
        { ip: "0", rank: "05", num: "0" },
        // { ip: "0", rank: "06", num: "0" },
        // { ip: "0", rank: "07", num: "0" },
        // { ip: "0", rank: "08", num: "0" },
      ],
      // 已修复漏洞分析
      analysisTotal: [
        {
          id: 0,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/repair.png"),
          title: "已修复漏洞数",
          num: "938",
        },
        {
          id: 1,
          bg: ["#009688", "#0F9C8F"],
          icon: require("@/assets/visualization/repairRate.png"),
          title: "修复率",
          num: "21%",
        },
      ],
      holeTypeDistributeChartData: {
        data: [
          {
            name: "严重",
            value: 2160,
          },
          {
            name: "高危",
            value: 894,
          },
          {
            name: "中危",
            value: 233,
          },
          {
            name: "低危",
            value: 195,
          },
          {
            name: "未定义",
            value: 19225,
          },
        ],
      },
      holeTypeDistributeChartOption: {
        legend: {
          left: 'center',
          bottom: '1%',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: 'auto',
          },
        },
        tooltip: {
          show: 'true',
          trigger: 'item',
          backgroundColor: '#091637',
          textStyle: {
            color: '#FFF',
          },
        },
        series: [
          {
            type: "pie",
            minAngle: 10, //最小的扇区角度（0 ~ 360）
            radius: ["40%", "65%"],
            center: ["50%", "50%"],
            clockwise: false, //饼图的扇区是否是顺时针排布
            avoidLabelOverlap: false,
            // roseType: 'area',
            itemStyle: {
              normal: {
                color: function ({ dataIndex }) {
                  let color;
                  switch (dataIndex) {
                    case 0:
                      color = "#E99494";
                      break;
                    case 1:
                      color = "#F3CF87";
                      break;
                    case 2:
                      color = "#8CB5F3";
                      break;
                    case 3:
                      color = "#7FCAC3";
                      break;
                    default:
                      color = "#7FCAC3";
                      break;
                  }
                  return color;
                },
                borderColor: "#fff",
                borderWidth: 2,
              },
            },
            label: {
              show: false,
              normal: {
                position: "outside",
                lineHeight: 15,
                color: "auto",
                formatter: "{b}\n{d}%",
              },
              emphasis: {
                show: true,
              },
            },
            labelLine: {
              normal: {
                smooth: true,
              }
            },
            data: [],
          },
        ],
      },
      // 已修复漏洞分析-漏洞排名
      fixedLoopholeRankChartData: {
        XData: ['OpenSSL 信息泄露漏洞 (CVE-2016-2183)', 'SNMP弱口令', 'Apache Tomcat 文件读取/文件包含漏洞', 'RDP远程命令执行漏洞', 'SSL/TLS RC4 信息泄露漏洞（CVE-2013-2566）'],
        seriesData: [72, 60, 52, 50, 44],
      },
      fixedLoopholeRankChartOption: {
        grid: {
          left: '1%',
          top: '10%',
          right: '10%',
          bottom: '1%',
          containLabel: false
        },
        xAxis: [{
          show: false,
        }],
        yAxis: [{
          axisTick: 'none',
          axisLine: 'none',
          inverse: true,
          offset: '27',
          axisLabel: {
            textStyle: {
              color: '#000',
            }
          },
          data: []
        }, { //名称
          type: 'category',
          offset: -10,
          position: "left",
          axisLabel: {
            color: `#000`,
          },
          axisLine: {
            show: false
          },
          inverse: false,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: ["#000"],
            align: "left",
            verticalAlign: "bottom",
            lineHeight: 42,
          },
          data: []
        },
        {
          type: "category",
          inverse: true,
          axisTick: "none",
          axisLine: "none",
          show: true,
          axisLabel: {
            textStyle: {
              color: "#000",
            }
          },
          data: []
        },
        ],
        series: [{
          name: '',
          type: 'bar',
          data: [],
          barWidth: "30%",
          barGap: "15%",
          label: {
            normal: {
              show: true,
              position: ['103%', 0],
              textStyle: {
                color: 'auto',
              }
            }
          },
          itemStyle: {
            normal: {
              color: ({ dataIndex }) => {
                if (dataIndex == 0) return '#E99494'
                if (dataIndex == 1) return '#F3CF87'
                if (dataIndex == 2) return '#8CB5F3'
                return '#7FCAC3'
              }
            },
          }
        }
        ]
      },
      // 修复率
      repairRateChartData: {
        XData: ['南昌', '赣州', '九江', '宜春', '鹰潭', '上饶', '景德镇'],
        seriesData: [72, 60, 52, 50, 44, 32, 20],
      },
      repairRateChartOption: {
        dataZoom: [{
          type: 'slider',
          yAxisIndex: [0,1], // 关键点：绑定到同一个y轴
          filterMode: 'filter',
          zoomLock: true,
          start: 0,
          end: 30,
          handleSize: '100%',
          handleStyle: {
            color: '#666',
            shadowBlur: 3
          },
          maxValueSpan: 5, // 固定显示10条数据（根据需求调整）
          minValueSpan: 5, // 与max相同确保不可缩放
          brushSelect: false, // 禁用框选
          fillerColor: 'rgba(150,150,150,0.2)',
          showDetail: false
        }],
        grid: {
          left: '1%',
          top: '10%',
          right: '10%',
          bottom: '15%',
          containLabel: false
        },
        xAxis: [{
          show: false,
        }],
        yAxis: [// 第一个Y轴（用于柱子）
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              show: false // 隐藏这个轴的标签
            }
          },
          { //名称
          type: 'category',
          offset: -10,
          position: "left",
          axisLabel: {
            color: `#000`,
          },
          axisLine: {
            show: false
          },
          inverse: false,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: ["#000"],
            align: "left",
            verticalAlign: "bottom",
            lineHeight: 20,
          },
          data: []
        }
        ],
        series: [{
          name: '',
          type: 'bar',
          yAxisIndex: 0,
          data: [],
          barWidth: "30%",
          barGap: "15%",
          label: {
            normal: {
              show: true,
              position: ['103%', 0],
              textStyle: {
                color: 'auto',
              }
            }
          },
          itemStyle: {
            normal: {
              color: ({ dataIndex }) => {
                if (dataIndex == 0) return '#E99494'
                if (dataIndex == 1) return '#F3CF87'
                if (dataIndex == 2) return '#8CB5F3'
                return '#7FCAC3'
              }
            },
          }
        }
        ]
      },
      // 漏洞变化趋势（2023年）
      holeChangeTrendChartData: {
        XData: ['01-31', '02-01', '02-02', '02-03', '02-04', '02-05', '02-06', '02-07', '02-08', '02-09'],
        seriesData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      },
      holeChangeTrendChartOption: {
        tooltip: {
          show: false,
          trigger: 'axis',
          backgroundColor: '#091637',
          textStyle: {
            color: '#fff',
          },
          axisPointer: {
            show: true,
            type: 'shadow',
          },
        },
        legend: {
          show: false,
          right: '4%',
          top: '-1%',
          textStyle: {
            color: 'auto',
          },
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              // rotate: 40,
              color: '#000',
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#00BFF3',
                opacity: 0.1,
              },
            },
            boundaryGap: false,
            data: [],
          },
        ],
        yAxis: [
          {
            type: 'value',
            // name: '数据/条',
            nameTextStyle: {
              color: '#000',
              align: 'right',
            },
            min: 0,
            splitNumber: 4,
            splitLine: {
              show: true,
              lineStyle: {
                color: '#00BFF3',
                opacity: 0.1,
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              margin: 20,
              textStyle: {
                color: '#000',
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            showAllSymbol: true,
            symbol: 'circle',
            smooth: false,
            lineStyle: {
              normal: {
                color: '#009688',
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgb(15, 156, 143, .6)'
                }, {
                  offset: 0.8,
                  color: 'rgba(73, 169, 159, 0.2)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#009688',
              },
            },
            itemStyle: {
              color: 'auto',
              borderColor: '#2CABE3',
            },
            data: [],
          },
        ],
      },
      // 未修复漏洞分析
      analysisTotal_unFix: [
        {
          id: 0,
          bg: ["#D42A2A", "#D84040"],
          icon: require("@/assets/visualization/loopholeNum.png"),
          title: "未修复漏洞数",
          num: "938",
        },
        {
          id: 1,
          bg: ["#D42A2A", "#D84040"],
          icon: require("@/assets/visualization/notRepairRate.png"),
          title: "未修复率",
          num: "21.%",
        },
      ],
      unFixHoleTypeDistributeChartData: {
        data: [
          {
            name: "严重",
            value: 2160,
          },
          {
            name: "高危",
            value: 894,
          },
          {
            name: "中危",
            value: 233,
          },
          {
            name: "低危",
            value: 195,
          },
        ],
      },
      unFixHoleTypeDistributeChartOption: {
        legend: {
          left: 'center',
          bottom: '1%',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: 'auto',
          },
        },
        tooltip: {
          show: 'true',
          trigger: 'item',
          backgroundColor: '#091637',
          textStyle: {
            color: '#FFF',
          },
        },
        series: [
          {
            type: "pie",
            minAngle: 10, //最小的扇区角度（0 ~ 360）
            radius: ["40%", "65%"],
            center: ["50%", "50%"],
            clockwise: false, //饼图的扇区是否是顺时针排布
            avoidLabelOverlap: false,
            // roseType: 'area',
            itemStyle: {
              normal: {
                color: function ({ dataIndex }) {
                  let color;
                  switch (dataIndex) {
                    case 0:
                      color = "#E99494";
                      break;
                    case 1:
                      color = "#F3CF87";
                      break;
                    case 2:
                      color = "#8CB5F3";
                      break;
                    case 3:
                      color = "#7FCAC3";
                      break;
                    default:
                      color = "#7FCAC3";
                      break;
                  }
                  return color;
                },
                borderColor: "#fff",
                borderWidth: 2,
              },
            },
            label: {
              show: false,
              normal: {
                position: "outside",
                lineHeight: 15,
                color: "auto",
                formatter: "{b}\n{d}%",
              },
              emphasis: {
                show: true,
              },
            },
            labelLine: {
              normal: {
                smooth: true,
              }
            },
            data: [],
          },
        ],
      },
      // 未修复漏洞分析-漏洞排名
      unFixedLoopholeRankChartData: {
        XData: ['OpenSSL 信息泄露漏洞 (CVE-2016-2183)', 'SNMP弱口令', 'Apache Tomcat 文件读取/文件包含漏洞', 'RDP远程命令执行漏洞', 'SSL/TLS RC4 信息泄露漏洞（CVE-2013-2566）'],
        seriesData: [72, 60, 52, 50, 44],
      },
      unFixedLoopholeRankChartOption: {
        grid: {
          left: '1%',
          top: '10%',
          right: '10%',
          bottom: '1%',
          containLabel: false
        },
        xAxis: [{
          show: false,
        }],
        yAxis: [{
          axisTick: 'none',
          axisLine: 'none',
          inverse: true,
          offset: '27',
          axisLabel: {
            textStyle: {
              color: '#000',
            }
          },
          data: []
        }, { //名称
          type: 'category',
          offset: -10,
          position: "left",
          axisLabel: {
            color: `#000`,
          },
          axisLine: {
            show: false
          },
          inverse: false,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: ["#000"],
            align: "left",
            verticalAlign: "bottom",
            lineHeight: 42,
          },
          data: []
        },
        {
          type: "category",
          inverse: true,
          axisTick: "none",
          axisLine: "none",
          show: true,
          axisLabel: {
            textStyle: {
              color: "#000",
            }
          },
          data: []
        },
        ],
        series: [{
          name: '',
          type: 'bar',
          data: [],
          barWidth: "30%",
          barGap: "15%",
          label: {
            normal: {
              show: true,
              position: ['103%', 0],
              textStyle: {
                color: 'auto',
              }
            }
          },
          itemStyle: {
            normal: {
              color: ({ dataIndex }) => {
                if (dataIndex == 0) return '#E99494'
                if (dataIndex == 1) return '#F3CF87'
                if (dataIndex == 2) return '#8CB5F3'
                return '#7FCAC3'
              }
            },
          }
        }
        ]
      },
      // 漏洞变化趋势（2023年）
      unFixHoleChangeTrendChartData: {
        XData: ['01-31', '02-01', '02-02', '02-03', '02-04', '02-05', '02-06', '02-07', '02-08', '02-09'],
        seriesData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      },
      unFixHoleChangeTrendChartOption: {
        tooltip: {
          show: false,
          trigger: 'axis',
          backgroundColor: '#091637',
          textStyle: {
            color: '#fff',
          },
          axisPointer: {
            show: true,
            type: 'shadow',
          },
        },
        legend: {
          show: false,
          right: '4%',
          top: '-1%',
          textStyle: {
            color: 'auto',
          },
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              // rotate: 40,
              color: '#000',
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#00BFF3',
                opacity: 0.1,
              },
            },
            boundaryGap: false,
            data: [],
          },
        ],
        yAxis: [
          {
            type: 'value',
            // name: '数据/条',
            nameTextStyle: {
              color: '#000',
              align: 'right',
            },
            min: 0,
            splitNumber: 4,
            splitLine: {
              show: true,
              lineStyle: {
                color: '#00BFF3',
                opacity: 0.1,
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              margin: 20,
              textStyle: {
                color: '#000',
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            showAllSymbol: true,
            symbol: 'circle',
            smooth: false,
            lineStyle: {
              normal: {
                color: '#009688',
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgb(15, 156, 143, .6)'
                }, {
                  offset: 0.8,
                  color: 'rgba(73, 169, 159, 0.2)'
                }], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#009688',
              },
            },
            itemStyle: {
              color: 'auto',
              borderColor: '#2CABE3',
            },
            data: [],
          },
        ],
      },

    }
  },
  watch: {},
  created() {
    listDictionarys().then(res => {
      this.loading = true;
      res.data.forEach(item => {
        const dict = {
          label: item.dictionaryname,
          value: item.dictionaryvalue
        };
        //  设备类型
        if (
          item.dictionarytypecode ==
          this.$guozwSettings.dictionarytypelist[1]
            .dictionarytypecode
        ) {

          this.deviceTypeList.push(dict);
        } else if (
          item.dictionarytypecode ==
          this.$guozwSettings.dictionarytypelist[6]
            .dictionarytypecode
        ) {

          this.systemTypeList.push(dict);

        }
        else if (
          item.dictionarytypecode ==
          this.$guozwSettings.dictionarytypelist[14]
            .dictionarytypecode
        ) {

          this.vulnerabilityList.push(dict);

        }
      })


      this.loadTable();
    })
  },
  mounted() {
    // this.getSummaryListData();
  },
  destroyed() { },
  methods: {
    getSummaryListData() {
      // 模拟数据请求
      setTimeout(() => {
        this.summaryList.splice(5, 0, {
          id: 9,
          bg: ["transparent", "transparent"],
          icon: "",
          title: "今日新增",
          num: "",
          class: "todayIcon",
        });
      }, 2000);
    },
    // 加载表格
    loadTable() {
      // this.loading = true;
      //头部各总数
      // homePageCount(null)
      //   .then(response => {

      //     this.summaryObj.num1 = response.data.assetIpCount;
      //     this.summaryObj.num2 = response.data.assetIpOnLineCount;
      //     this.summaryObj.num3 = response.data.vulnerableAssetsCount;
      //     this.summaryObj.num4 = response.data.leakCount;
      //     this.summaryObj.num5 = response.data.fixedLeakCount;



      //     this.loading = false;
      //   })
      //   .catch(error => {
      //     this.loading = false;
      //   })
      //   .finally(() => {

      //     this.loading = false;
      //   });
      //总数信息
      this.homePageCount();
      // 漏洞数
      this.holeBasicIpCount();
      // 已修复漏洞数
      this.holeBasicFixedLeakCount();
      // 有漏洞资产数
      this.holeBasicVulnerableAssetsCount();

      // this.todayHoleBasicIpCount();
      // 今日新增在线资产数
      this.todayHoleBasicIpCountTodayAssetIpOnLineCount();
      // 今日新增有漏洞资产数
      this.todayHoleBasicIpCountTodayVulnerableAssetsCount();
      // 今日新增漏洞数
      this.todayHoleBasicIpCountTodayLeakCount();
      // 今日新增已修复漏洞数
      this.todayHoleBasicIpCountTodayFixedLeakCount();
      // 资产排名
      this.homePageRanking();
      // 资产新增趋势
      this.homePageChartData();
      // 资产设备类型排名
      this.deviceTypeRank();
      // 资产按操作系统排名
      this.homePageOperatingSystem();
      // 服务排名
      this.homePageServicerank();
      // 漏洞数量排名
      this.homePageIPRate();
      // 已修复漏洞分析
      this.homePageRepairRate();
      // 已修复漏洞级别
      this.homePageVulnerabilityRate();
      // 已修复漏洞排行前6
      this.homePageRepairLimit();
      // 已修复部门修复率
      this.homePageRepairDepartment();
      // 已修复漏洞变化趋势
      this.homePageRepairMont();
      // 未修复漏洞分析
      this.homePageNotRepairRate();
      // 未修复漏洞级别
      this.homePageNotVulnerabilityRate();
      // 未修复漏洞排行前5
      this.homePageNotRepairLimit();
      // 未修复漏洞变化趋势
      this.homePageNotRepairMont();
    },
    homePageCount() {
      this.assetIpCountLoading = true;
      this.assetIpOnLineCountLoading = true;
      homePageCount().then(response => {

        this.summaryObj.num1 = response.data.assetIpCount;
        this.summaryObj.num2 = response.data.assetIpOnLineCount;

        this.assetIpCountLoading = false;
        this.assetIpOnLineCountLoading = false;
      })
        .catch(error => {
          this.assetIpCountLoading = false;
          this.assetIpOnLineCountLoading = false;
        })
        .finally(() => {
          this.assetIpCountLoading = false;
          this.assetIpOnLineCountLoading = false;
        });
    },
    holeBasicVulnerableAssetsCount() {
      this.vulnerableAssetsCountLoading = true;
      holeBasicVulnerableAssetsCount().then(response => {

        this.summaryObj.num3 = response.data.vulnerableAssetsCount;

        this.vulnerableAssetsCountLoading = false;
      })
        .catch(error => {
          this.vulnerableAssetsCountLoading = false;
        })
        .finally(() => {
          this.vulnerableAssetsCountLoading = false;
        });
    },
    holeBasicIpCount() {
      this.leakCountLoading = true;
      holeBasicIpCount().then(response => {

        this.summaryObj.num4 = response.data.leakCount;

        this.leakCountLoading = false;
      })
        .catch(error => {
          this.leakCountLoading = false;
        })
        .finally(() => {
          this.leakCountLoading = false;
        });
    },
    holeBasicFixedLeakCount() {
      this.fixedLeakCountLoading = true;
      holeBasicFixedLeakCount().then(response => {

        this.summaryObj.num5 = response.data.fixedLeakCount;

        this.fixedLeakCountLoading = false;
      })
        .catch(error => {
          this.fixedLeakCountLoading = false;
        })
        .finally(() => {
          this.fixedLeakCountLoading = false;
        });
    },
    todayHoleBasicIpCountTodayAssetIpOnLineCount() {
      this.todayAssetIpOnLineCountLoading = true;
      todayHoleBasicIpCountTodayAssetIpOnLineCount().then(response => {

        this.summaryObj.num6 = response.data.todayAssetIpOnLineCount;

        this.todayAssetIpOnLineCountLoading = false;
      })
        .catch(error => {
          this.todayAssetIpOnLineCountLoading = false;
        })
        .finally(() => {
          this.todayAssetIpOnLineCountLoading = false;
        });
    },
    //
    todayHoleBasicIpCountTodayVulnerableAssetsCount() {
      this.todayVulnerableAssetsCountLoading = true;
      todayHoleBasicIpCountTodayVulnerableAssetsCount().then(response => {

        this.summaryObj.num7 = response.data.todayVulnerableAssetsCount;

        this.todayVulnerableAssetsCountLoading = false;
      })
        .catch(error => {
          this.todayVulnerableAssetsCountLoading = false;
        })
        .finally(() => {
          this.todayVulnerableAssetsCountLoading = false;
        });
    },
    todayHoleBasicIpCountTodayLeakCount() {
      this.todayLeakCountLoading = true;
      todayHoleBasicIpCountTodayLeakCount().then(response => {

        this.summaryObj.num8 = response.data.todayLeakCount;

        this.todayLeakCountLoading = false;
      })
        .catch(error => {
          this.todayLeakCountLoading = false;
        })
        .finally(() => {
          this.todayLeakCountLoading = false;
        });
    },
    todayHoleBasicIpCountTodayFixedLeakCount() {
      this.todayFixedLeakCountLoading = true;
      todayHoleBasicIpCountTodayFixedLeakCount().then(response => {
        this.summaryObj.num9 = response.data.todayFixedLeakCount;
        this.todayFixedLeakCountLoading = false;
      })
        .catch(error => {
          this.todayFixedLeakCountLoading = false;
        })
        .finally(() => {
          this.todayFixedLeakCountLoading = false;
        });
    },


    //今日数量
    todayHoleBasicIpCount() {
      todayHoleBasicIpCount(null)
        .then(response => {
          for (let i = 0; i < this.summaryList.length; i++) {
            if (this.summaryList[i].id == "5") {
              this.summaryList[i].num = response.data.todayAssetIpOnLineCount;
            } else if (this.summaryList[i].id == "6") {
              this.summaryList[i].num = response.data.todayVulnerableAssetsCount;
            } else if (this.summaryList[i].id == "7") {
              this.summaryList[i].num = response.data.todayLeakCount;
            } else if (this.summaryList[i].id == "8") {
              this.summaryList[i].num = response.data.todayFixedLeakCount;
            }
          }
        })
        .catch(error => {
        })
        .finally(() => {

        });

    },
    //资产排名
    homePageRanking() {
      this.loadingAssetAnalysis = true;
      homePageRanking(null)
        .then(response => {
          this.assetRankChartData = response.data.map;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingAssetAnalysis = false;
        });

    },
    //资产新增趋势
    homePageChartData() {
      this.loadingAdditionTrend = true;
      homePageMont(null)
        .then(response => {
          this.additionTrendChartData = response.data.map;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingAdditionTrend = false;
        });

    },
    //资产设备类型排名
    deviceTypeRank() {
      this.loadingDeviceTypeRank = true;

      homePageDeviceType(null)
        .then(response => {
          this.deviceTypeRankChartData = response.data.map
          let deviceList = [];
          this.deviceTypeRankChartData.XData.forEach(item => {
            this.deviceTypeList.forEach(deviceType => {
              if (deviceType.value == item) {
                deviceList.push(deviceType.label)
                return;
              }
            })
          })
          this.deviceTypeRankChartData.XData = deviceList;
        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingDeviceTypeRank = false;
        });

    },
    //资产按操作系统排名
    homePageOperatingSystem() {
      this.loadingOperatingSystemRank = true;
      homePageOperatingSystem(null)
        .then(response => {
          this.operatingSystemRankChartData = response.data.map
          let deviceList = [];
          this.operatingSystemRankChartData.XData.forEach(item => {
            this.systemTypeList.forEach(deviceType => {
              if (deviceType.value == item) {
                deviceList.push(deviceType.label)

              }
            })
          })
          this.operatingSystemRankChartData.XData = deviceList;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingOperatingSystemRank = false;
        });

    },
    //服务排名
    homePageServicerank() {
      this.loadingServicerank = true;
      homePagePortService(null)
        .then(response => {
          this.servicerankChartData = response.data.map

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingServicerank = false;
        });

    },
    //漏洞数量排名 5 IP
    homePageIPRate() {
      this.loadingHoleNumberRankTop = true;
      homePageAssetIpRate(null)
        .then(response => {
          for (let i = 0; i < this.IPList.length; i++) {
            this.IPList[i].ip = response.data[i].asset_ip;
            this.IPList[i].num = response.data[i].assetIpCount;
          }


        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingHoleNumberRankTop = false;
        });

    },
    //已修复漏洞分析
    homePageRepairRate() {
      this.loadingFixedHoleAnalysis = true;
      homePageRepairRate(null)
        .then(response => {
          this.analysisTotal[0].num = response.data.fixedLeakCount;
          this.analysisTotal[1].num = response.data.repair_rate;
        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingFixedHoleAnalysis = false;
        });

    },
    //已修复漏洞级别
    homePageVulnerabilityRate() {
      this.loadingChart = true;
      homePageVulnerabilityRate(null)
        .then(response => {
          let holeTypeDistributeList = [];
          response.data.forEach(datahole => {
            this.vulnerabilityList.forEach(deviceType => {
              if (deviceType.value == datahole.hole_level) {
                let vulnerabilityMap = {};
                vulnerabilityMap.name = deviceType.label;
                vulnerabilityMap.value = datahole.vulnerability_rate;
                holeTypeDistributeList.push(vulnerabilityMap)

              }
            })

          })

          console.log("1111")
          this.holeTypeDistributeChartData.data = holeTypeDistributeList;
          console.log(this.holeTypeDistributeChartData.data)


        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingChart = false;
        });

    },
    //已修复漏洞排行前6
    homePageRepairLimit() {
      this.loadingFixedLoopholeRank = true;
      homePageRepairLimit(null)
        .then(response => {

          this.fixedLoopholeRankChartData = response.data.map;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingFixedLoopholeRank = false;
        });

    },
    //已修复部门修复率
    homePageRepairDepartment() {
      this.loadingRepairRate = true;
      homePageRepairDepartment(null)
        .then(response => {
          this.repairRateChartData = response.data.map;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingRepairRate = false;

        });

    },
    //已修复漏洞变化趋势
    homePageRepairMont() {
      this.loadingHoleChangeTrend = true;
      homePageRepairMont(null)
        .then(response => {
          this.holeChangeTrendChartData = response.data.map;
        })
        .catch(error => {
        })
        .finally(() => {

          this.loadingHoleChangeTrend = false;
        });

    },


    //未修复漏洞分析
    homePageNotRepairRate() {
      this.loadingUnFixedHoleAnalysis = true;
      homePageNotRepairRate(null)
        .then(response => {
          this.analysisTotal_unFix[0].num = response.data.fixedLeakCount;
          this.analysisTotal_unFix[1].num = response.data.repair_rate;
        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingUnFixedHoleAnalysis = false;
        });

    },
    //未修复漏洞级别
    homePageNotVulnerabilityRate() {
      this.loadingUnFixHoleTypeDistribute = true;
      homePageNotVulnerabilityRate(null)
        .then(response => {
          let holeTypeDistributeList = [];
          response.data.forEach(datahole => {
            this.vulnerabilityList.forEach(deviceType => {
              if (deviceType.value == datahole.hole_level) {
                let vulnerabilityMap = {};
                vulnerabilityMap.name = deviceType.label;
                vulnerabilityMap.value = datahole.vulnerability_rate;
                holeTypeDistributeList.push(vulnerabilityMap)

              }
            })

          })

          this.unFixHoleTypeDistributeChartData.data = holeTypeDistributeList;


        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingUnFixHoleTypeDistribute = false;

        });

    },
    //未修复漏洞排行前6
    homePageNotRepairLimit() {
      this.loadingUnFixedLoopholeRank = true;
      homePageNotRepairLimit(null)
        .then(response => {

          this.unFixedLoopholeRankChartData = response.data.map;

        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingUnFixedLoopholeRank = false;

        });

    },
    //未修复漏洞变化趋势
    homePageNotRepairMont() {
      this.loadingUnFixHoleChangeTrend = true;
      homePageNotRepairMont(null)
        .then(response => {
          this.unFixHoleChangeTrendChartData = response.data.map;
        })
        .catch(error => {
        })
        .finally(() => {
          this.loadingUnFixHoleChangeTrend = false;

        });

    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
