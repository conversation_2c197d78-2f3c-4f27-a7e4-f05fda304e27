package com.guozw.security.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "workorder_warn_user")
public class WorkorderWarnUser extends BaseEntity<WorkorderWarnUser> implements Serializable {

  private static final long serialVersionUID = 5279773401033844940L;
  @TableId
  private String workorder_warn_user_id;

  /**
   * workorder_warn表主键
   */
  private String workorder_warn_id;
  /**
   * 用户ID
   */
  private String user_id;
  /**
   * 是否已读  0-未读  1-已读
   */
  private String is_read;
  /**
   * 阅读时间
   */
  private Date readtime;
}
