package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "nessus_hole_info")
public class NessusHoleInfo  extends BaseEntity<NessusHoleInfo> implements Serializable {


    private static final long serialVersionUID = -802087557850418087L;


    private String id;
    /**
     * 名称
     */
    private String nessus_name;
    /**
     * 漏洞说明
     */
    private String nessus_remark;
    /**
     * 修复建议
     */
    private String nessus_repair;
    /**
     * 参考信息
     */
    private String nessus_referenceinfo;
}
