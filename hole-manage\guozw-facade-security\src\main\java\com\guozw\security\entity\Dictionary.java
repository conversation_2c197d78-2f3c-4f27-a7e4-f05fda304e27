package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 字典表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "dictionary")
public class Dictionary extends Model<Dictionary> implements Serializable {


    private static final long serialVersionUID = -3539905947558243343L;
    /**
     * 字典编码
     */
    @TableId
    private String dictionarycode;
    /**
     * 字典类型编码
     */
    private String dictionarytypecode;
    /**
     * 字典名称
     */
    private String dictionaryname;
    /**
     * 字典值
     */
    private String dictionaryvalue;
    /**
     * 字典描述
     */
    private String dictionarynote;
    /**
     * 字典排序
     */
    private Integer ordernumber;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
