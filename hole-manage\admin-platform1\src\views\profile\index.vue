<template>
    <div class="app-container">
        <div >
            <el-card>
                        <el-tabs v-model="activeTab">
                           
                            <el-tab-pane label="修改密码" name="modifyPassword">
                                <modify-password  />
                            </el-tab-pane>
                        </el-tabs>
                    </el-card>
        </div>
    </div>
</template>

<script>



import ModifyPassword from "./components/ModifyPassword";
export default {
    name: "Profile",
    components: { ModifyPassword },
   
	data() {
		return {
			activeTab: 'modifyPassword',
		}
	}
};
</script>
