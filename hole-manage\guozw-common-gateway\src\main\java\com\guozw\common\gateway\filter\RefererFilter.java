package com.guozw.common.gateway.filter;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.ErrorCodeEnum;
import com.guozw.common.gateway.config.CustomPropertiesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
/**
 * 验证referer处理CSRF跨站伪造
 */
@Slf4j
//@Component
public class RefererFilter implements GlobalFilter, Ordered {
    @Autowired
    private CustomPropertiesConfig customPropertiesConfig;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpRequest request = exchange.getRequest();
        HttpHeaders headers = request.getHeaders();
        String headerReferer = headers.getFirst(HttpHeaders.REFERER);
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        log.info("function=RefererFilter,headerReferer = 【{}】", headerReferer);
        boolean contain = false;
        String[] referers = customPropertiesConfig.getReferer().split(",");
        if (StrUtil.isBlank(headerReferer) || !ArrayUtil.contains(referers, headerReferer)) {
            log.error("referer请求头无效,请求路径为: 【{}】", request.getPath());
            response.setStatusCode(HttpStatus.PRECONDITION_FAILED);
            String body = JSONUtil.toJsonStr(BaseResult.of(ErrorCodeEnum.REFERER_INVALID).setStatus(false));
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(CharsetUtil.CHARSET_UTF_8));
            return response.writeWith(Mono.just(buffer));
        }
        return chain.filter(exchange);

    }

    @Override
    public int getOrder() {
        return 0;
    }

}
