<template>
  <div>
    <vxe-modal
      :height="myModalHeight"
      width="30%"
      position="centers"
      resize
      :title="dynamicData.modalTitle"
      :loading="loading"
      v-model="modalShow"
      @close="handleModalClose()"
      showFooter>
      <vxe-form
        ref="myForm"
        align="center"
        title-width="120"
        title-align="right"
        :data="myFormData"
        :rules="rules"
        prevent-submit
        span="20"
      >
        <vxe-form-item :title="dynamicData.selectLabel" field="plan_back_status">
          <template v-slot="scope">
            <vxe-select
              clearable
              :placeholder="dynamicData.selectPlaceholder"
              readonly
              transfer
              v-model="myFormData.plan_back_status"
              :options="dynamicData.nynamicEnum"
              @change="handleSelectChange"
            >
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="要求完成日期" field="test_date" v-if="dynamicData.isPlan">
          <template v-slot="scope">
            <vxe-input v-model="myFormData.test_date" clearable placeholder="日期选择" type="date"
                       @change="handleDateChange"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="特别说明" field="test_remark">
          <template v-slot="scope">
            <vxe-input v-model="myFormData.test_remark" placeholder="特别说明" @change="handleRemarkChange"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
          <template v-slot="scope">
            <el-upload
              class="upload-demo"
              ref="upload"
              :action="''"
              :on-preview="(file, fileList) => handlePreview(file, 1)"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 1)"
              :on-change="(file, fileList) => handleChange(file, fileList, 1)"
              :file-list="planBackFileList"
              :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip" style="color:red">上传文件不超过5M</div>
            </el-upload>

          </template>

        </vxe-form-item>

        <vxe-form-item>
          <el-divider content-position="left">通知方式</el-divider>
        </vxe-form-item>
        <vxe-form-item align="left" title="系统通知">
          <vxe-checkbox v-model="systemCheckBox" @change="(value)=>checkBoxChange(value,1)"></vxe-checkbox>
        </vxe-form-item>

        <vxe-form-item title="系统用户" field="workorderWarnVO.userIdList" v-if="systemCheckBox">
          <template v-slot="scope">

            <el-select v-model="myFormData.workorderWarnVO.userIdList" filterable placeholder="请选择系统用户"
                       style="width: 100%" :multiple="true">
              <el-option
                v-for="item in userEnum"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="消息标题" field="userIdList" v-if="systemCheckBox">
          <template v-slot="scope">
            <vxe-input v-model="myFormData.workorderWarnVO.workorder_warn_name"
                       placeholder="消息标题"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="消息内容" field="userIdList" v-if="systemCheckBox">
          <template v-slot="scope">
            <vxe-input v-model="myFormData.workorderWarnVO.workorder_warn_content"
                       placeholder="消息内容"></vxe-input>
          </template>
        </vxe-form-item>

        <vxe-form-item align="left" title="邮箱通知">
          <vxe-checkbox v-model="emailCheckBox" @change="(value)=>checkBoxChange(value,2)"></vxe-checkbox>
        </vxe-form-item>
        <vxe-form-item>
          <send-email-form ref="sendEmailForm" :show="emailCheckBox" :user="userList"
                           :email-template="emailTemplate"></send-email-form>
        </vxe-form-item>

      </vxe-form>
      <template v-slot:footer>
        <el-button type="" @click="handleModalClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm"
        >确定
        </el-button
        >
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import {listDictionarys} from "@/api/security/dictionary";
import {saveHolePlanBackAndSendEmail} from "@/api/holemanage/asset"
import SendEmailForm from "@/views/holemanage/hole/ip/components/sendEmailForm";

export default {
  name: "holePlanBackModal",
  components: {SendEmailForm},
  data() {
    return {
      myFormData: {
        workorderWarnVO: {
          workorder_warn_name: '',
          workorder_warn_content: '',
          userIdList: []
        },
        test_remark: '',
        plan_back_status: ''
      },
      rules: {
        'workorderWarnVO.userIdList': [{required: true, message: "必填字段", trigger: 'change'}],

        plan_back_status: [{required: true, message: "必填字段", trigger: 'change'}]
      },
      loading: false,
      dynamicEnum: [],
      planBackFileList: [],
      systemCheckBox: false,
      emailCheckBox: false,
      holeBasicIpList: [],
      myModalHeight: '99%',
      emailNotice: false,
      systemNotice: false
    }
  },
  props: {
    modalShow: {
      type: Boolean,
      default: false
    },
    planBackType: {
      type: String,
      require: true
    },
    //修复字典
    repairEnum: {
      type: Array,
      require: true
    },
    //验证字典
    testEnum: {
      type: Array,
      require: true
    },
    //复测字典
    retestEnum: {
      type: Array,
      require: true
    },
    //关闭字典
    closeEnum: {
      type: Array,
      require: true
    },
    userList: {
      type: Array,
      require: true
    },
    emailTemplate: {
      type: Array,
      require: true
    },
    assetType: {
      type: String,
      require: true
    },
    holeBasicInfo: {
      type: Object,
    },
    isHoleIp: {
      type: Boolean,
      default: false
    },
    isHoleWebsite: {
      type: Boolean,
      default: false
    }

  },
  created() {

  },
  methods: {

    handleModalClose() {
      this.$refs.myForm.clearValidate();
      this.$refs.myForm.reset();
      this.$refs.sendEmailForm.clearValidate();
      this.$refs.sendEmailForm.reset();
      this.systemCheckBox = false;
      this.emailCheckBox = false;
      this.myFormData.workorderWarnVO.userIdList = [];
      this.$emit('close');
    },
    handleRemove(file, fileList, value) {
      console.log(file, fileList);
    },
    handlePreview(file, value) {
      console.log(file);
    },
    handleChange(file, fileList, value) {
      console.log("onChange", file, fileList);
      if (this.checkFile(file)) {
        // 移除校验失败的文件
        this.$refs.upload.uploadFiles.splice(
          this.$refs.upload.uploadFiles
            .length - 1,
          1
        );
        console.log("onChange", fileList);
        fileList.push(file);
        if (value === 1) {
          this.planBackFileList = fileList;
        } else {

        }
        return;
      }
    },
    /* 文件校验 */
    checkFile(file) {
      // 判断文件大小是否符合要求
      if (file.size / 1024 / 1024 > 5) {
        this.$XModal.message({
          message: "单个上传文件大小不能超过 5 M",
          status: "error"
        });
        return false;
      }

      return true;
    },
    checkBoxChange(checked, value) {
      if (checked.checked) {
        this.$set(this.rules, 'workorderWarnVO.userIdList', [{required: true, message: "必填字段", trigger: 'change'}]);
      } else {
        delete this.rules['workorderWarnVO.userIdList'];
      }

      console.log(checked);
      /*if (this.systemCheckBox && this.emailCheckBox) {
        this.myModalHeight = '70%';
      } else if (this.systemCheckBox) {
        this.myModalHeight = '55%';
      } else if (this.emailCheckBox) {
        this.myModalHeight = '65%';
      } else {
        this.myModalHeight = '50%';
      }*/
      if (checked.checked) {
        this.handleSelectChange({value:this.myFormData.plan_back_status});
      } else {
        console.log('dddd')
        this.myFormData.workorderWarnVO.workorder_warn_name = '';
        this.myFormData.workorderWarnVO.workorder_warn_content = '';
      }


    },
    //提交表单
    submitForm() {
      this.loading = true;
      this.$refs.myForm
        .validate()
        .then(async () => {

          let formData = new FormData();
          let myFormData = this.myFormData;
          console.log(myFormData);
          let holeBasicIpList = [];
          let assetIpList = [];
          console.log('holeBasicIpList', this.holeBasicIpList);
          if (this.holeBasicIpList && this.holeBasicIpList.length > 0) {
            if (this.assetType === '1') {
              for (let el of this.holeBasicIpList) {
                holeBasicIpList.push(el.hole_basic_ip_id);
                assetIpList.push(el.asset_ip_id);
              }
              //漏洞id
              myFormData.holeBasicIpList = holeBasicIpList;
              //漏洞ip
              myFormData.assetIpList = assetIpList;
            } else {
              for (let el of this.holeBasicIpList) {
                holeBasicIpList.push(el.hole_basic_website_id);
                assetIpList.push(el.asset_ipv4);
              }
              //漏洞id
              myFormData.holeBasicIpList = holeBasicIpList;
              //漏洞ip
              myFormData.assetIpList = assetIpList;
            }
          }
          if (this.emailCheckBox) {
            let emailVO = await this.$refs.sendEmailForm.getDate();
            myFormData.holeEmailVO = emailVO.vo;
            if (emailVO.file) {
              formData.append('emailFile', emailVO.file);
            }
          }
          if (!this.systemCheckBox) {
            this.myFormData.workorderWarnVO.workorder_warn_name = '';
            this.myFormData.workorderWarnVO.workorder_warn_content = '';
            this.myFormData.workorderWarnVO.userIdList = [];
            this.myFormData.workorderWarnVO.params = {};
          } else {
            if (this.isHoleIp) {
              let url = {
                path: '/holemanage/hole/ip/ipDetail',
                /*query: {
                  asset_ip_id: this.holeBasicInfo.asset_ip_id
                }*/
              }
              this.myFormData.workorderWarnVO.workorder_warn_url = JSON.stringify(url);
            } else if (this.isHoleWebsite) {
             let url = {
                path: '/holemanage/hole/website/ipDetail',
                /* query: {
                   asset_website_id: this.holeBasicInfo.asset_website_id
                 }*/
              }
              this.myFormData.workorderWarnVO.workorder_warn_url = JSON.stringify(url);
            }
          }

          console.log(myFormData);
          this.getFormDate(myFormData, formData, null);

          if (this.planBackFileList && this.planBackFileList.length > 0) {
            formData.append('planBackFile', this.planBackFileList[0].raw);
          }
          formData.append("asset_type", this.assetType);


          console.log('formData', myFormData);
          saveHolePlanBackAndSendEmail(formData).then(res => {
            this.$XModal.message({
              message: "操作成功",
              status: "success"
            });
            this.handleModalClose()
            this.$emit("refreshTable");

          }).finally(e => {
            this.loading = false;
          })
        })
        .catch(err => {
          this.loading = false;
          console.log(err);
        });

    },
    getFormDate(object, formData, prefix) {
      Object.keys(object).forEach(key => {
        const value = object[key]
        if (value == null || value == undefined) {
          return
        }
        if (Array.isArray(value)) {
          if (value.length == 0) {
            return;
          }
          console.log('giao');
          console.log(value)
          value.forEach((subValue, i) => {
              if (prefix) {
                formData.append(prefix + '.' + key + `[${i}]`, subValue)
              } else {
                formData.append(key + `[${i}]`, subValue)
              }
            }
          )
        } else if (Object.prototype.toString.call(object[key]) === '[object Object]') {
          if (Object.keys(value).length == 0) {
            return;
          }
          if (prefix) {
            this.getFormDate(object[key], formData, prefix + '.' + key);
          } else {
            this.getFormDate(object[key], formData, key)
          }
        } else {
          if (value == '') {
            return;
          }
          if (prefix) {
            formData.append(prefix + '.' + key, object[key])
          } else {
            formData.append(key, object[key])
          }
        }
      })
    },
    //处理改变日期的时候改变消息通知的值
    handleDateChange(value) {
      console.log('handleDateChange', value);
      if (this.systemCheckBox) {
        if (this.planBackType === '0' || this.planBackType === '1' || this.planBackType === '2') {
          console.log('giao')
          console.log(this.myFormData.workorderWarnVO)
          this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name + (value.value != '' ? ',要求完成时间' + value.value + '完成' : '');
        }
      }
    },
    //处理改变状态的时候改变消息通知的值
    handleSelectChange(value) {
      console.log('handleSelectChange', value);
      console.log('handleSelectChange', this.systemCheckBox);
      console.log('handleSelectChange', this.planBackType);
      if (this.systemCheckBox) {
        if (value == ''){
          this.myFormData.workorderWarnVO.workorder_warn_name = '';
          this.myFormData.workorderWarnVO.workorder_warn_content = '';
          return;
        }
        //处置修复
        if (this.planBackType === '0') {
          if (value.value === '1') {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞修复已开启';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞修复已关闭';
          }
          if (this.myFormData.test_date) {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name + ',要求完成时间' + this.myFormData.test_date + '完成';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name;
          }
          console.log(this.myFormData.workorderWarnVO)

        }
        //处置验证
        else if (this.planBackType === '1') {
          if (value.value === '1') {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞验证已开启';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞验证已关闭';
          }
          if (this.myFormData.test_date) {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name + ',要求完成时间' + this.myFormData.test_date + '完成';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name;
          }

        }
        //处置复测
        else if (this.planBackType === '2') {
          if (value.value === '1') {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞复测已开启';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞复测已关闭';
          }
          if (this.myFormData.test_date) {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name + ',要求完成时间' + this.myFormData.test_date + '完成';
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_content = this.myFormData.workorderWarnVO.workorder_warn_name;
          }

        }//回执修复
        else if (this.planBackType === '3') {
          for (let repairEnumElement of this.repairEnum) {
            if (this.myFormData.plan_back_status === repairEnumElement.value) {
              console.log('giaogiaogiao')
              this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞修复,' + repairEnumElement.label;
              this.myFormData.workorderWarnVO.workorder_warn_content = '';
              break;
            }
          }
        }
        //回执验证
        else if (this.planBackType === '4') {
          for (let testEnumElement of this.testEnum) {
            if (this.myFormData.plan_back_status === testEnumElement.value) {
              this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞验证,' + testEnumElement.label;
              this.myFormData.workorderWarnVO.workorder_warn_content = '';
              break;
            }
          }
        }
        //回执复测
        else if (this.planBackType === '5') {
          for (let retestEnumElement of this.retestEnum) {
            if (this.myFormData.plan_back_status === retestEnumElement.value) {
              this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞复测,' + retestEnumElement.label;
              this.myFormData.workorderWarnVO.workorder_warn_content = '';
              break;
            }
          }
        }
        //回执关闭
        else if (this.planBackType === '6') {
          for (let closeEnumElement of this.closeEnum) {
            if (this.myFormData.plan_back_status === closeEnumElement.value) {
              this.myFormData.workorderWarnVO.workorder_warn_name = '漏洞关闭,' + closeEnumElement.label;
              this.myFormData.workorderWarnVO.workorder_warn_content = '';
              break;
            }
          }
        }
        //回执关闭
        else if (this.planBackType === '7') {
          if (value.value === '1') {
            this.myFormData.workorderWarnVO.workorder_warn_name = '部分漏洞已加入白名单';
            this.myFormData.workorderWarnVO.workorder_warn_content = "部分漏洞已加入白名单";
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_name = '部分漏洞已移出白名单';
            this.myFormData.workorderWarnVO.workorder_warn_content = "部分漏洞已移出白名单";
          }
        }
        //回执关闭
        else if (this.planBackType === '8') {
          if (value.value === '1') {
            this.myFormData.workorderWarnVO.workorder_warn_name = '部分漏洞已加入误报';
            this.myFormData.workorderWarnVO.workorder_warn_content = "部分漏洞已加入误报";
          } else {
            this.myFormData.workorderWarnVO.workorder_warn_name = '部分漏洞已移出误报';
            this.myFormData.workorderWarnVO.workorder_warn_content = "部分漏洞已移出误报";
          }
        }
      }
    },
    //处理特别说明修改时改变消息的值
    handleRemarkChange(value) {
      console.log('handleRemarkChange', value);
      if (this.systemCheckBox) {
        if (this.planBackType === '3' || this.planBackType === '4' || this.planBackType === '5' || this.planBackType === '6') {
          this.myFormData.workorderWarnVO.workorder_warn_content = value.value;
        }
      }

    }
  },
  computed: {
    //根据planBackType的值改变的值
    dynamicData() {
      let data = {
        nynamicEnum: [{label: '开启', value: '1'}, {label: '关闭', value: '0'}],
        isPlan: false
      };
      this.myFormData.plan_back_type = this.planBackType;
      //处置修复
      if (this.planBackType === '0') {
        data.modalTitle = '处置计划-修复';
        data.selectLabel = '开启修复';
        data.selectPlaceholder = '请选择开启修复状态';
        data.isPlan = true;
      }
      //处置验证
      else if (this.planBackType === '1') {
        data.modalTitle = '处置计划-验证';
        data.selectLabel = '开启验证';
        data.selectPlaceholder = '请选择开启验证状态';
        data.isPlan = true;
      }
      //处置复测
      else if (this.planBackType === '2') {
        data.modalTitle = '处置计划-复测';
        data.selectLabel = '开启复测';
        data.selectPlaceholder = '请选择开启复测状态';
        data.isPlan = true;
      }
      //回执修复
      else if (this.planBackType === '3') {
        data.modalTitle = '处置反馈-修复';
        data.selectLabel = '修复状态';
        data.selectPlaceholder = '请选择修复状态'
        let ary = Array.prototype.slice.call(this.repairEnum);
        ary.shift();
        ary.shift();
        data.nynamicEnum = ary;
      }
      //回执验证
      else if (this.planBackType === '4') {
        data.modalTitle = '处置反馈-验证';
        data.selectLabel = '验证状态';
        data.selectPlaceholder = '请选择验证状态'
        let ary = Array.prototype.slice.call(this.testEnum);

        ary.shift();
        data.nynamicEnum = ary;
      }
      //回执复测
      else if (this.planBackType === '5') {
        data.modalTitle = '处置反馈-复测';
        data.selectLabel = '复测状态';
        data.selectPlaceholder = '请选择复测状态'
        let ary = Array.prototype.slice.call(this.retestEnum);

        ary.shift();
        data.nynamicEnum = ary;
      }
      //回执关闭
      else if (this.planBackType === '6') {
        data.modalTitle = '处置反馈-关闭';
        data.selectLabel = '关闭状态';
        data.selectPlaceholder = '请选择关闭状态'
        let ary = Array.prototype.slice.call(this.closeEnum);
        ary.push({label:'移除关闭状态', value: '2'})
        data.nynamicEnum = ary;
      }
      //白名单
      else if (this.planBackType === '7') {
        data.nynamicEnum = [{label: '是', value: '1'}, {label: '否', value: '0'}];
        data.modalTitle = '白名单';
        data.selectLabel = '白名单状态';
        data.selectPlaceholder = '请选择白名单状态'
      }
      //误报
      else if (this.planBackType === '8') {
        data.nynamicEnum = [{label: '是', value: '1'}, {label: '否', value: '0'}];
        data.modalTitle = '误报';
        data.selectLabel = '误报状态';
        data.selectPlaceholder = '请选择误报状态'
      }
      return data;
    },
    userEnum() {
      let userEnum = [];
      for (let userElement of this.userList) {
        userEnum.push({label: userElement.nickname, value: userElement.usercode})
      }
      return userEnum;
    },

  }
}
</script>

<style scoped>

</style>
