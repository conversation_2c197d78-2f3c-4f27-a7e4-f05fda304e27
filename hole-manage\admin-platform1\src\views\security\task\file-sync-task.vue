<template>
    <el-container>
        <el-main>
            <!-- region 表格工具栏 -->
            <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
                <template v-slot:buttons>
                    <el-button-group> </el-button-group>
                </template>
            </vxe-toolbar>
            <!-- endregion -->
            <!-- region 角色表格 -->
            <vxe-table
                id="myTable"
                ref="myTable"
                v-loading="loading"
                element-loading-text="拼命加载中"
                resizable
                :custom-config="{ storage: true }"
                :data="dataList"
                :checkbox-config="{ trigger: 'row' }"
                :edit-config="{ trigger: 'dblclick', mode: 'cell' }"
            >
                >
                <vxe-table-column
                    title="序号"
                    type="seq"
                    width="60"
                    fixed="left"
                ></vxe-table-column>
                <vxe-table-column
                    field="task_id"
                    title="任务编号"
                ></vxe-table-column>
                <vxe-table-column
                    field="task_name"
                    title="任务名称"
                ></vxe-table-column>
                <vxe-table-column
                    field="task_note"
                    title="任务描述"
                ></vxe-table-column>
                <vxe-table-column field="task_cron" title="CRON表达式">
                </vxe-table-column>
                <vxe-table-column field="task_frequency" title="任务频率">
                </vxe-table-column>
                <vxe-table-column
                    field="task_clazz"
                    title="任务执行类"
                    :visible="false"
                ></vxe-table-column>
                <vxe-table-column field="task_status" title="任务状态">
                </vxe-table-column>

                <vxe-table-column field="" title="操作">
                    <template v-slot="{ row }">
                        <el-button
                            size="mini"
                            type="primary"
                            :disabled="row.task_status == '启动'"
                            @click="handleStart(row)"
                            :loading="row.loading"
                            >启动</el-button
                        >
                        <el-button
                            size="mini"
                            type="danger"
                            :disabled="row.task_status != '启动'"
                            @click="handleStop(row)"
                            :loading="row.loading3"
                            >停止</el-button
                        >
                    </template>
                </vxe-table-column>
                <vxe-table-column field="task_result" title="最近一次任务执行结果">
                </vxe-table-column>
            </vxe-table>

            <!-- endregion -->
        </el-main>
    </el-container>
</template>

<script>
import { getFileEncTaskList, startFileEncTask, stopTask } from "@/api/security/task.js";

export default {
    name: "file-sync-task",
    data() {
        return {
            loading: true,
            dataList: []
        };
    },
    created() {
        this.loadTable();
    },
    methods: {
        loadTable() {
            this.loading = true;
            getFileEncTaskList()
                .then(response => {
                    this.dataList = response.data;
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        handleStart(row) {
            console.log("启动");
            console.log(row);
            this.$set(row, "loading", true);
            startFileEncTask({ task_id: row.task_id })
                .then(res => {
                    if (res.data) {
                        this.$XModal.message({
                            message: "启动成功",
                            status: "success"
                        });

                        this.loadTable();
                    }
                })
                .finally(() => {
                    this.$set(row, "loading", false);
                });
        },
        handleStop(row) {
            console.log("停止");
            console.log(row);
            this.$set(row, "loading3", true);
            stopTask({ task_id: row.task_id  })
                .then(res => {
                    if (res.data) {
                        this.$XModal.message({
                            message: "停止成功",
                            status: "success"
                        });

                        this.loadTable();
                    }
                })
                .finally(() => {
                    this.$set(row, "loading3", false);
                });
        }
    }
};
</script>

<style scoped></style>
