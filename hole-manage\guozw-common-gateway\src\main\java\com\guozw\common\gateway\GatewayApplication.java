package com.guozw.common.gateway;

import com.guozw.common.gateway.handler.CaptchaImageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

/**
 * 其中@SpringBootApplication申明让spring boot自动给程序进行必要的配置，
 * 等价于以默认属性使用@Configuration，@EnableAutoConfiguration和@ComponentScan
 * MapperScan({"com.xxx.demo1","com.xxx.demo2"}) 扫描多个包
 * EnableTransactionManagement 开启事务支持
 * EnableCaching开启ehcache缓存
 * ServletComponentScan使Servlet、Filter、Listener 可以直接通过 @WebServlet、@WebFilter、@WebListener 注解自动注册，无需其他代码。
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.guozw.common.gateway", "cn.hutool.extra.spring"})
@EnableDiscoveryClient
@Slf4j
public class GatewayApplication {

	public static void main(String[] args) {
		SpringApplication.run(GatewayApplication.class, args);
		log.info("---------------------------【启动成功】--------------------------");
	}

	@Bean
	public RouterFunction<ServerResponse> routeFunction(CaptchaImageHandler captchaImageHandler){
		return RouterFunctions
				.route(RequestPredicates.GET("/gateway/kaptcha")
						.and(RequestPredicates.accept(MediaType.TEXT_PLAIN)), captchaImageHandler::handle) ;
	}
}
