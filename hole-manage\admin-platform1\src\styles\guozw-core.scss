.z-index-9999 {
    z-index: 9999 !important;
}

// #region 自定义滚动条样式
/*滚动条整体部分*/
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
    background-color: #ffffff;
}

/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
    background-color: #bfbfbf;
    border-radius: 5px;
    border: 1px solid #f1f1f1;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

::-webkit-scrollbar-thumb:active {
    background-color: #787878;
}

/*边角，即两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
    background-color: #ffffff;
}

// #endregion

// #region 修改element-ui的默认样式

// 修改input框的内左边距
input.el-input__inner {
    padding-left: 5px;
}

// 修改右侧被激活tab页标题的背景色
.el-scrollbar__view .tags-view-item.active {
    background-color: #009688 !important;
}

// 按钮背景色
.el-button--primary {
    background-color: #009688 !important;
    // 禁用状态的按钮颜色和透明度

    &:hover {
        opacity: 0.6;
    }

    // 按钮中的文字颜色
    span {
        color: white;
    }
}

.el-button--primary.is-disabled {
    background-color: #009688 !important;
    opacity: 0.6 !important;
    border-color: #009688;
}

// 按钮中的图标颜色
.span-button--primary i {
    color: white !important;
}

// 修改el-tag的颜色
.el-tag.el-tag--dark.el-tag--primary {
    background-color: #009688;
    border-color: #009688;
}

.el-aside,
.el-main {
    margin: 0;
    padding: 0 5px;
    // padding: 0 10px 0 10px !important;
}

// el-tree节点选中背景色
.el-tree-node:focus > .el-tree-node__content {
    background-color: #009688 !important;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #009688 !important;
    color: white;
}

// 树超出宽度出现横向滚动条
.el-tree {
    min-width: 100%;
    display: inline-block !important;
}

// 修改当前激活的菜单的文字颜色
.el-menu-item.is-active {
    // color: #009688 !important;
    color: $subMenuActiveText;
}

// 修改进度条的背景色
.el-progress-bar__inner {
    background-color: #009688 !important;
}

// #endregion

// #region 修改vxe-table的默认样式

// 修改按钮背景色
.vxe-button.type--button.theme--primary {
    background-color: #009688 !important;
    border-color: #009688 !important;
    color: #fff !important;
}

// 修改开关背景色
.vxe-switch.is--on .vxe-switch--button {
    background-color: #009688 !important;
}

// 修改分页组件被激活页码的背景色
.vxe-pager--btn-wrapper .vxe-pager--num-btn.is--active {
    background-color: #009688 !important;
}

// #endregion
