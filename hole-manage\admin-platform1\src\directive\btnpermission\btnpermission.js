import store from "@/store";
/* 按钮权限指令 */
export default {
    inserted(el, binding, vnode) {
        const { value } = binding;
        const btnpermissions =
            (store.getters && store.getters.btnpermissions) || [];
        console.log('按钮权限', btnpermissions)
        const hasPermission = btnpermissions.includes(value);
        if (!hasPermission) {
            el.parentNode && el.parentNode.removeChild(el);
        }
    }
};
