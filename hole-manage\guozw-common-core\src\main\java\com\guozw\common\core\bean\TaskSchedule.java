package com.guozw.common.core.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 任务调度
 */
@Data
@Accessors(chain = true)
public class TaskSchedule implements Serializable {


    private static final long serialVersionUID = -5355704897620564166L;

    /**
     * 任务调度类型
     * {@link com.guozw.common.core.constant.CommonEnum.TaskScheduleType}
     */
    private String scheduleType;
    /**
     * 任务执行时间 HH:mm:ss
     */
    private String time;
    /**
     * 任务执行日期
     */
    private String date;
    /**
     * 任务执行周期某天 取值范围1-31
     */
    private String dayofmonth;
    /**
     * 任务执行周期某星期 取值范围1-7
     */
    private String week;

}
