import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
Vue.prototype.$store = store

import './icons' // icon
import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters

// #region 注册全局指令
import btnpermission from '@/directive/btnpermission'
Vue.use(btnpermission)
// #endregion

// region 引入第三方插件|自定义包
import 'font-awesome/scss/font-awesome.scss' // 使用font-awesome字体
import './plugins/vxe-table'
import { guozwSettings } from './utils/guozw-settings'
Vue.prototype.$guozwSettings = guozwSettings

import VueCron from 'vue-cron'
Vue.use(VueCron)
// endregion



/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (process.env.NODE_ENV === 'production') {
  // const { mockXHR } = require('../mock')
  // mockXHR()
}

Vue.use(Element, {
  // medium|small|mini
  size: Cookies.get('size') || 'mini' // set element-ui default size
})

// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

// 重写console.log函数 取消打印输出
// console.log = function(){}

// 事件总线 主要用于兄弟组件之间通信
export const eventBus = new Vue()

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
