package com.guozw.common.gateway.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class CustomPropertiesConfig {
    @Value("${custom.referer}")
    private String referer;
    @Value("${custom.url.regist}")
    private String url_regist;
    @Value("${custom.url.login}")
    private String url_login;
    @Value("${custom.url.logout}")
    private String url_logout;
    @Value("${custom.url.pass}")
    private String url_pass;


    @Value("${custom.xss.whitelist}")
    private String xss_whitelist;
}
