package com.guozw.security.entity.holemanage.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.guozw.common.core.constant.DictEnum;
import com.guozw.security.entity.Dictionary;
import com.guozw.security.vo.holemanage.HoleBasicIpVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class HoleBasicIpAggExportExcel {

    @ExcelProperty("ip地址")
    private String asset_ip;
    @ExcelProperty("隶属关系")
    private String departmentname;
    @ExcelProperty("设备类型")
    private String device_type;

    @ExcelProperty("需关注漏洞总数")
    private int needNotice;
    @ExcelProperty("超危漏洞数")
    private int hole_level5;
    @ExcelProperty("高危漏洞数")
    private int hole_level4;
    @ExcelProperty("中危漏洞数")
    private int hole_level3;
    @ExcelProperty("低危漏洞数")
    private int hole_level2;


    public HoleBasicIpAggExportExcel(HoleBasicIpVO holeBasicIpVO, Map<String, List<Dictionary>> dictionaryMap){
        BeanUtils.copyProperties(holeBasicIpVO, this);
        //设备类型
        for (Dictionary dictionary : dictionaryMap.get(DictEnum.DEVICT_TYPE.getValue())) {
            if (dictionary.getDictionaryvalue().equals(this.getDevice_type())){
                this.setDevice_type(dictionary.getDictionaryname());
                break;
            }
        }
    }
}
