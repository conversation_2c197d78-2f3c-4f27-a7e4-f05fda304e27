package com.guozw.security.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 要传入给quzrtz定时任务的参数
 */
@Data
@Accessors(chain = true)
public class JobDataDTO implements Serializable {
    private static final long serialVersionUID = -32547476545457768L;

    /**
     * 任务id
     */
    private String task_basic_id;
    /**
     * 工具id
     */
    private String tool_basic_id;
}
