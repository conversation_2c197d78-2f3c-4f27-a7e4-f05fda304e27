package com.guozw.security.entity.config;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "config_email_template")
public class ConfigEmailTemplate extends BaseEntity<ConfigEmailTemplate> implements Serializable {
    private static final long serialVersionUID = 6177331024171367613L;
    @TableId
    private String config_email_template_id;
    /**
     * 模板名称
     */
    private String template_name;
    /**
     * 模板描述
     */
    private String template_remark;
    /**
     * 模板主题
     */
    private String template_theme;
    /**
     * 模板正文
     */
    private String template_text;
    /**
     * 创建人
     */
    private String createuserid;

    /**
     * 创建人部门id
     */
    private String create_user_department_id;

}
