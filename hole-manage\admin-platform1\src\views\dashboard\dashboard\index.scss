* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

ul li {
  list-style-type: none;
}

.visualization-container {
  padding: 40px;

  .chart-title-wrap {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #262626;

    img {
      width: 24px;
      height: 16px;
      object-fit: cover;
      margin-right: 5px;
    }
  }

  .part-wrap {
    margin-bottom: 25px;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei-Regular;
    height: 49px;
    padding: 8px 0 0 24px;
    background: url("~@/assets/visualization/partIcon.png") no-repeat left top / 100% 100% !important;
  }

  .summary-wrap {
    ul {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      column-gap: 3%;
      row-gap: 40px;

      .summary-item {
        display: flex;
        align-items: center;
        border-radius: 8px;
        height: 96px;

        .icon {
          height: 70px;
          object-fit: cover;
          // margin-top: -15px;
        }

        .info {
          display: flex;
          flex-direction: column;

          .title {
            font-size: 16px;
            font-weight: bold;
            // margin-bottom: 14px;
            color: rgba(255, 255, 255, 0.85);
          }

          .num {
            color: #fff;
            font-weight: bold;
            font-size: 28px;
          }
        }
      }

      .todayIcon {
        position: relative;
        // height: 110px;
        height: 90px;
        background: url("~@/assets/visualization/summaryPart.png") no-repeat left top / 100% 100% !important;

        .title {
          position: absolute;
          top: 25px;
          left: 40px;
          font-size: 26px;
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei-Regular;
        }

        .num {
          position: absolute;
          top: 13px;
          left: 127px;
        }
      }
    }
  }

  .assetAnalysis-wrap,
  .holeNumberRankTop-wrap,
  .fixedHoleAnalysis-wrap,
  .unFixedHoleAnalysis-wrap {
    margin: 40px 0;
  }

  // 资产分析_增加按服务排名
  .assetAnalysis-wrap {
    .chart-wrap {
      .top-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        >div {
          height: 400px;
          width: 49%;
          margin-bottom: 60px;
        }
      }

      .bottom-wrap {
        .servicerank-chart-wrap {
          height: 200px;
          // color: rgba(100, 217, 214, .7);
        }
      }
    }
  }

  // 漏洞数量排名 10
  .holeNumberRankTop-wrap {
    .topList-wrap {
      margin: 40px 0;
    }

    .topList-wrap>ul {
      display: flex;
      align-items: center;

      .item {
        width: 200px;
        width: calc((100% - 160px) / 5);
        // height: 134px;
        background: url("~@/assets/visualization/topOther.png") no-repeat left top / 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;
        padding: 10px 0;
        margin-right: 40px;

        &:last-child {
          margin-right: 0;
        }

        .ip {
          font-size: 20px;
        }

        .rank {
          font-size: 32px;
          position: relative;
          top: -5px;
        }

        .num-wrap {
          display: flex;
          align-items: flex-end;
          align-items: center;
          text-align: left;
          color: #000;
          font-size: 16px;

          .text {
            // margin: 10px 10px 0 0;
          }

          .num {
            font-size: 32px;
            color: #009688;
            margin-left: 10px;
          }
        }

        &:nth-child(1) {
          background-image: url("~@/assets/visualization/top1.png");

          .num {
            font-size: 32px;
            color: #f04545;
          }
        }

        &:nth-child(2) {
          background-image: url("~@/assets/visualization/top2.png");

          .num {
            font-size: 32px;
            color: #e79f10;
          }
        }

        &:nth-child(3) {
          background-image: url("~@/assets/visualization/top3.png");

          .num {
            font-size: 32px;
            color: #196ce7;
          }
        }
      }
    }
  }

  // 已修复漏洞分析
  .fixedHoleAnalysis-wrap {
    .content-wrap {

      .top-wrap,
      .bottom-wrap {
        display: flex;
        justify-content: space-between;
        margin-bottom: 60px;
        height: 300px;

        >div {
          width: 49%;
        }
      }

      .top-wrap {
        .top-left-wrap {
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .box {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            width: 35%;
            height: 100%;

            .item {
              display: flex;
              align-items: center;
              border-radius: 8px;
              width: 100%;
              height: 96px;

              &:last-of-type {
                margin-top: 40px;
              }

              .icon {
                height: 70px;
                object-fit: cover;
                // margin-top: -15px;
              }

              .info {
                display: flex;
                flex-direction: column;

                .title {
                  font-size: 16px;
                  font-weight: bold;
                  // margin-bottom: 14px;
                  color: rgba(255, 255, 255, 0.85);
                }

                .num {
                  color: #fff;
                  font-weight: bold;
                  font-size: 22px;
                }
              }
            }
          }

          .chart-box {
            width: 49%;
            flex: 1;
            height: 100%;
            margin-left: 30px;
            display: flex;
            flex-direction: column;
          }
        }

        .top-right-wrap {
          display: flex;
          flex-direction: column;

          .chart-box {
            flex: 1;
            height: 100%;
          }
        }
      }

      .bottom-wrap {
        height: 300px;
        display: flex;
        justify-content: space-between;

        .bottom-left-wrap,
        .bottom-right-wrap {
          width: 49%;
          display: flex;
          flex-direction: column;
          height: 100%;

          .chart-box {
            flex: 1;
            height: 100%;
          }
        }
      }
    }
  }

  // 未修复漏洞分析
  .unFixedHoleAnalysis-wrap {
    .content-wrap {
      .top-wrap {
        display: flex;
        justify-content: space-between;
        margin-bottom: 60px;
        height: 300px;

        >div {
          width: 49%;
        }
      }

      .top-wrap {
        .top-left-wrap {
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .box {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            width: 35%;
            height: 100%;

            .item {
              display: flex;
              align-items: center;
              border-radius: 8px;
              width: 100%;
              height: 96px;

              &:last-of-type {
                margin-top: 40px;
              }

              .icon {
                height: 70px;
                object-fit: cover;
                // margin-top: -15px;
              }

              .info {
                display: flex;
                flex-direction: column;

                .title {
                  font-size: 16px;
                  font-weight: bold;
                  // margin-bottom: 14px;
                  color: rgba(255, 255, 255, 0.85);
                }

                .num {
                  color: #fff;
                  font-weight: bold;
                  font-size: 22px;
                }
              }
            }
          }

          .chart-box {
            width: 49%;
            flex: 1;
            height: 100%;
            margin-left: 30px;
            display: flex;
            flex-direction: column;
          }
        }

        .top-right-wrap {
          display: flex;
          flex-direction: column;

          .chart-box {
            flex: 1;
          }
        }
      }

      .bottom-wrap {
        height: 300px;

        .chart-box {
          flex: 1;
          height: 100%;
        }
      }
    }
  }
}