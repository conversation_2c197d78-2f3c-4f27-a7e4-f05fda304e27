package com.guozw.common.gateway.config;

import cn.hutool.core.util.StrUtil;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.gateway.filter.CaptchaWebFilter;
import com.guozw.common.gateway.security.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.authentication.AuthenticationWebFilter;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

@Slf4j
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    @Autowired
    private CustomServerFormLoginAuthenticationConverter serverFormLoginAuthenticationConverter;
    @Autowired
    private CustomServerAuthenticationSuccessHandler serverAuthenticationSuccessHandler;
    @Autowired
    private CustomServerAuthenticationFailureHandler serverAuthenticationFailureHandler;
    @Autowired
    private CustomServerAuthenticationEntryPoint serverAuthenticationEntryPoint;
    @Autowired
    private CustomServerLogoutSuccessHandler serverLogoutSuccessHandler;
    @Autowired
    private CustomReactiveAuthorizationManager reactiveAuthorizationManager;
    @Autowired
    private CustomPropertiesConfig customPropertiesConfig;
    @Autowired
    private CaptchaWebFilter captchaWebFilter;

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {

        // 跨域过滤器
        http.addFilterAt(corsFilter(), SecurityWebFiltersOrder.CORS);
        http.addFilterAfter(captchaWebFilter, SecurityWebFiltersOrder.CORS);
        SecurityWebFilterChain chain = http.formLogin()
                .loginPage(customPropertiesConfig.getUrl_login())
                // 登录成功handler
                .authenticationSuccessHandler(serverAuthenticationSuccessHandler)
                // 登陆失败handler
                .authenticationFailureHandler(serverAuthenticationFailureHandler)
                // 无访问权限handler
                .authenticationEntryPoint(serverAuthenticationEntryPoint)
                .and()
                .logout().logoutUrl(customPropertiesConfig.getUrl_logout())
                // 登出成功handler
                .logoutSuccessHandler(serverLogoutSuccessHandler)
                .and()
                .csrf().disable()
                .httpBasic().disable()
                .authorizeExchange()
                // 白名单放行
                .pathMatchers(StrUtil.splitToArray(customPropertiesConfig.getUrl_pass(), CommonConstant.DEFAULT_SEPARATOR)).permitAll()
                // 访问权限控制
                .anyExchange().access(reactiveAuthorizationManager)
                .and().build();
        // 设置自定义登录参数转换器
        chain.getWebFilters()
                .filter(webFilter -> webFilter instanceof AuthenticationWebFilter)
                .subscribe(webFilter -> {
                    AuthenticationWebFilter filter = (AuthenticationWebFilter) webFilter;
                    filter.setServerAuthenticationConverter(serverFormLoginAuthenticationConverter);
                });

        return chain;
    }


    /**
     * BCrypt密码编码
     * @return
     */
    @Bean
    public BCryptPasswordEncoder bcryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }


    private WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            if (CorsUtils.isCorsRequest(request)) {
                HttpHeaders requestHeaders = request.getHeaders();
                ServerHttpResponse response = ctx.getResponse();
                HttpMethod requestMethod = requestHeaders.getAccessControlRequestMethod();
                HttpHeaders headers = response.getHeaders();
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, requestHeaders.getOrigin());
                headers.addAll(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, requestHeaders.getAccessControlRequestHeaders());
                if (requestMethod != null) {
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, requestMethod.name());
                }
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
                headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
                headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "18000L");
                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }
            return chain.filter(ctx);
        };
    }
}
