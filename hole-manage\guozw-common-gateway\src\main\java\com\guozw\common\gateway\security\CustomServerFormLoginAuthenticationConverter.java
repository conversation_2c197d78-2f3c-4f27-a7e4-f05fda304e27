package com.guozw.common.gateway.security;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.constant.RedisKeyConstant;
import com.guozw.common.core.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.authentication.ServerFormLoginAuthenticationConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 表单登录的用户名密码转换
 */
@Slf4j
@Component
public class CustomServerFormLoginAuthenticationConverter extends ServerFormLoginAuthenticationConverter {

    @Override
    public Mono<Authentication> convert(ServerWebExchange exchange) {
        log.info("表单登录的用户名密码转换");
        return exchange.getFormData()
                .map(data -> {
                    String username = data.getFirst(CommonConstant.USERNAME_PARAMETER);
                    String password = data.getFirst(CommonConstant.PASSWORD_PARAMETER);
                    String aesKey = (String) RedisUtils.get(RedisKeyConstant.AES_KEY);
                    password = SecureUtil.aes(StrUtil.bytes(aesKey)).decryptStr(password);
                    return new UsernamePasswordAuthenticationToken(username, password);
                });
    }
}
