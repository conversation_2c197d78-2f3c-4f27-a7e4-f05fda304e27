<template>
  <div style="  display: flex;">
    <Timeline :timeDataList="timeDataList" :plan-back-status-enum="planBackTypeEnum"
              :close-status-enum="closeStatusEnum" :repair-status-enum="repairStatusEnum"
              :retest-status-enum="retestStatusEnum" :test-status-enum="testStatusEnum"/>
  </div>
</template>

<script>
import Timeline from "@/views/loopholeTimeLine/components/Timeline";

export default {
  name: "lifeCycleTab",
  components: {Timeline},
  props: {
    timeDataList: {
      type: Array,
      require: true,
      default: () => []
    },
    planBackTypeEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    testStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    repairStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    retestStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
    closeStatusEnum: {
      type: Array,
      require: true,
      default: () => []
    },
  }
}
</script>

<style scoped>

</style>
