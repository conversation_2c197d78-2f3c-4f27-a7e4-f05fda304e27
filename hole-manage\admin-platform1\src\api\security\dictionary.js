import request from '@/utils/request'
import qs from 'qs'
export function listDictionarys(data) {
    return request({
        url: '/security/dictionary/listDictionarys',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function saveDictionary(data) {
    return request({
        url: '/security/dictionary/saveDictionary',
        method: 'post',
        params: data
    })
}
export function modifyDictionary(data) {
    return request({
        url: '/security/dictionary/modifyDictionary',
        method: 'post',
        params: data
    })
}
export function deleteDictionary(data) {
    return request({
        url: '/security/dictionary/deleteDictionary',
        method: 'post',
        params: data
    })
}
