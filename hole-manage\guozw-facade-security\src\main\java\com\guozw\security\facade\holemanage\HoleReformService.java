package com.guozw.security.facade.holemanage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guozw.security.entity.holemanage.HoleBasicIp;
import com.guozw.security.entity.holemanage.HoleReform;
import com.guozw.security.vo.holemanage.HoleReformVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 整改下发
 */
public interface HoleReformService extends IService<HoleReform>{

    Page<HoleReformVO> pageHoleReform(HoleReformVO vo);

    Boolean doReform(HoleReformVO vo, MultipartFile holePlanBackFile, MultipartFile emailFile);
    Boolean doReform(HoleReformVO vo, MultipartFile holePlanBackFile, MultipartFile emailFile, String username);

    Boolean deleteBatchByHoleId(List<String> holeIdList);

    /**
     * 生成整改通知单
     * @param request
     * @param response
     */
    void grenrationDownFile(HttpServletRequest request, HttpServletResponse response,List<String> holeBasicIds);


    /**
     * 系统自动下发整改
     * @param holeBasicIp
     * @param repeatModel
     * @return
     */
    Boolean systemDoReform(HoleBasicIp holeBasicIp, String repeatModel,String username);
}
