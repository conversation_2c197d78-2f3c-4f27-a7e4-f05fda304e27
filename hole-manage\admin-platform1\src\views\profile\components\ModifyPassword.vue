<template>
    <vxe-form
        ref="form"
        title-align="right"
        title-width="100"
        :data="formData"
        :rules="formRules"
    >
        <vxe-form-item title="旧密码" field="oldpassword" span="24">
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.oldpassword"
                    placeholder="请输入旧密码"
                    clearable
                    type="password"
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>

        <vxe-form-item title="新密码" field="newpassword" span="24">
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.newpassword"
                    placeholder="请输入新密码"
                    clearable
                    type="password"
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>
        <vxe-form-item title="确认新密码" field="newpasswordconfirm" span="24">
            <template v-slot="scope">
                <vxe-input
                    v-model.trim="formData.newpasswordconfirm"
                    placeholder="请再次输入新密码"
                    type="password"
                    clearable
                    @input="$refs.form.updateStatus(scope)"
                ></vxe-input>
            </template>
        </vxe-form-item>

        <vxe-form-item align="center" span="24">
            <template v-slot>
                <vxe-button type="" status="primary" @click="handleSubmit"
                    >提交修改</vxe-button
                >
                <vxe-button type="reset">重置</vxe-button>
            </template>
        </vxe-form-item>
    </vxe-form>
</template>

<script>
import { modifyPassword } from "@/api/security/user";
import { validPassword } from "@/utils/validate";
import { aesUtil } from "@/utils/aes";
export default {
    data() {
        return {
            formData: {
                oldpassword: null,
                newpassword: null,
                newpasswordconfirm: null
            },
            formRules: {
                oldpassword: [{ required: true, message: "请输入旧密码" }],
                newpassword: [
                    { required: true, message: "请输入新密码" },
                    {
                        validator: ({ itemValue }) => {
                            if (validPassword(itemValue)) {
                            } else {
                                return new Error(
                                    "密码包括至少大小写字母、数字和特殊字符，长度8-20位。"
                                );
                            }
                        }
                    }
                ],
                newpasswordconfirm: [
                    { required: true, message: "请再次输入新密码" },
                    {
                        validator: ({ itemValue }) => {
                            if (itemValue != this.formData.newpassword) {
                                return new Error("两次输入的密码不一致");
                            }
                        }
                    }
                ]
            }
        };
    },
    methods: {
        handleSubmit() {
            const that = this;
             console.log(`AES密钥【${this.$store.getters.aesKey}】`);
            this.$refs.form.validate().then(() => {

                const postData = {
                    oldpassword: aesUtil.encrypt(
                        this.formData.oldpassword,
                        this.$store.getters.aesKey
                    ),
                    newpassword: aesUtil.encrypt(
                        this.formData.newpassword,
                        this.$store.getters.aesKey
                    ),
                    usercode: this.$store.getters.userInfo.usercode
                };
                modifyPassword(postData)
                    .then(res => {
                        if (res.status) {
                            this.$XModal.message({
                                message: `密码修改成功，即将跳转到重录页。新密码为 ${
                                    this.formData.newpassword
                                } 请妥善保管。`,
                                status: "success"
                            });
                            return true;
                        } else {
                            this.$XModal.message({
                                message: res.message,
                                status: "error"
                            });
                        }
                    })
                    .then(res2 => {
                        setTimeout(function() {
                            if (res2) that.logout();
                        }, 3000);
                    });
            });
        },
        async logout() {
            await this.$store.dispatch("user/logout");
            // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
            this.$router.push(`/login?redirect=/dashboard`);
        }
    }
};
</script>
