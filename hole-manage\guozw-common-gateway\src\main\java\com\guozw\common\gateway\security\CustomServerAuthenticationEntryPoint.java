package com.guozw.common.gateway.security;

import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.util.JacksonUtils;
import com.guozw.common.gateway.exception.CustomAccessDeniedException;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 未认证处理
 */
@Slf4j
@Component
public class CustomServerAuthenticationEntryPoint implements ServerAuthenticationEntryPoint {

    @Override
    public Mono<Void> commence(ServerWebExchange serverWebExchange, AuthenticationException e) {
        log.error("认证失败【{}】", e.getMessage());
        CustomAccessDeniedException customException = (CustomAccessDeniedException) e.getCause();
        ServerHttpResponse response = serverWebExchange.getResponse();
        response.setStatusCode(HttpStatus.OK);
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String body = JacksonUtils.obj2json(new BaseResult(customException.getCode(), customException.getMessage()).setStatus(Boolean.FALSE));
        DataBuffer wrap = serverWebExchange.getResponse().bufferFactory().wrap(body.getBytes(CharsetUtil.UTF_8));
        return serverWebExchange.getResponse().writeWith(Flux.just(wrap));
    }
}
