<template>
    <div ref="rightPanel" :class="{ show: show }" class="rightPanel-container">
        <div class="rightPanel-background" />
        <div class="rightPanel">
            <VueDragResize
                axis="y"
                :w="0"
                :h="0"
                :stickSize="0"
                :isResizable="false"
                @dragstop="handleDragstop"
                parent-limitation
                :parentW="260"
                :parentH="parentH"
                :x="0"
                :y="locationY"
            >
                <div
                    @click="show = !show"
                    class="handle-button"
                    :style="{ 'background-color': theme }"
                >
                    <i :class="show ? 'el-icon-close' : 'el-icon-setting'" />
                </div>
            </VueDragResize>
            <div class="rightPanel-items">
                <slot />
            </div>
        </div>
    </div>
</template>

<script>
import { addClass, removeClass } from '@/utils'
import VueDragResize from 'vue-drag-resize';
import {
    mapGetters
} from 'vuex'
export default {
    name: 'RightPanel',
    components: {
        VueDragResize
    },
    props: {
        clickNotClose: {
            default: false,
            type: Boolean
        },
        buttonTop: {
            default: 250,
            type: Number
        }
    },
    data() {
        return {
            show: false,
        }
    },
    computed: {
        ...mapGetters(['extraHeight']),
        theme() {
            return this.$store.state.settings.theme
        },
        locationY() {
            return +this.$store.state.settings.locationY
        },
        parentH() {
            return window.innerHeight - this.extraHeight + 80
        }
    },
    watch: {
        show(value) {
            if (value && !this.clickNotClose) {
                this.addEventClick()
            }
            if (value) {
                addClass(document.body, 'showRightPanel')
            } else {
                removeClass(document.body, 'showRightPanel')
            }
        }
    },
    mounted() {
        this.insertToBody()
    },
    beforeDestroy() {
        const elx = this.$refs.rightPanel
        elx.remove()
    },
    methods: {
        addEventClick() {
            window.addEventListener('click', this.closeSidebar)
        },
        closeSidebar(evt) {
            const parent = evt.target.closest('.rightPanel')
            if (!parent) {
                this.show = false
                window.removeEventListener('click', this.closeSidebar)
            }
        },
        insertToBody() {
            const elx = this.$refs.rightPanel
            const body = document.querySelector('body')
            body.insertBefore(elx, body.firstChild)
        },
        handleDragstop({ top }) {
            localStorage.setItem('locationY', top)
        }
    }
}
</script>

<style>
.showRightPanel {
    overflow: hidden;
    position: relative;
    width: calc(100% - 15px);
}
</style>

<style lang="scss" scoped>
.rightPanel-background {
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);
    background: rgba(0, 0, 0, 0.2);
    z-index: -1;
}

.rightPanel {
    width: 100%;
    max-width: 260px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05);
    transition: all 0.25s cubic-bezier(0.7, 0.3, 0.1, 1);
    transform: translate(100%);
    background: #fff;
    z-index: 40000;
}

.show {
    transition: all 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);

    .rightPanel-background {
        z-index: 20000;
        opacity: 1;
        width: 100%;
        height: 100%;
    }

    .rightPanel {
        transform: translate(0);
    }
}

.handle-button {
    width: 48px;
    height: 48px;
    position: absolute;
    left: -48px;
    text-align: center;
    font-size: 24px;
    border-radius: 6px 0 0 6px !important;
    z-index: 0;
    pointer-events: auto;
    cursor: pointer;
    color: #fff;
    line-height: 48px;
    opacity: 0.3;
    transition: all 0.2s;
    i {
        font-size: 24px;
        line-height: 48px;
    }
    &:hover {
        opacity: 1;
    }
}
</style>
