package com.guozw.security.facade.holemanage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guozw.common.core.base.BaseResult;
import com.guozw.security.entity.holemanage.ToolBasic;
import com.guozw.security.vo.holemanage.ToolBasicVO;

import java.util.List;

/**
 * 漏扫工具基础信息
 */
public interface ToolBasicService extends IService<ToolBasic> {

    Page<ToolBasicVO> pageToolBasic(ToolBasicVO vo);
    boolean getToolStatus(ToolBasicVO vo);
    List<Boolean> getToolStatusList(ToolBasicVO vo);

    boolean saveToolBasic(ToolBasicVO vo);

    BaseResult deleteToolBasic(List<String> toolBasicIdList);
}
