package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "api_task_dispatch")
public class ApiTaskDispatch extends BaseEntity<ApiTaskDispatch> implements Serializable {

    private static final long serialVersionUID = 4721336913009818892L;

    @TableId
    private String api_task_dispatch_id;
    private String api_task_id;
    private String task_id;
    private String tool_basic_id;
    private String dispatch_in_time;
    private String dispatch_start_time;
    private String dispatch_end_time;
    private String dispatch_run_time;
    private String task_status;
    /**
     * 平台自身的任务计划调度id
     */
    private String task_plan_dispatch_id;
    /**
     * 任务进度
     */
    private String task_progress;
    /**
     * 任务执行失败原因
     */
    private String failure_reason;

    /**
     * 扫描漏洞数
     */
    private Integer hole_count;

    /**
     * 扫描资产数
     */
    private Integer asset_count;

    private Integer service_port_count;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
