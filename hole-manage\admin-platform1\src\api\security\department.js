import request from '@/utils/request'

export function listDepartments(data) {
    return request({
        url: '/security/department/listDepartments',
        method: 'post',
        params: data
    })
}
export function listDepartmentTypes(data) {
    return request({
        url: '/security/department/listDepartmentTypes',
        method: 'post',
        params: data
    })
}
export function oneDepartment(data) {
    return request({
        url: '/security/department/oneDepartment',
        method: 'post',
        params: data
    })
}
export function saveDepartment(data) {
    return request({
        url: '/security/department/saveDepartment',
        method: 'post',
        params: data
    })
}
export function saveDepartment2(data) {
    return request({
        url: '/security/department/saveDepartment2',
        method: 'post',
        params: data
    })
}
export function departmentExists(data) {
    return request({
        url: '/security/department/departmentExists',
        method: 'post',
        params: data
    })
}
export function modifyDepartment(data) {
    return request({
        url: '/security/department/modifyDepartment',
        method: 'post',
        params: data
    })
}
export function modifyDepartment2(data) {
    return request({
        url: '/security/department/modifyDepartment2',
        method: 'post',
        params: data
    })
}
export function deleteDepartment(data) {
    return request({
        url: '/security/department/deleteDepartment',
        method: 'post',
        params: data
    })
}
export function hasChildren(data) {
    return request({
        url: '/security/department/hasChildren',
        method: 'post',
        params: data
    })
}
