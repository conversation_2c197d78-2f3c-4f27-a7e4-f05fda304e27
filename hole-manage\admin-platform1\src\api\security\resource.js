import request from '@/utils/request'
import qs from 'qs'

export function listResources(data) {
    return request({
        url: '/security/resource/listResources',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function listRoleResourceRelations(data) {
    return request({
        url: '/security/resource/listRoleResourceRelations',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function listBtnPermissions(data) {
    return request({
        url: '/security/resource/listBtnPermissions',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function listRoutes(data) {
    return request({
        url: '/security/resource/listRoutes',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function listResourceTypes(data) {
    return request({
        url: '/security/resource/listResourceTypes',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function oneResource(data) {
    return request({
        url: '/security/resource/oneResource',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function saveResource(data) {
    return request({
        url: '/security/resource/saveResource',
        method: 'post',
        data: qs.stringify(data)
    })
}

export function modifyResource(data) {
    return request({
        url: '/security/resource/modifyResource',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function deleteResource(data) {
    return request({
        url: '/security/resource/deleteResource',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function enabledResource(data) {
    return request({
        url: '/security/resource/enabledResource',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function hiddenResource(data) {
    return request({
        url: '/security/resource/hiddenResource',
        method: 'post',
        data: qs.stringify(data)
    })
}
export function hasChildren(data) {
    return request({
        url: '/security/resource/hasChildren',
        method: 'post',
        data: qs.stringify(data)
    })
}
