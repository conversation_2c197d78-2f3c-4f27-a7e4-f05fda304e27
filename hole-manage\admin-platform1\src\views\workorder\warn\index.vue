<template>
  <el-container>
    <el-main>
      <el-tabs ref="tabs" v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="未读" name="0"></el-tab-pane>
        <el-tab-pane label="已读" name="1"></el-tab-pane>
      </el-tabs>
      <!-- region 查询条件表单-->
      <vxe-form ref="queryConditionForm" title-width="100" title-align="right" span="6" :data="queryParams" @submit="handleQueryConditionFormSubmit" @toggle-collapse="handleQueryConditionFormToggleCollapse">
        <vxe-form-item field="workorder_warn_from" title="发送人">
          <!-- <el-select clearable v-model="queryParams.workorder_warn_from" style="width:100%;" filterable placeholder="发送人" @change="changeWorkorder_warn_from($event)">
            <el-option v-for="item in userList" :key="item.usercode" :label="item.departmentname + '/' + item.nickname" :value="item.usercode">
            </el-option>
          </el-select> -->
          <el-select v-model="queryParams.workorder_warn_from" placeholder="发送人" filterable style="width:100%;" @change="changeWorkorder_warn_from($event)">
            <el-option v-for="item in userList" :key="item.usercode" :label="item.departmentname + '/' + item.nickname" :value="item.usercode">
              <span style="float: left">{{ item.nickname }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px;margin-left:10px;">{{ item.departmentname }}</span>
            </el-option>
          </el-select>
        </vxe-form-item>

        <!-- <vxe-form-item field="device_type" title="发送人">
                    <vxe-select
                        clearable
                        placeholder="发送人"
                        v-model="queryParams.device_type"
                        :options="deviceTypeEnum"
                    ></vxe-select>
                </vxe-form-item> -->
        <vxe-form-item field="startDate" title="发送时间开始">
          <el-date-picker v-model="queryParams.startDate" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>
        <vxe-form-item field="endDate" title="发送时间结束">
          <el-date-picker v-model="queryParams.endDate" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>

        <vxe-form-item align="center" v-if="activeName==0">
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>
        <vxe-form-item align="center" v-if="activeName==1" collapse-node>
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>
        <vxe-form-item field="startDateRead" title="阅读时间开始" v-if="activeName==1" folding>
          <el-date-picker v-model="queryParams.startDateRead" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>
        <vxe-form-item field="endDateRead" title="阅读时间结束" v-if="activeName==1" folding>
          <el-date-picker v-model="queryParams.endDateRead" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <!-- <el-button
                        type="primary"
                        icon="fa fa-plus"
                        @click="handleAdd"
                    >
                        新增</el-button
                    >
                    <el-button
                        type="primary"
                        icon="fa fa-edit"
                        @click="handleBatchEdit"
                    >
                        批量修改</el-button
                    >
                    <el-button
                        type="danger"
                        icon="fa fa-trash"
                        @click="handleBatchDelete"
                    >
                        批量删除</el-button
                    >-->
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table id="myTable" ref="myTable" v-loading="loading" element-loading-text="拼命加载中" border auto-resize resizable height="auto" show-overflow :sort-config="{ trigger: 'cell', remote: true }" @sort-change="sortChange" :custom-config="{ storage: true }" :data="page.records" :checkbox-config="{ trigger: 'row' }">
          <!-- <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column> -->
          <vxe-table-column title="序号" type="seq" width="60" fixed="left"></vxe-table-column>
          <vxe-table-column field="workorder_warn_name" title="消息名称">
            <template slot-scope="scope">
              <el-link type="primary" @click="handleView(scope.row)">{{ scope.row.workorder_warn_name }}</el-link>
              <!-- <router-link v-if="scope.row.workorder_warn_url.path" :to="scope.row.workorder_warn_url.path" @click.native="handleWarnUserStatus(scope.row)" class="link-type">
                <span style="font-weight: bold">{{ scope.row.workorder_warn_name }}</span>
              </router-link> -->
            </template>
          </vxe-table-column>
          <vxe-table-column field="workorder_warn_content" title="消息内容"></vxe-table-column>
          <vxe-table-column field="departmentname" title="发送人部门名称"></vxe-table-column>
          <vxe-table-column field="nickname" title="发送人"></vxe-table-column>
          <!-- <vxe-table-column field="workorder_warn_from" title="发送人">
            <template slot-scope="scope" v-if="scope.row.workorder_warn_from!=null">
              <span style="color: blank">
                {{scope.row.departmentname}}/{{ scope.row.nickname }}
              </span>
            </template>
          </vxe-table-column> -->
          <!-- <vxe-table-column field="asset_web_product" title="发送时间">
            <template v-slot="{ row }">
              <el-tag style="margin-left: 5px" v-for="item in row.assetSoftWebList" :key="item.asset_web_id" type="" effect="plain">
                {{ item.asset_web_product }}
              </el-tag>
            </template>
          </vxe-table-column> -->
          <vxe-table-column field="createdate" width="150px"  title="发送时间" sortable>
          </vxe-table-column>
          <vxe-table-column field="readtime" title="阅读时间" v-if="activeName==1"></vxe-table-column>
          <!-- <vxe-table-column title="附件" width="80px">
            <template slot-scope="scope">
              <el-link type="primary" v-if="scope.row.file_id!=null" @click="goDownload(scope.row)">附件下载</el-link>
            </template>
          </vxe-table-column> -->
          <vxe-table-column field="" title="操作" fixed="right" width="70px">
            <!-- <template v-slot="{ row }">
              <i class="el-icon-view" style="font-size: 18px; color: #409EFF" @click="handleView(row)"></i>
            </template> -->
            <template slot-scope="scope">
              <el-link type="primary" v-if="scope.row.workorder_warn_url !=null" @click="gowarn(scope.row)">去处理</el-link>
              <!-- <router-link v-if="scope.row.workorder_warn_url.path" :to="scope.row.workorder_warn_url.path" @click.native="handleWarnUserStatus(scope.row)" class="link-type">
                <span style="font-weight: bold">{{ scope.row.workorder_warn_name }}</span>
              </router-link> -->
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <vxe-modal ref="resourceFormModal" height="99%" width="700" position="center" resize :title="resourceFormModalTitle" :showFooter="!disabled">
        <vxe-form ref="resourceForm" title-align="right" title-width="100" :data="resourceInfo" :rules="resourceFormRules" prevent-submit>
          <vxe-form-item title="发送人" field="nickname" span="24">
            <template>
              <vxe-input v-model="resourceInfo.nickname" readonly :disabled="disabled"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="发送人部门" field="departmentname" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.departmentname" placeholder="请输入发送人部门名称" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="发送时间" field="createdate" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.createdate" placeholder="请输入发送时间" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="阅读时间" v-if="activeName==1" field="readtime" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.readtime" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="消息名称" field="workorder_warn_name" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.workorder_warn_name" placeholder="请输入消息名称" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="消息内容" field="workorder_warn_content" span="24">
            <template>
              <!-- <vxe-input v-model="resourceInfo.workorder_warn_content" clearable :disabled="disabled" placeholder="请输入消息内容" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input> -->
              <el-input type="textarea" :rows="5" clearable :disabled="disabled" v-model="resourceInfo.workorder_warn_content">
              </el-input>
            </template>
          </vxe-form-item>
        </vxe-form>
        <template v-slot:footer>
          <vxe-button type="button" content="取消" @click="$refs.resourceFormModal.close()"></vxe-button>
          <vxe-button type="button" status="primary" content="确定" @click="handleResourceFormModalConfirm"></vxe-button>
        </template>
      </vxe-modal>
      <!-- region 分页-->
      <vxe-pager ref="pager" :current-page="queryParams.pagenumber" :page-size="queryParams.pagesize" :total="page.total" @page-change="handlePageChange">
      </vxe-pager>
      <!-- endregion -->

      <!-- <AssetWarnEdit :modalInfo="editModalInfo" :formDefaultData="formDefaultData" @refreshTable="loadTable" :deviceTypeEnum="deviceTypeEnum" :systemTypeEnum="systemTypeEnum" :systemLoadEnum="systemLoadEnum" :protectionEnum="protectionEnum" :importanceEnum="importanceEnum" :secretEnum="secretEnum" :assetStatusEnum="assetStatusEnum"></AssetWarnEdit> -->
      <!-- <AssetIpBatchEdit :modalInfo="batchEditModalInfo" :formDefaultData="batchEdit_formDefaultData" @refreshTable="loadTable" :deviceTypeEnum="deviceTypeEnum" :protectionEnum="protectionEnum" :importanceEnum="importanceEnum" :secretEnum="secretEnum"></AssetIpBatchEdit> -->
    </el-main>
  </el-container>
</template>

<script>
import {
  pageWorkorderWarn,
  modifyWorkorderWarnUser,
  oneWorkorderWarnFile,
} from "@/api/workorder/warn";
import { pageUsers } from "@/api/security/user";
import { listDictionarys } from "@/api/security/dictionary";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import AssetIpBatchEdit from "@/views/holemanage/asset/ip/components/AssetIpBatchEdit";
import { mapGetters } from "vuex";
import { eventBus } from "@/main";
import { getToken } from '@/utils/auth' // get token from cookie
import { download } from "@/utils/file";

export default {
  name: "index",
  components: { DepartmentTree, AssetIpBatchEdit },
  data() {
    return {
      activeName: "0",
      tabsHeight: 54,
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      userList: [],
      loading: true,
      disabled: false, // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
      page: {
        total: 0,
        records: [],
      },
      token:null,
      resourceInfo: {
      },
      resourceFormRules: {

      },
      resourceFormModalTitle: "详细信息",
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        workorder_warn_id: "",
        workorder_warn_name: "",
        workorder_warn_from: null,
        workorder_warn_content: "",
        workorder_warn_url: "",
        isdelete: "",
        is_read: "0",
        //日期
        startDate: null,
        endDate: null,
        //阅读日期
        startDateRead: null,
        endDateRead: null,
      },

      // 设备类型
      deviceTypeEnum: [],
      // 系统类型
      systemTypeEnum: [],
      // 等级保护
      protectionEnum: [],
      // 重要程度
      importanceEnum: [],
      // 涉密状态
      secretEnum: [],
      // 资产状态枚举
      assetStatusEnum: [],
      // 系统负载枚举
      systemLoadEnum: [],

      // 新增 修改 弹窗的表单默认数据
      formDefaultData: {},
      // 新增 修改 弹窗的显示关闭以及标题等信息
      editModalInfo: {},
      batchEditModalInfo: {},
      batchEdit_formDefaultData: {},
    };
  },
  created() {
    this.token=this.$store.getters.token;
    this.initData();
    this.loadTable();
    this.loadUserTable();
    

    
  },

  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
    this.windowResize();
  },
  methods: {
    initData() {
    },
    // 跳转地址
    gowarn(row) {
      this.handleWarnUserStatus(row);
      const urlJson = JSON.parse(row.workorder_warn_url);
      this.$router.push({ path: urlJson.path, query: urlJson.query });
    },
    goDownload(rows) {
      console.log(rows)
      const row = {};
      row.file_id = rows.file_id;
      row.file_real_name = rows.file_real_name;
      row.file_storage_name = rows.file_storage_name;
      oneWorkorderWarnFile(row).then((res) => {

        console.log(res)
        // download("aa.xls", data)
        // const blob = new Blob([res])
        // const fileName = ""
        // if ('download' in document.createElement('a')) { // 非IE下载
        //   const elink = document.createElement('a')
        //   elink.download = fileName
        //   elink.style.display = 'none'
        //   elink.href = URL.createObjectURL(blob)
        //   document.body.appendChild(elink)
        //   elink.click()
        //   URL.revokeObjectURL(elink.href) // 释放URL 对象
        //   document.body.removeChild(elink)
        // } else { // IE10+下载
        //   navigator.msSaveBlob(blob, fileName)
        // }
        let blob = new Blob([res]);
        let objectUrl = URL.createObjectURL(blob);
        let link = document.createElement("a");
        link.href = objectUrl;
        link.download = `${row.file_real_name}-模板文件.${row.file_storage_name.split(".")[1]
          }`;
        link.click();
      });
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      this.queryParams.is_read = this.activeName;
      this.loadTable();
    },
    // 加载用户表格
    loadUserTable() {
      const query = {};
      query.pagesize = 1000;
      query.pagenumber = 1;
      pageUsers(query)
        .then((response) => {
          this.userList = response.data.records;
        })
        .catch((error) => { });
    },
    handleWarnUserStatus(row) {
      if (row.is_read == "0") {
        const query = {};
        query.workorder_warn_id = row.workorder_warn_id;
        var userName = this.$store.state.user.name;
        if (userName != "admin") {
          modifyWorkorderWarnUser(query)
            .then(({ data, msg }) => {
              if (data) {
                // this.$XModal.message({
                //     message: "修改成功",
                //     status: "success"
                // });
                eventBus.$emit("countWork", '总数变化');

              } else {
                this.$XModal.message({
                  message: msg,
                  status: 'warning'
                });
              }
            })
            .finally(() => {

            });
        }
      }

    },
    // 处理查看
    handleView(row) {
      console.log(row)
      this.$refs.resourceFormModal.open()
      this.disabled = true;
      this.resourceInfo = row;
      // this.formDefaultData = row;
      // this.editModalInfo = {
      //   title: "查看资产",
      //   show: true,
      //   isActionView: true
      // };
    },
    changeWorkorder_warn_from(row) {
    },
    remoteMethodWarnFrom(query) {
      if (query !== "") {
        // this.loading = true;
        // listCustomers(this.queryParams).then(response => {
        //   this.userList = response.rows;
        // });
      } else {
        // this.userList = [];
      }
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight =
        this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight =
          this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },

    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },
// 监听窗口改变
    windowResize() {
      const that = this;
      window.onresize = () => {
        return (() => {
          that.setTableHeight();
          console.log(
            // "窗口resize-----------------" + that.tableHeight
          );
        })();
      };
    },
    /* 字段排序 */
    sortChange(e) {
      console.log("sortChange", e);
      const { field, order } = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },

    // 查询条件
    getQueryCondition() {

      this.queryParams.beginCreatedTime = null;
      this.queryParams.endCreatedTime = null;
      this.queryParams.beginReadTime = null;
      this.queryParams.endReadTime = null;
      this.queryParams.beginCreatedTime = this.queryParams.startDate;
      this.queryParams.endCreatedTime = this.queryParams.endDate;

      this.queryParams.beginReadTime= this.queryParams.startDateRead;
      this.queryParams.endReadTime = this.queryParams.endDateRead;
      // this.queryParams.params['beginCreatedTime'] = this.queryParams.startDate;
      // this.queryParams.params['endCreatedTime'] = this.queryParams.endDate;

      // this.queryParams.params['beginReadTime'] = this.queryParams.startDateRead;
      // this.queryParams.params['endReadTime'] = this.queryParams.endDateRead;
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));

      return quryCondition;
    },
    // 加载表格
    loadTable() {
      const hasToken = getToken()
      if(this.token!=hasToken){
        this.$alert("检测到您切换账户正在为您刷新页面");
        window.location.reload();
      }
      // eventBus.$emit("countWork", '总数变化');
      this.loading = true;
      pageWorkorderWarn(this.getQueryCondition())
        .then((response) => {
          this.page.records = response.data.records;
          this.page.total = response.data.total;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },

  },
  computed: {
    ...mapGetters(["extraHeight"]),
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val;
    },
  },
};
</script>
<style scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
