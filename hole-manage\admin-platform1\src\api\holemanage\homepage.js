import request from '@/utils/request'
import qs from 'qs'


export function homePageCount(data) {
  return request({
    url: '/holemanage/homePage/homePageCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function holeBasicIpCount(data) {
  return request({
    url: '/holemanage/homePage/holeBasicIpCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function holeBasicVulnerableAssetsCount(data) {
  return request({
    url: '/holemanage/homePage/holeBasicVulnerableAssetsCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function holeBasicFixedLeakCount(data) {
  return request({
    url: '/holemanage/homePage/holeBasicFixedLeakCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function todayHoleBasicIpCountTodayAssetIpOnLineCount(data) {
  return request({
    url: '/holemanage/homePage/todayHoleBasicIpCountTodayAssetIpOnLineCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function todayHoleBasicIpCountTodayVulnerableAssetsCount(data) {
  return request({
    url: '/holemanage/homePage/todayHoleBasicIpCountTodayVulnerableAssetsCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function todayHoleBasicIpCountTodayLeakCount(data) {
  return request({
    url: '/holemanage/homePage/todayHoleBasicIpCountTodayLeakCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function todayHoleBasicIpCountTodayFixedLeakCount(data) {
  return request({
    url: '/holemanage/homePage/todayHoleBasicIpCountTodayFixedLeakCount',
    method: 'post',
    data: qs.stringify(data)
  })
}



export function todayHoleBasicIpCount(data) {
  return request({
    url: '/holemanage/homePage/todayHoleBasicIpCount',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function homePageRanking(data) {
  return request({
    url: '/holemanage/homePage/homePageRanking',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageMont(data) {
  return request({
    url: '/holemanage/homePage/homePageMont',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageDeviceType(data) {
  return request({
    url: '/holemanage/homePage/homePageDeviceType',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageOperatingSystem(data) {
  return request({
    url: '/holemanage/homePage/homePageOperatingSystem',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePagePortService(data) {
  return request({
    url: '/holemanage/homePage/homePagePortService',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageAssetIpRate(data) {
  return request({
    url: '/holemanage/homePage/homePageAssetIpRate',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageRepairRate(data) {
  return request({
    url: '/holemanage/homePage/homePageRepairRate',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageVulnerabilityRate(data) {
  return request({
    url: '/holemanage/homePage/homePageVulnerabilityRate',
    method: 'post',
    data: qs.stringify(data)
  })
}


export function homePageRepairLimit(data) {
  return request({
    url: '/holemanage/homePage/homePageRepairLimit',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageRepairDepartment(data) {
  return request({
    url: '/holemanage/homePage/homePageRepairDepartment',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageRepairMont(data) {
  return request({
    url: '/holemanage/homePage/homePageRepairMont',
    method: 'post',
    data: qs.stringify(data)
  })
}


export function homePageNotRepairRate(data) {
  return request({
    url: '/holemanage/homePage/homePageNotRepairRate',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageNotVulnerabilityRate(data) {
  return request({
    url: '/holemanage/homePage/homePageNotVulnerabilityRate',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageNotRepairLimit(data) {
  return request({
    url: '/holemanage/homePage/homePageNotRepairLimit',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageNotRepairDepartment(data) {
  return request({
    url: '/holemanage/homePage/homePageNotRepairDepartment',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function homePageNotRepairMont(data) {
  return request({
    url: '/holemanage/homePage/homePageNotRepairMont',
    method: 'post',
    data: qs.stringify(data)
  })
}
