package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 检测目标表
 * 检测类型为：IP地址/网站
 * 目标类型为：分组
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "task_target_group")
public class TaskTargetGroup extends BaseEntity<TaskTargetGroup> implements Serializable {

    private static final long serialVersionUID = -6316031388813905786L;

    @TableId
    private String task_target_group_id;
    private String task_basic_id;
    private String asset_ip_type;
    /**
     * 分组部门id 逗号分割
     */
    private String departmentids;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
