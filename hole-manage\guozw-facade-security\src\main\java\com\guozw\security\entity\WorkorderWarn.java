package com.guozw.security.entity;


import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guozw.common.core.base.BaseEntity;
import com.guozw.security.dto.PathAddressDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "workorder_warn")
public class WorkorderWarn extends BaseEntity<WorkorderWarn> implements Serializable {

  private static final long serialVersionUID = -3388755116862951536L;

  /**
   * 消息名称
   */
  @TableId
  private String workorder_warn_id;
  /**
   * 消息名称
   */
  private String workorder_warn_name;
  /**
   * 发送人用户id
   */
  private String workorder_warn_from;
  /**
   * 消息内容
   */
  private String workorder_warn_content;
  /**
   * 跳转地址，以json方式存储，链接后需要带的条件也一并存在此字
   */
  private String workorder_warn_url;
  /**
   * 是否删除  0-未删除 1-已删除
   */
  private String isdelete;

  private String hole_plan_back_id;


  @Override
  public String toString() {
    return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
  }

}
