import request from '@/utils/request'
import qs from 'qs'

export function pageWorkorderWarn(data) {
  return request({
    url: '/workorder/warn/pageWorkorderWarn',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function oneWorkorderWarnFile(data) {
  return request({
    url: '/workorder/warn/oneWorkorderWarnFile',
    method: 'post',
    data: qs.stringify(data),
    responseType: 'blob'
  })
}
export function countWorkorderWarn(data) {
  return request({
    url: '/workorder/warn/countWorkorderWarn',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function modifyWorkorderWarnUser(data) {
  return request({
    url: '/workorder/warn/modifyWorkorderWarnUser',
    method: 'post',
    data: qs.stringify(data)
  })
}


