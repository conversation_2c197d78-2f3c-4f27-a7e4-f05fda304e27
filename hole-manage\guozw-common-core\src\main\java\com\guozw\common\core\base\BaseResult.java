package com.guozw.common.core.base;

import cn.hutool.core.date.DateUtil;
import com.guozw.common.core.constant.ErrorCodeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/5/2
 */
@Slf4j
@Accessors(chain = true)
@Data
public class BaseResult<T> implements Serializable {


    private static final long serialVersionUID = -434049328851095442L;

    /**
     * 响应码
     */
    private Integer code;
    /**
     * 消息
     */
    private String message;
    /**
     * 响应数据
     */
    private T data;
    /**
     * 状态
     */
    private boolean status = true;
    /**
     * 时间戳
     */
    private String timestamp = DateUtil.now();

    public BaseResult() {
    }

    public BaseResult(T t) {
        this.data = t;
    }

    public BaseResult(ErrorCodeEnum errorCodeEnum) {
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getMessage();
    }
    public BaseResult(int code, String message) {
        this.code = code;
        this.message = message;
    }
    public static BaseResult of(ErrorCodeEnum errorCodeEnum) {
        return new BaseResult<>(errorCodeEnum).setStatus(Boolean.FALSE);
    }
    public static BaseResult success() {
        return new BaseResult<>(ErrorCodeEnum.SUCCESS);
    }

    public static BaseResult failed() {
        return new BaseResult(ErrorCodeEnum.FAILED).setStatus(Boolean.FALSE);
    }

    public static BaseResult forbidden() {
        return new BaseResult(ErrorCodeEnum.FORBIDDEN).setStatus(Boolean.FALSE);
    }
}
