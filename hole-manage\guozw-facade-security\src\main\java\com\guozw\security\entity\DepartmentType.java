package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 部门类型表
 * <AUTHOR>
 * @date 2020/5/4
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "departmenttype")
public class DepartmentType extends Model<DepartmentType> implements Serializable {


    private static final long serialVersionUID = 1313086838538704299L;
    /**
     * 部门类别编码
     */
    @TableId
    private String departmenttypecode;
    /**
     * 部门类别名称
     */
    private String departmenttypename;
    /**
     * 部门类别描述
     */
    private String departmenttypenote;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
