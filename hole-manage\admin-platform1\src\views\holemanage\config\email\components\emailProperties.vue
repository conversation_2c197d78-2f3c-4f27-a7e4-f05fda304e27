<template>
  <div>
    <vxe-form
      ref="myForm"
      align="center"
      title-width="120"
      title-align="right"
      :data="emailForm"
      :loading="loading"
      :rules="rules"
      span="6"
      style="width: 400px;"
    >
      <vxe-form-item title="邮箱地址" field="email_address" span="24">
        <template v-slot>
          <vxe-input v-model="emailForm.email_address" placeholder="请输入邮箱地址" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="登录密码" field="email_password" span="24">
        <template v-slot>
          <vxe-input type="password" v-model="emailForm.email_password" placeholder="请输入登录密码" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="显示名称" field="email_name" span="24">
        <template v-slot>
          <vxe-input v-model="emailForm.email_name" placeholder="请输入显示名称" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="发件服务器" field="email_from_server" span="24">
        <template v-slot>
          <vxe-input v-model="emailForm.email_from_server" placeholder="请输入发件服务器" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="使用ssl" field="isssl" span="24">
        <template v-slot>
          <vxe-radio-group v-model="emailForm.isssl">
            <vxe-radio label="1" content="是"></vxe-radio>
            <vxe-radio label="0" content="否"></vxe-radio>
          </vxe-radio-group>

        </template>
      </vxe-form-item>
      <vxe-form-item title="端口" field="email_port" span="24">
        <template v-slot>
          <vxe-input v-model="emailForm.email_port" placeholder="请输入端口" clearable></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24">
        <template v-slot>
          <vxe-button status="primary" @click="testEmail">测试连通性</vxe-button>
          <vxe-button status="primary" @click="submit">提交</vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
  </div>
</template>

<script>
import {getConfigEmailOne, saveConfigEmail,testEmail} from '@/api/config/email'
import {validEmail} from "@/utils/validate";

export default {
  name: "emailProperties",
  data() {
    return {
      emailForm: {},
      loading: false,
      rules: {
        email_address: [{
          required:true,
          trigger:'change',
          validator: ({itemValue}) => {
            if (validEmail(itemValue)) {
            } else {
              return new Error("绑定邮箱格式不正确");
            }
          }
        }],
        email_password: [{required: true, message: '请输入登录密码'}],
        email_from_server: [{required: true, message: '请输入发件服务器'}],
        email_port: [{required: true, message: '请输入端口'}],
      },
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.loading = true;
      getConfigEmailOne().then(res => {
        this.emailForm = res.data;
        if(!res.data.email_from_server){
          this.emailForm.email_from_server = "newmail.sgcc.com.cn";
        }
        if(!res.data.email_port){
          this.emailForm.email_port = "25";
        }
        if (!res.data.isssl){
          this.emailForm.isssl = '1';
        }
      }).finally(e => {
        this.loading = false;
      })
    },
    submit() {
      this.loading = true;
      this.$refs.myForm.validate().then(()=>{
        let emailForm = this.emailForm;
        saveConfigEmail(emailForm).then(res=>{
          this.$XModal.message({
            message: '操作成功',
            status:'success'
          })
        })
      }).finally(e=>{
        this.loading = false;
      })
    },
    testEmail(){
      this.loading = true;
      this.$refs.myForm.validate().then(()=>{
        let emailForm = this.emailForm;
        testEmail(emailForm).then(res=>{
          if (res.data){
            this.$XModal.message({
              message: '连接成功！',
              status:'success'
            })
          }else {
            this.$XModal.message({
              message: '连接失败！请重新检查配置！',
              status: 'error',
            })
          }


        })
      }).finally(e=>{
        this.loading = false;
      })
    }
  }
}
</script>

<style scoped>

</style>
