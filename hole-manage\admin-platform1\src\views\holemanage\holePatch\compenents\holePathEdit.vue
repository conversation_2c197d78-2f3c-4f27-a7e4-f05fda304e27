<template>
  <div id="topDiv">
    <vxe-modal height="99%" width="40%" position="center" resize :title="modalInfo.title" v-model="modalInfo.show" @close="handleModalClose()" showFooter>
      <vxe-form ref="myForm" title-width="120" :data="modalForm" :rules="rules" title-align="right" prevent-submit span="12">
        <!-- <div class="bigTitle">
          <span>基本信息</span>
          <el-divider content-position="left"></el-divider>
        </div> -->
        <vxe-form-item title="官方地址" field="hole_patch_url">
          <vxe-input clearable :disabled="!show" placeholder="官方地址" v-model="modalForm.hole_patch_url"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="补丁描述" field="hole_patch_remark">
          <vxe-input clearable :disabled="!show" placeholder="补丁描述" v-model="modalForm.hole_patch_remark"></vxe-input>
        </vxe-form-item>
        <!-- <vxe-form-item title="关联漏洞" span="24" field="tool_make">
          <vxe-select :disabled="!show" @change="selectToolMake" clearable placeholder="关联漏洞" v-model="modalForm.tool_make" :options="brandEnum"></vxe-select>
        </vxe-form-item> -->

        <vxe-form-item title="关联漏洞" field="library_patch_id" span="24">
          <el-select v-model="modalForm.library_patch_id" filterable placeholder="请选择关联漏洞" style="width: 100%" :multiple="true" remote :remote-method="remoteMethod" :loading="loading" :clearable="true">
            <el-option v-for="item in districtOption" :key="item.hole_library_id" :label="item.hole_library_name" :value="item.hole_library_id">
            </el-option>
          </el-select>
          <!-- <el-select v-model="modalForm.holePatchVO.hole_patch_id" style="width:350px" filterable placeholder="请输入关联漏洞" remote reserve-keyword :remote-method="remoteMethod" :loading="loading" :clearable="true" @change="selectDistrict($event)">
            <el-option v-for="item in districtOption" :key="item.hole_library_id" :label="item.hole_library_name" :value="item.hole_library_id">
            </el-option>
          </el-select> -->
        </vxe-form-item>
        <vxe-form-item title="附件" field="file_id" title-align="right" align="top">
          <template v-slot="scope">

            <el-upload ref="upload" :limit="1" v-if="uploadshow==false" :file-list="fileList" :on-remove="handleRemove" :before-remove="beforeRemove" :headers="upload.headers" :data="formData" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
              <div class="el-upload__tip" slot="tip">
              </div>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <!-- <div class="el-upload__tip" style="color: red" slot="tip">
                提示：仅允许导入“xml”格式文件！
              </div> -->
            </el-upload>
            <el-upload ref="upload" :limit="1" v-if="uploadshow" :headers="upload.headers" :data="formData" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
              <div class="el-upload__tip" slot="tip">
              </div>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
              <!-- <div class="el-upload__tip" style="color: red" slot="tip">
                提示：仅允许导入“xml”格式文件！
              </div> -->
            </el-upload>
          </template>
        </vxe-form-item>
      </vxe-form>

      <template v-slot:footer>
        <el-button type="" @click="handleDialogCancel" v-if="show">取消</el-button>
        <el-button type="primary" :loading="loading" v-if="show" @click="submitFileForm">确定</el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import { saveToolBasic } from "@/api/testManage";
import { saveAndSendHolePath, pageHoleLibrary, updateHolePath } from "@/api/holemanage/asset";
import { getToken } from '@/utils/auth';
import { clearProperty, copyProperty } from "@/utils/guozw-core.js";

export default {
  name: "ToolsDialog",
  components: {},
  props: {
    // 品牌枚举
    brandEnum: {
      type: Array,
      default: () => []
    },
    // 协议枚举
    agreementEnum: {
      type: Array,
      default: () => []
    },
    selectEnum: {
      type: Array,
      default: () => []
    },
    modalInfo: {
      type: Object,
      default: () => { }
    },
    formDefaultData: {
      type: Object,
      default: () => { }
    }
  },
  watch: {
    immediate: true,
    "modalInfo.type": {
      handler(n) {
        this.selectShow = {};

        this.fileList = [];
        this.passwordkey = null;
        if (n === "edit") {
          console.log("sad")
          this.uploadshow = false;
          this.modalForm = this.modalInfo.data;
          this.fileList = [{ name: this.modalForm.file_real_name, url: this.modalForm.file_storage_name }]

          this.formData.library_patch_id = this.modalForm.library_patch_id;
          this.formData.hole_patch_id = this.modalForm.hole_patch_id;
          this.formData.hole_patch_url = this.modalForm.hole_patch_url;
          this.formData.hole_patch_remark = this.modalForm.hole_patch_remark;
          console.log(this.modalForm)
          const rows = {};
          rows.value = this.modalForm.tool_make;
          this.selectToolMake(rows);
        } else if (n === "view") {
          this.modalForm = this.modalInfo.data;
          const rows = {};
          rows.value = this.modalForm.tool_make;
          this.selectToolMake(rows);
          this.show = false;
        } else {
          this.uploadshow = true;
          console.log(
            this.uploadshow)
          this.show = true;
        }
      }
    }

    // formDefaultData(newVal, oldVal) {
    //   copyProperty(newVal, this.modalForm);
    //   //   initData()
    // }
  },
  computed: {
    handleDisabled() {
      return this.modalInfo.type === "edit";
    }
  },
  data() {
    return {
      loading: false,
      modalForm: {
        library_patch_id: [],
        hole_patch_id: "",
        hole_patch_url: "",
        hole_patch_remark: "",
        holePatchVO: {},
      },
      queryParams: {},
      modalData: {},
      formData: {},
      uploadshow: true,
      // 导入参数
      upload: {
        // 弹出层标题（文件导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/holemanage/holeLibrary/saveAndSendHolePath',
      },
      // 文件列表
      fileList: [],
      //漏洞库
      districtOption: [],
      baseApi: process.env.VUE_APP_BASE_API,
      passwordkey: null,
      show: true,
      selectShow: {},
      rules: {
        library_patch_id: [
          { required: true, message: "必填字段" },
          { validator: this.checkAssetDepartmentId }
        ],

        hole_patch_url: [{ required: true, message: "必填字段" }],
        // file_id: [{ required: true, message: "必填字段" }],
      },
      // 关闭弹窗的时候是否需要刷新表格
      isRefreshTable: false
    };
  },
  created() {
    this.queryParams.pagenumber = 1;
    this.queryParams.pagesize = 10000000;
  },
  mounted() { },
  methods: {
    resetForm() {
      this.modalInfo.show = false;
      this.modalInfo.type = "";
      this.modalInfo.data = {};
      this.$refs.myForm.clearValidate();
      this.modalForm = {
        library_patch_id: [],
        hole_patch_id: "",
        hole_patch_url: "",
        hole_patch_remark: "",
        holePatchVO: {},
      };
    },
    selectToolMake(rows) {
      this.selectShow = {};
      this.selectEnum.forEach(item => {
        if (rows.value == item.value) {
          this.selectShow = JSON.parse(item.label);
        }
      })
    },
    uploadFile(params) {
      console.log("看看")
      console.log("uploadFile", params);
    },
    handleModalClose() {
      this.resetForm();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
    },
    handleDialogCancel() {
      this.resetForm();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      console.log("sss")
      this.upload.isUploading = true;
    },

    //上传后再删除文件
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let fileId = this.fileList.map((item) => item.fileId);
      this.ruleForm.fileId = fileId;
    },
    beforeRemove(file, fileList) {

      this.uploadshow = true;
      // return this.$confirm(`确定移除 ${file.name}？`);
    },
    // 自定义提示信息方法
    checkAssetDepartmentId({ itemValue }) {
      if (itemValue.length == 0 || itemValue == null) {
        return new Error("必填字段");
      }
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      console.log("sdsf")
      console.log(fileList)
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("上传成功", '导入结果', { dangerouslyUseHTMLString: true });
      this.$emit("refreshTable");
      this.modalInfo.show = false;

      this.modalInfo.type = "";
    },
    patchchange() {
      console.log(this.modalForm.library_patch_id)
      if (this.modalForm.library_patch_id == null && this.modalForm.library_patch_id.length == 0) {
        this.modalForm.library_patch_id = null;
      }
    },
    // 提交上传文件
    submitFileForm() {
      // 添加验证，立即触发校验
      this.$refs['myForm'].validate('rules')
      if (this.modalForm.library_patch_id.length == 0) {
        return;
      }
      if (this.modalForm.hole_patch_url == null || this.modalForm.hole_patch_url.length == "") {
        return;
      }

      const queryCondition = JSON.parse(JSON.stringify(this.modalForm));;

      this.formData.library_patch_id = this.modalForm.library_patch_id;
      this.formData.hole_patch_id = this.modalForm.hole_patch_id;
      this.formData.hole_patch_url = this.modalForm.hole_patch_url;
      this.formData.hole_patch_remark = this.modalForm.hole_patch_remark;
      this.$refs.upload.$forceUpdate();
      if (this.modalForm.file_storage_name != null && this.modalForm.file_storage_name != undefined && this.$refs.upload.uploadFiles[0].url == this.modalForm.file_storage_name) {

        this.loading = true;
        updateHolePath(this.formData)
          .then(response => {

            this.$alert("上传成功");
            clearProperty(this.modalForm);
            this.$emit("refreshTable");
            this.modalInfo.show = false;

          })
          .finally(() => {
            this.resetForm();
            this.loading = false;
            this.modalInfo.show = false;
          });
      } else {

        this.$refs.upload.submit();
        this.resetForm();

      }

      this.modalInfo.type = "";

    },
    // handleRemove(file, fileList) {
    //   console.log(file, fileList);
    // },
    handlePreview(file) {
      console.log(file);
    },
    handleChange(file, fileList) {
      console.log("onChange", file, fileList);
      if (this.checkFile(file)) {
        // 移除校验失败的文件
        this.$refs.upload.uploadFiles.splice(
          this.$refs.upload.uploadFiles
            .length - 1,
          1
        );
        console.log("onChange", fileList);
        fileList.push(file);
        this.fileList = fileList;
        return;
      }
    },
    /* 文件校验 */
    checkFile(file) {
      // // 判断文件大小是否符合要求
      // if (file.size / 1024 / 1024 > 5) {
      //   this.$XModal.message({
      //     message: "单个上传文件大小不能超过 5 M",
      //     status: "error"
      //   });
      //   return false;
      // }

      return true;
    },
    getQueryCondition() {

      let queryCondition = JSON.parse(JSON.stringify(this.modalForm));
      !queryCondition.hole_patch_id
        ? this.$set(
          queryCondition,
          "hole_patch_id",
          this.modalInfo.data.hole_patch_id
        )
        : null;
      return queryCondition;
    },
    //漏洞库
    selectDistrict(val) {
      // if (val != null && val != '' && val != undefined) {
      //   let obj = this.districtOption.find((item) => {
      //     return item.customersId === val;
      //   });

      // }

      // this.patchchange();
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        this.queryParams.searchValue = query;
        pageHoleLibrary(this.queryParams).then((response) => {
          this.districtOption = response.data.records;
          this.loading = false;
        });
      }else {
        this.districtOption = [];
      }

    },
    queryLibraryByIds(ids){
      if (ids != null && ids.length > 0){
        pageHoleLibrary({holePatchIds:ids}).then((response) => {
          this.districtOption = response.data.records;
        });
      }
    },

    // 提交表单
    submitForm() {

      this.$refs.myForm
        .validate()
        .then(() => {
          this.loading = true;
          const queryCondition = this.getQueryCondition();
          saveAndSendHolePath(queryCondition)
            .then(response => {
              this.$XModal.message({
                message: this.modalForm.tool_basic_id
                  ? "修改成功"
                  : "新增成功",
                status: "success"
              });
              this.$emit("refreshTable");
              this.modalInfo.show = false;

              this.modalInfo.type = "";

            })
            .finally(() => {
              this.loading = false;
              clearProperty(this.modalForm);
              this.modalInfo.show = false;
            });
        })
        .catch(err => {
          console.log(err);
          return false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
#topDiv {
  .vxe-form /deep/ .vxe-form--item-inner {
    min-height: 30px !important;
  }
  .bigTitle {
    margin-top: 10px;
    span {
      font-size: 15px;
      font-weight: bolder;
    }
  }

  .lastItem {
    margin-bottom: 10px !important;
  }
  .el-divider--horizontal {
    margin: 5px 0;
  }
  .tip {
    color: red;
    font-size: 10px;
  }

  .my-dropdown1 {
    height: 200px;
    overflow: auto;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
  }
  .list-item1:hover {
    background-color: #f5f7fa;
  }
  .addRow {
    float: right;
    position: relative;
    top: -5px;
  }
  .delRow {
    float: right;
    margin-left: 5px;
    position: relative;
    top: -5px;
  }
}
</style>
