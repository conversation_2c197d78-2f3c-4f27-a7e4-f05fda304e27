package com.guozw.common.core.constant;

import cn.hutool.core.map.MapUtil;
import com.guozw.common.core.bean.AppInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by guozw on 2020/5/5.
 */
public class CommonConstant {

    /**
     * 树的根节点pid
     */
    public static final String ROOT_ID = "0";
    /**
     * 记住我
     */
    public static final int REMEMBERME = 1;

    /**
     * 通用是
     */
    public static final Integer YES = 1;
    /**
     * 通用否
     */
    public static final Integer NO = 0;
    /**
     * 默认分隔符
     */
    public static final String DEFAULT_SEPARATOR = ",";
    /**
     * 登录表单的用户名参数名称
     */
    public static final String USERNAME_PARAMETER = "username";
    /**
     * 登录表单的密码参数名称
     */
    public static final String PASSWORD_PARAMETER = "password";
    /**
     * 登录表单的验证码参数名称
     */
    public static final String CODE_PARAMETER = "code";
    /**
     * 验证码校验的随机值参数
     */
    public static final String RANDOMCODE_PARAMETER = "random_code";
    public static final String LIMIT_ONE = "limit 1";
    public static final int MAX_PAGE_SIZE = 1000;

    /**
     * 系统API是否加密
     */
    public static final boolean SYS_API_ENCRYPT = true;

    // region 日期格式
    public static final String TIME_ZONE = "GMT+8";
    // endregion

    // region 各系统模块名称
    public static final String MODULE_SECURITY = "SECURITY";
    public static final String MODULE_HOLEMANAGE = "HOLEMANAGE";
    // endregion

    // region 访问类型
    public static final String ACCESS_TYPE_LOGIN = "LOGIN";
    public static final String ACCESS_TYPE_LOGOUT = "LOGOUT";
    public static final String ACCESS_TYPE_QUERY = "QUERY";
    public static final String ACCESS_TYPE_SAVE = "SAVE";
    public static final String ACCESS_TYPE_ADD = "ADD";
    public static final String ACCESS_TYPE_EDIT = "EDIT";
    public static final String ACCESS_TYPE_DELETE = "DELETE";
    public static final String ACCESS_TYPE_START = "START";
    public static final String ACCESS_TYPE_STOP = "STOP";
    public static final String ACCESS_TYPE_PAUSE = "PAUSE";
    public static final String ACCESS_TYPE_CONTINUE = "CONTINUE";


    public static final String REDIS_KEY = "_loginTimes";

    public static final int LOGIN_TIMES = 5;

    public static final int LOGIN_TIME_OUT_SECONDS = 300;
    // endregion

    /**
     * 调用api接口的应用列表信息
     */
    public static List<AppInfo> apiAppList = new ArrayList<>();

    static {
        apiAppList.add(new AppInfo().setAppId("1").setAppSecret("1234567899999999"));
    }



}
