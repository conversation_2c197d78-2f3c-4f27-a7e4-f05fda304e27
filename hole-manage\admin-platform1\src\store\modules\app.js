import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium',
  activeMenu: '/calc/zbgz/shrz/index'
}

const mutations = {
  TOGGLE_SIDEBAR: (state, type = '') => {
    if (localStorage.getItem('menuStyle')) {
      if (type) {
        // 只有点击面包屑图标才可以切换菜单伸缩
        state.sidebar.opened = !state.sidebar.opened
        state.sidebar.withoutAnimation = false
        if (state.sidebar.opened) {
          Cookies.set('sidebarStatus', 1)
        } else {
          Cookies.set('sidebarStatus', 0)
        }
      } else {
        // 调整菜单模式时，强制不伸缩
        state.sidebar.opened = true
        state.sidebar.withoutAnimation = false
        Cookies.set('sidebarStatus', 1)
      }
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_ACTIVEMENU: (state, path) => {
    state.activeMenu = path
  }
}

const actions = {
  toggleSideBar ({ commit }, type) {
    commit('TOGGLE_SIDEBAR', type)
  },
  closeSideBar ({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice ({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize ({ commit }, size) {
    commit('SET_SIZE', size)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
