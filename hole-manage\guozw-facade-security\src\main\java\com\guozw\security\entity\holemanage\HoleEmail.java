package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 漏洞邮件通知通告
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "hole_email")
public class HoleEmail extends BaseEntity<HoleEmail> implements Serializable {

    private static final long serialVersionUID = 9136942014931295611L;
    @TableId
    private String hole_email_id;
    /**
     * 系统用户id,多个是用英文;隔开，通过选择系统用户，可以带出系统该用户的邮箱
     */
    private String email_to_users;
    /**
     * 收件人邮箱号,多个是用英文;隔开
     */
    private String email_number;
    /**
     * 发件模板ID，config_email_template表主键
     */
    private String config_email_template_id;
    /**
     * 邮件主题
     */
    private String email_theme;
    /**
     * 邮件正文
     */
    private String email_text;
    /**
     * 附件ID
     */
    private String file_id;
    /**
     * 附件类型
     */
    private String file_type;
    /**
     * 发件人用户ID
     */
    private String email_user;
    /**
     * 对应处置反馈表主键
     */
    private String hole_plan_back_id;




    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
