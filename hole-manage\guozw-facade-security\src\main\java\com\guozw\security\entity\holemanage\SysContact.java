package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 系统管理-联系人表（有关联系人的都存此表）
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "sys_contact")
public class SysContact extends BaseEntity<SysContact> implements Serializable {

    private static final long serialVersionUID = -5041668494705605876L;
    /**
     * 主键
     */
    @TableId
    private String contact_id;
    /**
     * 关联类型 1-IP资产  2-网站资产
     */
    private String relation_type;
    /**
     * 关联表主键 IP资产表主键或网站资产表主键
     */
    private String relation_id;
    /**
     * 联系人姓名
     */
    private String contact_name;
    /**
     * 联系人电话
     */
    private String contact_phone;
    /**
     * 联系人邮箱
     */
    private String contact_email;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
