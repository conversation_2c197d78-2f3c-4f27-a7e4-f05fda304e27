<template>
    <vxe-modal
        ref="roleFormModal"
        maxHeight="90%"
        width="40%"
        position="center"
        resize
        :title="modalInfo.title"
        v-model="modalInfo.show"
        @close="handleClose"
        :showFooter="showFooter"
    >
        <vxe-form
            ref="roleForm"
            title-align="right"
            title-width="100"
            :data="roleInfo"
            :rules="roleFormRules"
            prevent-submit
        >
            <vxe-form-item title="角色名称" field="rolename" span="24">
                <template v-slot="scope">
                    <vxe-input
                        v-model.trim="roleInfo.rolename"
                        placeholder="请输入角色名称"
                        clearable
                        :disabled="disabled"
                        @input="$refs.roleForm.updateStatus(scope)"
                    ></vxe-input>
                </template>
            </vxe-form-item>
            <vxe-form-item title="角色描述" field="rolenote" span="24">
                <template v-slot="scope">
                    <vxe-input
                        v-model.trim="roleInfo.rolenote"
                        placeholder="请输入角色描述"
                        clearable
                        :disabled="disabled"
                        @input="$refs.roleForm.updateStatus(scope)"
                    ></vxe-input>
                </template>
            </vxe-form-item>
            <vxe-form-item title="角色排序" field="ordernumber" span="24">
                <template v-slot="scope">
                    <vxe-input
                        v-model.trim="roleInfo.ordernumber"
                        type="number"
                        placeholder="请输入角色排序"
                        clearable
                        :disabled="disabled"
                        @input="$refs.roleForm.updateStatus(scope)"
                    ></vxe-input>
                </template>
            </vxe-form-item>
        </vxe-form>
        <template v-slot:footer>
            <vxe-button
                type="button"
                content="取消"
                @click="handleClose"
            ></vxe-button>
            <vxe-button
                type="button"
                status="primary"
                content="确定"
                @click="handleSubmit"
            ></vxe-button>
        </template>
    </vxe-modal>
</template>
<script>
import { oneRole, modifyRole, saveRole, roleExists } from "@/api/security/role";
import { clearProperty, copyProperty } from "@/utils/guozw-core";
export default {
    name: "Edit",
    data() {
        return {
            roleInfo: {
                rolecode: null,
                rolename: null,
                rolenote: null,
                ordernumber: null
            },
            showFooter: true,
            disabled: false,
            roleFormRules: {
                rolename: [
                    { required: true, message: "请输入角色名称" },
                    {
                        pattern: "^[A-Z_]{1,30}$",
                        message: "只能输入 1 到 30 个大写英文字母或下划线"
                    }
                ],
                rolenote: [
                    { required: false, message: "请输入角色名称" },
                    { min: 0, max: 50, message: "长度在 0 到 50 个字符" }
                ],
                ordernumber: [{ required: true, message: "请输入角色排序" }]
            }
        };
    },
    props: {
        modalInfo: {
            type: Object,
            default: () => {
                return { show: null, title: null, type: null, rolecode: null };
            }
        }
    },
    watch: {
        "modalInfo.type": {
            immediate: true,
            handler(newVal, oldVal) {
                this.disabled = false;
                if (newVal == this.$guozwSettings.operationType[2].type) {
                    this.showFooter = false;
                    this.disabled = true;
                }
            }
        },
        "modalInfo.rolecode": {
            immediate: true,
            handler(newVal, oldVal) {
                if (newVal) {
                    this.roleInfo.rolecode = newVal;
                    this.loadRoleInfo();
                } else {
                    clearProperty(this.roleInfo);
                }
            }
        }
    },
    mounted() {},
    methods: {
        loadRoleInfo() {
            oneRole({ rolecode: this.roleInfo.rolecode }).then(response => {
                copyProperty(response.data, this.roleInfo)
            });
        },
        handleSubmit() {
            this.$refs.roleForm.validate().then(() => {
                if (this.roleInfo.rolecode) {
                    // 修改角色
                    modifyRole(this.roleInfo).then(response => {
                        this.modalInfo.show = false;
                        this.$XModal.alert({
                            message: "修改成功",
                            position: "center",
                            status: "success"
                        });
                        this.$emit("refreshTable");
                    });
                } else {
                    // 新增角色
                    roleExists({ rolename: this.roleInfo.rolename })
                        .then(response => {
                            return response.data;
                        })
                        .then(response => {
                            // 角色已经存在
                            if (response) {
                                this.$XModal.alert({
                                    message: "角色已存在",
                                    position: "center",
                                    status: "warning"
                                });
                            } else {
                                saveRole(this.roleInfo).then(response => {
                                    this.modalInfo.show = false;
                                    this.$XModal.alert({
                                        message: "新增成功",
                                        position: "center",
                                        status: "success"
                                    });
                                    this.$emit("refreshTable");
                                });
                            }
                        });
                }
            });
        },
        handleClose() {
            this.$refs.roleFormModal.close();
            this.modalInfo.show = false;
            this.$nextTick(() => {
                this.$refs.roleForm.clearValidate();
            });
        }
    }
};
</script>
