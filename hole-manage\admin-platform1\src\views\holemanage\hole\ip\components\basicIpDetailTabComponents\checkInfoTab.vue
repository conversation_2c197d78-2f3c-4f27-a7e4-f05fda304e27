<template>
  <div style="height: auto;">
    <span style=" font-size: 20px; font-weight: bold;">自定义 </span>
    <el-form label-position="right">
      <el-row :gutter="20">
        <el-col :span="6" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="扫描编号:">
              <span>{{ holeBasicIpInfo.departmentname }}/{{ holeType=='1'? holeBasicIpInfo.asset_ip: holeBasicIpInfo.asset_website_name }}</span>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6" :offset="6">
          <div class="grid-content bg-purple">
            <div class="grid-content bg-purple">
              <el-form-item label="漏洞名称:">
                <span>{{ holeBasicIpInfo.hole_name }}</span>
              </el-form-item>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="漏洞类型:">
              <span>{{ holeBasicIpInfo.hole_type }}</span>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6" :offset="6">
          <div class="grid-content bg-purple">
            <el-form-item label="CVE编号:">
              <span>{{ holeBasicIpInfo.hole_cve }}</span>
            </el-form-item>
          </div>
        </el-col>

      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="CVSS分值:">
              <span>{{ holeBasicIpInfo.updatetime }}</span>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6" :offset="6">
          <div class="grid-content bg-purple">
            <el-form-item label="CVSS向量:">
              <span>{{ holeBasicIpInfo.updatetime }}</span>
            </el-form-item>
          </div>
        </el-col>

      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="利用程序和平台:">
              <span>{{ holeBasicIpInfo.updatetime }}</span>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="6" :offset="6">
          <div class="grid-content bg-purple">
            <el-form-item label="漏洞公布时间:">
              <span>{{ holeBasicIpInfo.createtime }}</span>
            </el-form-item>
          </div>
        </el-col>

      </el-row>
      <el-row :gutter="20">
        <el-col :span="17" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="漏洞说明:">
              <span>{{ holeBasicIpInfo.hole_remark }}</span>
            </el-form-item>
          </div>
        </el-col>


      </el-row>
      <el-row :gutter="20">
        <el-col :span="17" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="修复建议:">
              <span>{{ holeBasicIpInfo.hole_repair }}</span>
            </el-form-item>
          </div>
        </el-col>


      </el-row>
      <el-row :gutter="20">
        <el-col :span="17" :offset="3">
          <div class="grid-content bg-purple">
            <el-form-item label="漏洞参考信息:">
              <span>{{ holeBasicIpInfo.hole_referenceinfo }}</span>
            </el-form-item>
          </div>
        </el-col>


      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "checkInfoTab",
  data() {
    return {}
  },
  props: {
    holeBasicIpInfo: {
      type: Object,
      require: true
    },
    holeType:{
      type: String,
      default: '1'
    }
  }
}
</script>

<style scoped>
.contentBox {
  width: 100%;
  height: 200px;
  border: solid 0.2px #909399;
  /*margin-top: 10px;*/
  margin: 10px 0px;
  overflow-y: auto
}
</style>
