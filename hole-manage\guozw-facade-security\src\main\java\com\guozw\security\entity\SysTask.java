package com.guozw.security.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 定时任务
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "sys_task")
public class SysTask extends BaseEntity<SysTask> implements Serializable {
    private static final long serialVersionUID = 684503116419150387L;

    /**
     * 主键
     */
    @TableId
    private String task_id;
    /**
     * 任务名称
     */
    private String task_name;
    /**
     * 任务描述
     */
    private String task_note;
    /**
     * 定时任务执行类名
     */
    private String task_clazz;
    /**
     * cron表达式
     */
    private String task_cron;


}
