server:
  port: 9000
  servlet:
    context-path: /gateway
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: dubbo-provider-gateway
  devtools:
    restart:
      enabled: true

  # redis配置
  redis:
    database: 1
    host: **************
    port: 6379
    password: Jxtz@888 # 密码 默认为空
    lettuce:
      pool:
        max-active: 20 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10  # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
    timeout: 10000 # 连接超时时间（毫秒）

  cloud:
    # Nacos注册中心相关配置
    nacos:
      discovery:
        #        server-addr: 127.0.0.1:8848,127.0.0.1:8849,127.0.0.1:8850 # 注册中心地址
        server-addr: 127.0.0.1:8848 # 注册中心地址
        register-enabled: true # 默认开启自动注册
      config:
        #        server-addr: 127.0.0.1:8848,127.0.0.1:8849,127.0.0.1:8850
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yaml # 配置文件后缀
        group: DEFAULT_GROUP # 默认 DEFAULT_GROUP
        encode: UTF-8
        enabled: true

    # 网关配置
    gateway:
      discovery:
        locator:
          enabled: true # 启动服务中心注册与发现，通过服务id访问服务
          lower-case-service-id: true # 服务id默认小写，不能使用下划线，应使用中划线
      routes:
        - id: security # 路由的唯一标识
          uri: lb://dubbo-provider-security # 如果断言成功，将要转发出去的地址
          predicates: # 断言，满足所有断言才会转发
            - Path=${server.servlet.context-path}/security/** # 注意这里使用等号不是冒号
          filters:
            - StripPrefix=1 # 跳过指定路径 如请求/gateway/security/auth/login 转发到/security/auth/login
        - id: log # 路由的唯一标识
          uri: lb://dubbo-provider-log # 如果断言成功，将要转发出去的地址
          predicates: # 断言，满足所有断言才会转发
            - Path=${server.servlet.context-path}/log/** # 注意这里使用等号不是冒号
          filters:
            - StripPrefix=1 # 跳过指定路径 如请求/gateway/security/auth/login 转发到/security/auth/login
        - id: holemanage # 路由的唯一标识
          uri: lb://dubbo-provider-security # 如果断言成功，将要转发出去的地址
          predicates: # 断言，满足所有断言才会转发
            - Path=${server.servlet.context-path}/holemanage/** # 注意这里使用等号不是冒号
          filters:
            - StripPrefix=1 # 跳过指定路径 如请求/gateway/security/auth/logout 转发到/security/auth/logout
        - id: workorder # 路由的唯一标识
          uri: lb://dubbo-provider-security # 如果断言成功，将要转发出去的地址
          predicates: # 断言，满足所有断言才会转发
            - Path=${server.servlet.context-path}/workorder/** # 注意这里使用等号不是冒号
          filters:
            - StripPrefix=1 # 跳过指定路径 如请求/gateway/security/auth/logout 转发到/security/auth/logout

# dubbo服务配置
dubbo:
  application:
    id: ${spring.application.name}
    name: ${spring.application.name}
  protocol:
    name: dubbo # dubbo协议
    port: -1 # 协议端口 -1表示端口自增
  registry:
    address: spring-cloud://${spring.cloud.nacos.discovery.server-addr} # 挂在到springcloud注册中心
  consumer:
    check: false # 用于启动时是否检查服务提供方是否运行正常，如果不正常将不能启动调用方
    loadbalance: leastactive # 负载均衡策略  random|consistenthash|leastactive|roundrobin


custom:
  referer: http://localhost/,http://localhost:8080/,http://localhost:8080/
  url:
    regist: ${server.servlet.context-path}/auth/regist
    login: ${server.servlet.context-path}/auth/login
    logout: ${server.servlet.context-path}/auth/logout
    pass: ${custom.url.regist},${custom.url.login},${custom.url.logout},${server.servlet.context-path}/favicon,${server.servlet.context-path}/security/user/getBackendPublickey,${server.servlet.context-path}/security/user/getAesKey,${server.servlet.context-path}/kaptcha

  xss:
    whitelist:





# 日志文件配置
logging:
  config: classpath:logback-spring-pro.xml
