<template>
  <div id="topDiv">
    <vxe-modal height="99%" width="50%" position="center" resize :title="modalInfo.title" v-model="modalInfo.show" @close="handleModalClose()" showFooter>
      <vxe-form ref="myForm" title-width="120" :data="modalForm" :rules="rules" title-align="right" prevent-submit span="12">
        <!-- <div class="bigTitle">
          <span>基本信息</span>
          <el-divider content-position="left"></el-divider>
        </div> -->
        <vxe-form-item title="扫描工具品牌" field="tool_make">
          <vxe-select :disabled="!show" @change="selectToolMake" clearable placeholder="扫描工具品牌" v-model="modalForm.tool_make" :options="brandEnum"></vxe-select>
        </vxe-form-item>
        <vxe-form-item field="tool_name" title="扫描工具名称" v-if="selectShow.tool_name==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="扫描工具名称" v-model="modalForm.tool_name"></vxe-input>
        </vxe-form-item>
        <vxe-form-item title="协议" field="tool_protocol" v-if="selectShow.tool_protocol==null?true:false">
          <vxe-select :disabled="!show" clearable placeholder="协议" v-model="modalForm.tool_protocol" :options="agreementEnum"></vxe-select>
        </vxe-form-item>
        <vxe-form-item field="tool_address" title="地址" v-if="selectShow.ress==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="地址" v-model="modalForm.tool_address"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_port" title="端口" v-if="selectShow.port==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="端口" v-model="modalForm.tool_port"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_account_num" title="账号" v-if="selectShow.tool_account_num==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="账号" v-model="modalForm.tool_account_num"></vxe-input>
        </vxe-form-item>
        <!-- 动态隐藏 -->
        <vxe-form-item field="tool_password" title="密码" v-if="selectShow.tool_password==null?true:false">
          <el-input :disabled="!show" placeholder="密码" auto-complete="new-password" v-model="modalForm.tool_password" type="password"></el-input>
          <!-- <vxe-input
            clearable
            placeholder="密码"
            v-model="modalForm.tool_password"
            show-password
          ></vxe-input> -->
        </vxe-form-item>
        <vxe-form-item field="tool_apikey" title="Api Key" v-if="selectShow.tool_apikey==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="Api Key" v-model="modalForm.tool_apikey"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_target_num" title="单个主机任务最大目标数" title-width="160px" v-if="selectShow.tool_target_num==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="单个主机任务最大目标数量" v-model="modalForm.tool_target_num"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_concurrent_num" title="扫描工具并发任务数量" title-width="160px" v-if="selectShow.tool_concurrent_num==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="扫描工具并发任务数量" v-model="modalForm.tool_concurrent_num"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_max_task" title="最大任务数" title-width="160px" v-if="selectShow.tool_max_task==null?true:false">
          <vxe-input clearable :disabled="!show" placeholder="最大任务数" v-model="modalForm.tool_max_task"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="tool_remark" title="备注" v-if="selectShow.tool_remark==null?true:false">
          <vxe-textarea :disabled="!show" v-model="modalForm.tool_remark" placeholder="备注" clearable></vxe-textarea>
        </vxe-form-item>
        <vxe-form-item title="" field="" class="lastItem"> </vxe-form-item>
        <vxe-form-item title="" field="" class="lastItem"> </vxe-form-item>
        <vxe-form-item title="" field="" class="lastItem"> </vxe-form-item>
        <vxe-form-item title="" field="" class="lastItem"> </vxe-form-item>
      </vxe-form>

      <template v-slot:footer>
        <el-button type="" @click="handleDialogCancel" v-if="show">取消</el-button>
        <el-button type="primary" :loading="loading" v-if="show" @click="submitForm">确定</el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import { saveToolBasic } from "@/api/testManage";
import { clearProperty, copyProperty } from "@/utils/guozw-core.js";
export default {
  name: "ToolsDialog",
  components: {},
  props: {
    // 品牌枚举
    brandEnum: {
      type: Array,
      default: () => []
    },
    // 协议枚举
    agreementEnum: {
      type: Array,
      default: () => []
    },
    selectEnum: {
      type: Array,
      default: () => []
    },
    modalInfo: {
      type: Object,
      default: () => { }
    },
    formDefaultData: {
      type: Object,
      default: () => { }
    }
  },
  watch: {
    immediate: true,
    "modalInfo.type": {
      handler(n) {
        this.selectShow = {};
        this.passwordkey = null;
        if (n === "edit") {
          this.modalForm = this.modalInfo.data;
          this.passwordkey = JSON.parse(JSON.stringify(this.modalInfo.data.tool_password))
          const rows = {};
          rows.value = this.modalForm.tool_make;
          this.selectToolMake(rows);
        }
        if (n === "view") {
          this.modalForm = this.modalInfo.data;
          const rows = {};
          rows.value = this.modalForm.tool_make;
          this.selectToolMake(rows);
          this.show = false;
        } else {
          this.show = true;
        }
      }
    }

    // formDefaultData(newVal, oldVal) {
    //   copyProperty(newVal, this.modalForm);
    //   //   initData()
    // }
  },
  computed: {
    handleDisabled() {
      return this.modalInfo.type === "edit";
    }
  },
  data() {
    return {
      loading: false,
      modalForm: {
        tool_make: "1",
        tool_name: "",
        tool_protocol: "",
        tool_address: "",
        tool_apikey: "",
        tool_port: "443",
        tool_account_num: "",
        tool_password: "",
        tool_target_num: "200",
        tool_concurrent_num: "5",
        tool_remark: "",
        tool_max_task: "0"
      },
      passwordkey: null,
      show: true,
      selectShow: {},
      rules: {
        tool_make: [{ required: true, message: "必填字段" }],
        tool_name: [{ required: true, message: "必填字段" }],
        tool_protocol: [{ required: true, message: "必填字段" }],
        tool_address: [{ required: true, message: "必填字段" }],
        tool_port: [{ required: true, message: "必填字段" }],
        tool_account_num: [{ required: true, message: "必填字段" }],
        tool_password: [{ required: true, message: "必填字段" }],
        tool_target_num: [{ required: true, message: "必填字段" }],
        tool_concurrent_num: [{ required: true, message: "必填字段" }],
        tool_apikey: [{ required: true, message: "必填字段" }],
        tool_max_task: [{ required: true, message: "必填字段" }],
      },
      // 关闭弹窗的时候是否需要刷新表格
      isRefreshTable: false
    };
  },
  created() {
  },
  mounted() { },
  methods: {
    resetForm() {
      this.modalInfo.show = false;
      this.modalInfo.type = "";
      this.modalInfo.data = {};
      this.$refs.myForm.clearValidate();
      this.modalForm = {
        tool_make: "",
        tool_name: "",
        tool_protocol: "",
        tool_address: "",
        tool_port: "443",
        tool_account_num: "",
        tool_password: "",
        tool_apikey: "",
        tool_target_num: "200",
        tool_concurrent_num: "5",
        tool_remark: ""
      };
    },
    selectToolMake(rows) {
      this.selectShow = {};
      this.selectEnum.forEach(item => {
        if (rows.value == item.value) {
          this.selectShow = JSON.parse(item.label);
        }
      })
    },
    handleModalClose() {
      this.resetForm();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
    },
    handleDialogCancel() {
      this.resetForm();
      if (this.isRefreshTable) {
        this.$emit("refreshTable");
      }
    },

    getQueryCondition() {
      if (this.selectShow.tool_name != null) {
        this.modalForm.tool_name = null;
      }
      if (this.selectShow.tool_protocol != null) {
        this.modalForm.tool_protocol = null;
      }
      if (this.selectShow.tool_address != null) {
        this.modalForm.tool_address = null;
      }
      if (this.selectShow.tool_port != null) {
        this.modalForm.tool_port = null;
      }
      if (this.selectShow.tool_account_num != null) {
        this.modalForm.tool_account_num = null;
      }
      if (this.selectShow.tool_password != null) {
        this.modalForm.tool_password = null;
      }
      if (this.selectShow.tool_target_num != null) {
        this.modalForm.tool_target_num = null;
      }
      if (this.selectShow.tool_concurrent_num != null) {
        this.modalForm.tool_concurrent_num = null;
      }
      if (this.selectShow.tool_remark != null) {
        this.modalForm.tool_remark = null;
      }
      if (this.selectShow.tool_apikey != null) {
        this.modalForm.tool_apikey = null;
      }
      this.modalForm.passwordKey = null;
      if (this.passwordkey != null && this.passwordkey == this.modalForm.tool_password) {
        this.modalForm.passwordKey = this.passwordkey;
      }


      let queryCondition = JSON.parse(JSON.stringify(this.modalForm));
      !queryCondition.tool_basic_id
        ? this.$set(
          queryCondition,
          "tool_basic_id",
          this.modalInfo.data.tool_basic_id
        )
        : null;
      return queryCondition;
    },
    // 提交表单
    submitForm() {
      this.$refs.myForm
        .validate()
        .then(() => {
          this.loading = true;
          const queryCondition = this.getQueryCondition();
          saveToolBasic(queryCondition)
            .then(response => {
              if (response){
                this.$XModal.message({
                  message: this.modalForm.tool_basic_id
                    ? "修改成功"
                    : "新增成功",
                  status: "success"
                });
                this.$emit("refreshTable");
                this.modalInfo.show = false;

                this.modalInfo.type = "";
                this.modalInfo.show = false;
              }
            })
            .finally(() => {
              this.loading = false;
              clearProperty(this.modalForm);
            });
        })
        .catch(err => {
          console.log(err);
          return false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
#topDiv {
  .vxe-form /deep/ .vxe-form--item-inner {
    min-height: 30px !important;
  }
  .bigTitle {
    margin-top: 10px;
    span {
      font-size: 15px;
      font-weight: bolder;
    }
  }

  .lastItem {
    margin-bottom: 10px !important;
  }
  .el-divider--horizontal {
    margin: 5px 0;
  }
  .tip {
    color: red;
    font-size: 10px;
  }

  .my-dropdown1 {
    height: 200px;
    overflow: auto;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
  }
  .list-item1:hover {
    background-color: #f5f7fa;
  }
  .addRow {
    float: right;
    position: relative;
    top: -5px;
  }
  .delRow {
    float: right;
    margin-left: 5px;
    position: relative;
    top: -5px;
  }
}
</style>
