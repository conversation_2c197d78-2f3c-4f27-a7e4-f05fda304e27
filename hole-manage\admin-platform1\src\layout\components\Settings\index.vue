<template>
  <div class="drawer-container">
    <div>
      <h3 class="drawer-title">Page style setting</h3>

      <div class="drawer-item drawer-item-menu-mode">
        <span>导航模式</span>
        <div class="menu-container">
          <el-tooltip content="侧边菜单布局" placement="top">
            <div class="mode-item left-meun" @click="handleChangeMenuMode('vertical')">
              <div class="menu-wrap"></div>
              <div class="right-wrap">
                <div class="top-wrap"></div>
                <div class="con-wrap"></div>
                <i class="el-icon-check icon" v-if="menuStyle === 'vertical'"></i>
              </div>
            </div>
          </el-tooltip>

          <el-tooltip content="顶部菜单布局" placement="top">
            <div class="mode-item top-meun" @click="handleChangeMenuMode('horizontal')">
              <div class="menu-wrap"></div>
              <i class="el-icon-check icon" v-if="menuStyle === 'horizontal'"></i>
            </div>
          </el-tooltip>

          <el-tooltip content="混合菜单布局" placement="top">
            <div class="mode-item hybrid-meun" @click="handleChangeMenuMode('hybrid')">
              <div class="top-wrap"></div>
              <div class="box">
                <div class="menu-wrap"></div>
                <div class="con-wrap"></div>
              </div>
              <i class="el-icon-check icon" v-if="menuStyle === 'hybrid'"></i>
            </div>
          </el-tooltip>
        </div>
      </div>

      <div class="drawer-item" v-if="menuStyle === 'hybrid'">
        <span>是否显示标签栏和面包屑</span>
        <el-switch v-model="showNavHamburger_IN_hybrid" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>切换菜单栏背景色</span>
        <theme-picker style="float: right; height: 26px; margin: -3px 8px 0 0" @change="themeChange1" />
      </div>
      <div class="drawer-item">
        <span>切换已选中菜单背景色</span>
        <theme-picker style="float: right; height: 26px; margin: -3px 8px 0 0" @change="themeChange2" />
      </div>

      <!-- <div class="drawer-item">
                <span>Theme Color</span>
                <theme-picker style="float: right;height: 26px;margin: -3px 8px 0 0;" @change="themeChange" />
            </div> -->

      <div class="drawer-item">
        <span>Open Tags-View</span>
        <el-switch v-model="tagsView" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>Fixed Header</span>
        <el-switch v-model="fixedHeader" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>Sidebar Logo</span>
        <el-switch v-model="sidebarLogo" class="drawer-switch" />
      </div>
    </div>
  </div>
</template>

<script>
import ThemePicker from '@/components/ThemePicker'
import {
  mapGetters
} from 'vuex'


export default {
  components: {
    ThemePicker
  },
  data () {
    return {

    }
  },
  computed: {
    fixedHeader: {
      get () {
        return this.$store.state.settings.fixedHeader
      },
      set (val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'fixedHeader',
          value: val
        })
      }
    },
    tagsView: {
      get () {
        return this.$store.state.settings.tagsView
      },
      set (val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value: val
        })
      }
    },
    sidebarLogo: {
      get () {
        return this.$store.state.settings.sidebarLogo
      },
      set (val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'sidebarLogo',
          value: val
        })
      }
    },
    ...mapGetters(['menuStyle']),
    showNavHamburger_IN_hybrid: {
      get () {
        return this.$store.state.settings.showNavHamburger_IN_hybrid;
      },
      set (val) {
        this.changeShowNavHamburger(val)

      }
    }
  },
  methods: {
    themeChange (val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value: val
      })
    },
    handleChangeMenuMode (type) {
      if (this.menuStyle === type) return
      this.$store.dispatch('settings/toggleMeunStyle', type)
      this.$store.dispatch('app/toggleSideBar')
      this.$store.dispatch('app/toggleDevice', "desktop")

      // 切换为混合模式时，默认显示导航栏和面包屑
      if (type === 'hybrid') this.changeShowNavHamburger(true)
    },
    themeChange1 (val) {
      this.$store.dispatch('settings/changeMenuOptColor', {
        key: 'menuBg',
        value: val
      })
      localStorage.setItem("menuBg", val);
    },
    themeChange2 (val) {
      this.$store.dispatch('settings/changeMenuOptColor', {
        key: 'activeBgColor',
        value: val
      })
      localStorage.setItem("activeBgColor", val);
    },
    changeShowNavHamburger (val) {
      this.$store.dispatch("settings/changeShowNavHamburger", val);
      localStorage.setItem("showNavHamburger_IN_hybrid", val);
    }
  }
}

</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 24px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;

  .drawer-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
  }

  .drawer-item {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    padding: 12px 0;
  }

  .drawer-switch {
    float: right;
  }

  // 导航模式
  .drawer-item-menu-mode {
    .menu-container {
      margin-top: 10px;
      display: flex;
      align-items: center;

      .el-tooltip {
        &:nth-of-type(2) {
          margin: 0 16px;
        }
      }

      .mode-item {
        position: relative;
        width: 44px;
        height: 36px;
        overflow: hidden;
        background-color: #f0f2f5;
        border-radius: 4px;
        box-shadow: 0 1px 2.5px 0 rgba(0, 0, 0, 0.18);
        cursor: pointer;

        .icon {
          position: absolute;
          right: 6px;
          bottom: 4px;
          color: #000;
          font-weight: bold;
        }
      }

      .left-meun {
        display: flex;
        align-items: center;

        .menu-wrap {
          width: 33%;
          height: 100%;
          background: #001529;
        }

        .right-wrap {
          flex: 1;
          height: 100%;

          .top-wrap {
            height: 25%;
            background: #ddd;
          }
        }
      }

      .top-meun {
        .menu-wrap {
          height: 25%;
          background: #001529;
        }
      }

      .hybrid-meun {
        display: flex;
        flex-direction: column;

        .top-wrap {
          height: 25%;
          background: #001529;
        }

        .box {
          flex: 1;
          display: flex;

          .menu-wrap {
            width: 33%;
            height: 100%;
            background: #ddd;
          }
        }
      }
    }
  }
}
</style>
