package com.guozw.security.facade.config;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guozw.security.entity.config.ConfigEmailTemplate;
import com.guozw.security.vo.config.ConfigEmailTemplateVO;

import java.util.List;

public interface ConfigEmailTemplateService extends IService<ConfigEmailTemplate> {

    Boolean saveConfigEmailTemplate(ConfigEmailTemplateVO vo);

    Boolean deleteBatchConfigEmailTemplate(List<String> idList);

    Page<ConfigEmailTemplate> pageEmailTemplate(ConfigEmailTemplateVO vo);

    List<ConfigEmailTemplate> listEmailTemplate(ConfigEmailTemplateVO vo);

}
