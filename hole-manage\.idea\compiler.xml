<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="guozw-provider-security-holemanage" />
        <module name="guozw-common-gateway-holemanage" />
        <module name="guozw-facade-log" />
        <module name="guozw-facade-security" />
        <module name="guozw-common-config" />
        <module name="guozw-provider-log-holemanage" />
        <module name="guozw-common-core" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="guozw-common-gateway-holemanage" options="-extdirs lib -verbose -bootclasspath &quot;C:/Program Files/Java/jdk1.8.0_351/jre\lib\rt.jar;C:/Program Files/Java/jdk1.8.0_351/jre\lib\jce.jar&quot;" />
      <module name="guozw-provider-log-holemanage" options="-extdirs lib -verbose -bootclasspath &quot;C:/Program Files/Java/jdk1.8.0_351/jre\lib\rt.jar;C:/Program Files/Java/jdk1.8.0_351/jre\lib\jce.jar&quot;" />
      <module name="guozw-provider-security-holemanage" options="-extdirs lib -verbose -bootclasspath &quot;C:/Program Files/Java/jdk1.8.0_351/jre\lib\rt.jar;C:/Program Files/Java/jdk1.8.0_351/jre\lib\jce.jar&quot;" />
    </option>
  </component>
</project>