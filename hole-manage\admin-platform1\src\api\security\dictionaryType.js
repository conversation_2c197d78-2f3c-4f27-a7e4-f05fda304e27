import request from '@/utils/request'
import qs from 'qs'
export function saveDictionaryType(data) {
    return request({
        url: '/security/dictionarytype/saveDictionaryType',
        method: 'post',
        params: data
    })
}
export function modifyDictionaryType(data) {
    return request({
        url: '/security/dictionarytype/modifyDictionaryType',
        method: 'post',
        params: data
    })
}
export function deleteDictionaryType(data) {
    return request({
        url: '/security/dictionarytype/deleteDictionaryType',
        method: 'post',
        params: data
    })
}
export function listDictionaryTypes(data) {
    return request({
        url: '/security/dictionarytype/listDictionaryTypes',
        method: 'post',
        data: qs.stringify(data)
    })
}
