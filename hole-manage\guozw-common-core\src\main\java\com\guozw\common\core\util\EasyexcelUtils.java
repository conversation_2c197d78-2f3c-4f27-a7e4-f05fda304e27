package com.guozw.common.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

@Slf4j
public class EasyexcelUtils {

    /**
     * 单元格添加下拉菜单（不限制菜单项个数）
     *
     * @param workbook
     * @param sheet
     * @param items    下拉列表数据
     * @param firstRow
     * @param lastRow
     * @param column   待添加下拉菜单的单元格所在的列（从0开始）
     */
    public static void addDropdownList(Workbook workbook, Sheet sheet, List<String> items, int firstRow, int lastRow, int column) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        //必须以字母开头，最长为31位
        String hiddenSheetName = "a" + UUID.randomUUID().toString().replace("-", "").substring(1, 31);
        //excel中的"名称"，用于标记隐藏sheet中的用作菜单下拉项的所有单元格
        String formulaId = "form" + UUID.randomUUID().toString().replace("-", "");
        //用于存储 下拉菜单数据
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);
        //设置存储下拉菜单项的sheet页不显示
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);

        Row row = null;
        Cell cell = null;
        //隐藏sheet中添加菜单数据
        for (int i = 0; i < items.size(); i++) {
            row = hiddenSheet.createRow(i);
            //隐藏表的数据列必须和添加下拉菜单的列序号相同，否则不能显示下拉菜单
            cell = row.createCell(column);
            cell.setCellValue(items.get(i));
        }
        //创建"名称"标签，用于链接
        Name namedCell = workbook.createName();
        namedCell.setNameName(formulaId);
        namedCell.setRefersToFormula(hiddenSheetName + "!A$1:A$" + items.size());
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(formulaId);

        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, column, column);
        //添加菜单(将单元格与"名称"建立关联)
        DataValidation validation = helper.createValidation(constraint, addressList);
        sheet.addValidationData(validation);
    }
}
