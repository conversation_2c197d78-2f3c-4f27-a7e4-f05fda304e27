package com.guozw.security.dto;

import com.guozw.security.entity.SysTask;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SysTaskDTO extends SysTask implements Serializable {
    private static final long serialVersionUID = -1695370169982585579L;

    /**
     * 任务状态 0-停止 1-启动
     */
    private String task_status;
    /**
     * 任务编码列表
     */
    private List<String> task_id_list;
}
