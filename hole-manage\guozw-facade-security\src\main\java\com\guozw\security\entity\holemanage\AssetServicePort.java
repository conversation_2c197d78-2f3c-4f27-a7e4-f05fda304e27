package com.guozw.security.entity.holemanage;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guozw.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 资产管理-服务端口表
 * <AUTHOR>
 * @date 2020/5/2
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
@TableName(value = "asset_service_port")
public class AssetServicePort extends BaseEntity<AssetServicePort> implements Serializable {

    private static final long serialVersionUID = -6866651620553335346L;
    /**
     * 主键
     */
    @TableId
    private String port_id;
    /**
     * 端口号
     */
    private String port_num;
    /**
     * 端口协议
     */
    private String port_protocol;
    /**
     * 端口服务
     */
    private String port_service;
    /**
     * 备注信息
     */
    private String port_remark;
    /**
     * 关联类型 1-IP资产  2-网站资产
     */
    private String relation_type;
    /**
     * 关联表主键 IP资产表主键或网站资产表主键
     */
    private String relation_id;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

}
