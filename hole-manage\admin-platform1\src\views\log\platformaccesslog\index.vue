<template>
  <el-container>
    <el-main>
      <!-- <el-tabs ref="tabs" v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="未读" name="0"></el-tab-pane>
        <el-tab-pane label="已读" name="1"></el-tab-pane>
      </el-tabs> -->
      <!-- region 查询条件表单-->
      <vxe-form ref="queryConditionForm" title-width="100" title-align="right" span="8" :data="queryParams" @submit="handleQueryConditionFormSubmit" @toggle-collapse="handleQueryConditionFormToggleCollapse">
        <vxe-form-item field="accessmodule" title="访问模块">
          <vxe-input clearable placeholder="访问模块" v-model="queryParams.accessmodule"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="accesstype" title="访问类型">
          <vxe-input clearable placeholder="访问类型" v-model="queryParams.accesstype"></vxe-input>
        </vxe-form-item>

        <vxe-form-item align="center" collapse-node>
          <vxe-button type="submit" status="primary" icon="fa fa-search">查询</vxe-button>
          <vxe-button type="reset" icon="fa fa-refresh">重置</vxe-button>
        </vxe-form-item>
        <vxe-form-item field="requesturi" title="请求uri" folding>
          <vxe-input clearable placeholder="请求uri" v-model="queryParams.requesturi"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="requestmethod" title="请求方法名" folding>
          <vxe-input clearable placeholder="请求方法名" v-model="queryParams.requestmethod"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="username" title="用户名称" folding>
          <vxe-input clearable placeholder="用户名称" v-model="queryParams.username"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="departmentname" title="部门名称" folding>
          <vxe-input clearable placeholder="部门名称" v-model="queryParams.departmentname"></vxe-input>
        </vxe-form-item>
        <vxe-form-item field="startDate" title="创建时间开始" folding>
          <el-date-picker v-model="queryParams.startDate" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>
        <vxe-form-item field="endDate" title="创建时间结束"  folding>
          <el-date-picker v-model="queryParams.endDate" @change="endDateChange" type="date" value-format="yyyy-MM-dd" range-separator="-" placeholder="选择日期">
          </el-date-picker>
        </vxe-form-item>
      </vxe-form>
      <!-- endregion-->

      <!-- region 表格工具栏 -->
      <vxe-toolbar ref="toolbar" :refresh="{ query: loadTable }" custom>
        <template v-slot:buttons>
          <!-- <el-button type="primary" icon="fa fa-plus" @click="handleAdd">
            新增</el-button> -->
          <!-- <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleImport" v-btnpermission="'hole:holelibrary:import'">导入文件</el-button>
          </el-col> -->
          <!-- 
                    <el-button
                        type="primary"
                        icon="fa fa-edit"
                        @click="handleBatchEdit"
                    >
                        批量修改</el-button
                    >
                    <el-button
                        type="danger"
                        icon="fa fa-trash"
                        @click="handleBatchDelete"
                    >
                        批量删除</el-button
                    >-->
        </template>
      </vxe-toolbar>
      <!-- endregion -->

      <!-- region 表格 -->
      <div v-bind:style="{ height: tableHeight + 'px' }">
        <vxe-table id="myTable" ref="myTable" v-loading="loading" element-loading-text="拼命加载中" border auto-resize resizable height="auto" show-overflow :sort-config="{ trigger: 'cell', remote: true }" @sort-change="sortChange" :custom-config="{ storage: true }" :data="page.records" :checkbox-config="{ trigger: 'row' }">
          <!-- <vxe-table-column type="checkbox" width="50" fixed="left"></vxe-table-column> -->
          <vxe-table-column title="序号" type="seq" width="60" fixed="left"></vxe-table-column>
          <!-- <vxe-table-column field="workorder_warn_name" title="消息名称">
            <template slot-scope="scope">
              <el-link type="primary" @click="handleView(scope.row)">{{ scope.row.workorder_warn_name }}</el-link>
            </template>
          </vxe-table-column> -->
          <vxe-table-column field="accessmodule" title="访问模块"></vxe-table-column>
          <vxe-table-column field="accesstype" title="访问类型"></vxe-table-column>
          <vxe-table-column field="accessdesc" title="访问描述"></vxe-table-column>
          <vxe-table-column field="clientip" title="客户端ip"></vxe-table-column>
          <vxe-table-column field="requesturi" title="请求uri"></vxe-table-column>
          <vxe-table-column field="requestmethod" title="请求方法名"></vxe-table-column>
          <!-- <vxe-table-column field="requestparams" title="请求参数"></vxe-table-column> -->
          <vxe-table-column field="requeststartdate" title="请求开始时间"></vxe-table-column>
          <vxe-table-column field="requestenddate" title="请求结束时间"></vxe-table-column>
          <vxe-table-column field="username" title="用户名称"></vxe-table-column>
          <vxe-table-column field="departmentname" title="部门名称"></vxe-table-column>
          <vxe-table-column field="createdate" title="创建时间"></vxe-table-column>
          <!-- <vxe-table-column field="requestmethod" title="请求方法名">
            <template slot-scope="scope">
              <el-link type="primary" v-if="scope.row.holePatchList !=null" @click="gowarn(scope.row)">补丁下载</el-link>
            </template>
          </vxe-table-column> -->
          <vxe-table-column field="" title="操作" fixed="right" width="">
            <template slot-scope="scope">
              <!-- <i class="el-icon-edit" style="font-size: 18px; color: #409EFF" @click="handleUpdate(scope.row)"></i> -->
              <i class="el-icon-view" style="font-size: 18px; color: #409EFF" @click="handleView(scope.row)"></i>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
      <!-- endregion-->

      <vxe-modal ref="resourceFormModal" height="99%" width="700" position="center" resize :title="resourceFormModalTitle" :showFooter="!disabled">
        <vxe-form ref="resourceForm" title-align="right" title-width="100" :data="resourceInfo" :rules="resourceFormRules" prevent-submit>
          <vxe-form-item title="访问描述" field="accessdesc" span="24">
            <template>
              <el-input type="textarea" :rows="3" clearable :disabled="disabled" v-model="resourceInfo.accessdesc">
              </el-input>
            </template>
            <!-- <template>
              <vxe-input v-model="resourceInfo.hole_library_name" readonly :disabled="disabled"></vxe-input>
            </template> -->
          </vxe-form-item>
          <vxe-form-item title="访问模块" field="accessmodule" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.accessmodule" placeholder="请输入访问模块" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="访问类型" field="accesstype" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.accesstype" placeholder="请输入访问类型" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="客户端ip" field="clientip" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.clientip" placeholder="请输入客户端ip" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求uri" field="requesturi" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requesturi" placeholder="请输入请求uri" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求方法名" field="requestmethod" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requestmethod" placeholder="请输入请求方法名" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求参数" field="requestparams" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requestparams" placeholder="请输入请求参数" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求开始时间" field="requeststartdate" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requeststartdate" placeholder="请输入请求开始时间" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求结束时间" field="requestenddate" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requestenddate" placeholder="请输入请求结束时间" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="请求消耗时间（毫秒）" field="requestcostdate" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.requestcostdate" placeholder="请输入请求消耗时间（毫秒）" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <!-- <vxe-form-item title="响应参数" field="resultparams" span="24">
            <template>
              <el-input type="textarea" :rows="3" clearable :disabled="disabled" v-model="resourceInfo.resultparams">
              </el-input>
              </template>
          </vxe-form-item>
          <vxe-form-item title="异常名称" field="exceptionname" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.exceptionname" placeholder="请输入异常名称" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item> -->
          <vxe-form-item title="用户名称" field="username" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.username" placeholder="请输入用户名称" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="部门名称" field="departmentname" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.departmentname" placeholder="请输入部门名称" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item title="创建时间" field="createdate" span="24">
            <template v-slot="scope">
              <vxe-input v-model="resourceInfo.createdate" placeholder="请输入创建时间" clearable :disabled="disabled" @input="$refs.resourceForm.updateStatus(scope)"></vxe-input>
            </template>
          </vxe-form-item>
          <!-- <vxe-form-item title="异常信息" field="exceptioninfo" span="24">
            <template>
              <el-input type="textarea" :rows="3" clearable :disabled="disabled" v-model="resourceInfo.exceptioninfo">
              </el-input>
            </template>
          </vxe-form-item> -->
        </vxe-form>
        <template v-slot:footer>
          <vxe-button type="button" content="取消" @click="$refs.resourceFormModal.close()"></vxe-button>
          <vxe-button type="button" status="primary" content="确定" @click="handleResourceFormModalConfirm"></vxe-button>
        </template>
      </vxe-modal>

      <el-dialog :title="resourceFormModalTitle" :visible.sync="handOPen" width="500px" append-to-body>
        <el-form ref="postOrderForm" :model="queryParams" label-width="80px">
          <el-upload ref="upload" :limit="1" accept=".xml" :headers="upload.headers" :data="uploadData" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <div class="el-upload__tip" slot="tip">

            </div>

            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>

            <!-- <div class="el-upload__tip" slot="tip">
            <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板</el-link>
          </div> -->
            <div class="el-upload__tip" style="color: red" slot="tip">
              提示：仅允许导入“xml”格式文件！
            </div>
          </el-upload>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <!-- region 分页-->
      <vxe-pager ref="pager" :current-page="queryParams.pagenumber" :page-size="queryParams.pagesize" :total="page.total" @page-change="handlePageChange">
      </vxe-pager>
      <!-- endregion -->

      <!-- <AssetWarnEdit :modalInfo="editModalInfo" :formDefaultData="formDefaultData" @refreshTable="loadTable" :deviceTypeEnum="deviceTypeEnum" :systemTypeEnum="systemTypeEnum" :systemLoadEnum="systemLoadEnum" :protectionEnum="protectionEnum" :importanceEnum="importanceEnum" :secretEnum="secretEnum" :assetStatusEnum="assetStatusEnum"></AssetWarnEdit> -->
      <!-- <AssetIpBatchEdit :modalInfo="batchEditModalInfo" :formDefaultData="batchEdit_formDefaultData" @refreshTable="loadTable" :deviceTypeEnum="deviceTypeEnum" :protectionEnum="protectionEnum" :importanceEnum="importanceEnum" :secretEnum="secretEnum"></AssetIpBatchEdit> -->
    </el-main>
  </el-container>
</template>

<script>
import {
  pagePlatformAccessLog,
  onePlatformAccessLog
} from "@/api/log/platformaccesslog";
import { pageHoleLibrary, downloadHoleLibraryFile } from "@/api/holemanage/asset";
import { pageUsers } from "@/api/security/user";
import DepartmentTree from "@/views/security/department/components/DepartmentTree";
import AssetIpBatchEdit from "@/views/holemanage/asset/ip/components/AssetIpBatchEdit";
import { mapGetters } from "vuex";
import { getToken } from '@/utils/auth';

export default {
  name: "index",
  components: { DepartmentTree, AssetIpBatchEdit },
  data() {
    return {
      activeName: "0",
      tabsHeight: 0,
      handOPen: false,
      uploadData: {},
      //日期
        startDate: null,
        endDate: null,
      // 导入参数
      upload: {
        // 是否显示弹出层（文件导入）
        postOrder: {},
        // 弹出层标题（文件导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/holemanage/holeLibrary/importData',
      },
      queryConditionFormHeight: 0, //查询条件表单高度
      tableHeight: 0, // 表格高度
      userList: [],
      loading: true,
      disabled: false, // 控制查看操作 禁用表单元素 不显示弹框的底部按钮
      page: {
        total: 0,
        records: [],
      },
      resourceInfo: {
      },
      resourceFormRules: {

      },
      resourceFormModalTitle: "详细信息",
      // 查询条件
      queryParams: {
        pagenumber: 1,
        pagesize: 10,
        hole_library_id: null,
        hole_library_name: null,
        hole_library_level: null,
        hole_library_type: null,
        hole_library_cve: null,
        hole_library_vkb: null,
        hole_library_cwe: null,
        hole_library_cnnvd: null,
        hole_library_cvss: null,
        hole_library_attack_route: null,
        hole_library_attack_diff: null,
        hole_library_attack_auth: null,
        hole_library_privacy: null,
        hole_library_integrity: null,
        hole_library_usability: null,
        hole_library_remark: null,
        hole_library_repair: null,
        hole_library_referenceinfo: null,
      },

      // 设备类型
      deviceTypeEnum: [],
      // 系统类型
      systemTypeEnum: [],
      // 等级保护
      protectionEnum: [],
      // 重要程度
      importanceEnum: [],
      // 涉密状态
      secretEnum: [],
      // 资产状态枚举
      assetStatusEnum: [],
      // 系统负载枚举
      systemLoadEnum: [],

      // 新增 修改 弹窗的表单默认数据
      formDefaultData: {},
      // 新增 修改 弹窗的显示关闭以及标题等信息
      editModalInfo: {},
      batchEditModalInfo: {},
      batchEdit_formDefaultData: {},
    };
  },
  created() {
    this.initData();
    this.loadTable();
    this.loadUserTable();
  },

  mounted() {
    this.setQueryConditionFormHeight();
    this.setTableHeight();
  },
  methods: {
    initData() {
    },
    /**导入 */
    handleImport() {
      this.handOPen = true;
      this.resourceFormModalTitle = '导入文件';
    },
    handleAdd() { },
    handleUpdate() { },
    cancel() {
      this.handOPen = false;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      // this.upload.open = false;
      this.upload.isUploading = false;
      this.handOPen = false;
      this.$refs.upload.clearFiles();
      this.$alert("上传成功", '导入结果', { dangerouslyUseHTMLString: true });
      this.loadTable();
    },
    // 提交上传文件
    submitFileForm() {

      this.$refs.upload.submit();
      this.uploadData = {};
      // this.handOPen = false;
    },
    // 跳转地址
    gowarn(row) {
      this.handleWarnUserStatus(row);
      // const urlJson = JSON.parse(row.workorder_warn_url);
      this.$router.push({ path: '/holemanage/holePatch/index', query: { 'hole_patch_id': row.hole_patch_id } });
    },
    goDownload(rows) {
      console.log(rows)
      const row = {};
      row.hole_patch_id = rows.hole_patch_id;
      console.log(row)
      // row.file_id = rows.file_id;
      // row.file_real_name = rows.file_real_name;
      // row.file_storage_name = rows.file_storage_name;
      downloadHoleLibraryFile(row).then((res) => {

        let blob = new Blob([res]);
        let objectUrl = URL.createObjectURL(blob);
        let link = document.createElement("a");
        link.href = objectUrl;
        link.download = `${row.file_real_name}-模板文件.${row.file_storage_name.split(".")[1]
          }`;
        link.click();
      });
    },
    // 处理tabs点击
    handleTabClick(tab, event) {
      this.queryParams.is_read = this.activeName;
      this.loadTable();
    },
    // 加载用户表格
    loadUserTable() {
      // const query = {};
      // query.pagesize = 1000;
      // query.pagenumber = 1;
      // pageUsers(query)
      //   .then((response) => {
      //     this.userList = response.data.records;
      //   })
      //   .catch((error) => { });
    },
    handleWarnUserStatus(row) {


    },
    // 处理查看
    handleView(row) {
      console.log(row)
      this.$refs.resourceFormModal.open()
      this.disabled = true;
      this.resourceInfo = row;
      // this.formDefaultData = row;
      // this.editModalInfo = {
      //   title: "查看资产",
      //   show: true,
      //   isActionView: true
      // };
    },
    changeWorkorder_warn_from(row) {
    },
    remoteMethodWarnFrom(query) {
      if (query !== "") {
        // this.loading = true;
        // listCustomers(this.queryParams).then(response => {
        //   this.userList = response.rows;
        // });
      } else {
        // this.userList = [];
      }
    },
    // 设置查询条件表单的高度
    setQueryConditionFormHeight() {
      this.queryConditionFormHeight =
        this.$refs.queryConditionForm.$el.offsetHeight;
    },
    // 设置表格的高度
    setTableHeight() {
      // console.log("extraHeight=", this.extraHeight);
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.queryConditionFormHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight;

      // console.log("tableHeight=", this.tableHeight);
    },
    // 处理查询条件表单折叠按钮折叠
    handleQueryConditionFormToggleCollapse(collapse, data) {
      this.$nextTick(function () {
        this.queryConditionFormHeight =
          this.$refs.queryConditionForm.$el.offsetHeight;
      });
    },
    // 处理查询条件表单提交
    handleQueryConditionFormSubmit({ data }) {
      this.queryParams.pagenumber = 1;
      this.loadTable();
    },
    endDateChange(rows){
      this.$forceUpdate();
    },
    // 处理页码变化
    handlePageChange({ currentPage, pageSize }) {
      this.queryParams.pagenumber = currentPage;
      this.queryParams.pagesize = pageSize;
      this.loadTable();
    },

    /* 字段排序 */
    sortChange(e) {
      console.log("sortChange", e);
      const { field, order } = e;

      this.queryParams.sort_field = field;
      this.queryParams.sort_order = order;

      this.loadTable();
    },

    // 查询条件
    getQueryCondition() {

      // this.queryParams.params = {};
      // this.queryParams.params['beginCreatedTime'] = this.queryParams.startDate;
      // this.queryParams.params['endCreatedTime'] = this.queryParams.endDate;
      this.queryParams.beginCreatedTime=null;
      this.queryParams.endCreatedTime=null;
      this.queryParams.beginCreatedTime = this.queryParams.startDate;
      this.queryParams.endCreatedTime = this.queryParams.endDate;
      let quryCondition = JSON.parse(JSON.stringify(this.queryParams));

      return quryCondition;
    },
    // 加载表格
    loadTable() {
      this.loading = true;
      pagePlatformAccessLog(this.getQueryCondition())
        .then((response) => {
          
          console.log('----------------------------------------------')
          this.page.records = response.data.records;
          console.log(this.page.records)
          this.page.total = response.data.total;
          this.loading = false;
        })
        .catch((error) => {
           console.log('================', error)
          this.loading = false;
        });
    },

  },
  computed: {
    ...mapGetters(["extraHeight"]),
  },
  watch: {
    extraHeight() {
      this.setTableHeight();
    },
    // 监听查询条件表单高度变化
    queryConditionFormHeight(val) {
      this.tableHeight =
        window.innerHeight -
        this.extraHeight -
        this.$refs.toolbar.$el.offsetHeight -
        this.$refs.pager.$el.offsetHeight -
        this.tabsHeight -
        val;
    },
  },
};
</script>
<style scoped>
i {
  cursor: pointer;
  margin-right: 5px;
}
</style>
