package com.guozw.common.core.annotation;

import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.*;


/**
 * 自定义平台访问日志注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PlatformAccessLogAnno {
    /**
     * 访问模块
     * @return
     */
    String accessModule() default StringUtils.EMPTY;

    /**
     * 访问类型
     * @return
     */
    String accessType() default StringUtils.EMPTY;

    /**
     * 访问描述
     * @return
     */
    String accessDesc() default StringUtils.EMPTY;
}
