<template>
    <div>
        <vxe-modal
            height="99%"
            width="60%"
            position="center"
            resize
            :title="modalInfo.title"
            v-model="modalInfo.show"
            @close="handleModalClose()"
            showFooter
            v-loading.fullscreen="loading"
            element-loading-text="拼命加载中"
        >
            <vxe-form
                ref="myForm"
                title-width="120"
                :data="modalForm"
                :rules="rules"
                title-align="right"
                prevent-submit
                span="24"
            >
                <vxe-form-item title="隶属关系" field="departmentparentname">
                    <template v-slot="scope">
                        <vxe-pulldown ref="xDown1" style="width: 100%">
                            <template v-slot>
                                <vxe-input
                                    v-model.trim="
                                        modalForm.departmentparentname
                                    "
                                    readonly
                                    placeholder="隶属关系"
                                    @input="$refs.myForm.updateStatus(scope)"
                                    @focus="focusEvent1"
                                ></vxe-input>
                            </template>
                            <template v-slot:dropdown>
                                <div class="my-dropdown1">
                                    <el-input
                                        placeholder="输入关键字进行过滤"
                                        v-model="filterText"
                                    ></el-input>
                                    <el-tree
                                        ref="departmentTree"
                                        class="filter-tree"
                                        node-key="departmentcode"
                                        :data="departmentTreeNodes"
                                        :props="defaultProps"
                                        default-expand-all
                                        @node-click="handleNodeClick"
                                        :filter-node-method="filterNode"
                                    >
                                    </el-tree>
                                </div>
                            </template>
                        </vxe-pulldown>
                    </template>
                </vxe-form-item>

                <vxe-form-item title="分组名称" field="departmentname">
                    <template v-slot="scope">
                        <vxe-input
                            clearable
                            placeholder="分组名称"
                            v-model.trim="modalForm.departmentname"
                            @input="$refs.myForm.updateStatus(scope)"
                        >
                        </vxe-input>
                    </template>
                </vxe-form-item>
                <vxe-form-item
                    title="资产组网段"
                    field="zczwd"
                    v-if="assetType == 1"
                >
                    <template v-slot="scope">
                        <vxe-textarea
                            clearable
                            :placeholder="
                                `格式：192.168.x.x 或 192.168.x.x/12 或 192.168.x.x-192.168.x.x 如果有多个，请使用英文逗号(,)分割`
                            "
                            rows="10"
                            resize="vertical"
                            showWordCount
                            maxlength="1000"
                            :autosize="{ minRows: 10, maxRows: 20 }"
                            v-model.trim="modalForm.zczwd"
                            @input="$refs.myForm.updateStatus(scope)"
                        ></vxe-textarea>
                        <span class="tipSpan"
                            >格式：192.168.x.x 或 192.168.x.x/12 或
                            192.168.x.x-192.168.x.x
                            如果有多个，请使用英文逗号(,)分割</span
                        >
                    </template>
                </vxe-form-item>
            </vxe-form>
            <template v-slot:footer>
                <el-button type="" @click="handleDialogCancel">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </template>
        </vxe-modal>
    </div>
</template>
<script>
import { saveDepartment, modifyDepartment } from "@/api/security/department.js";
import { clearProperty, copyProperty, replaceAll } from "@/utils/guozw-core.js";
export default {
    name: "DepartmentEdit",
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        },
        formDefaultData: {
            type: Object,
            default: () => {}
        },
        departmentTreeNodes: {
            type: Array,
            default: () => []
        },
        // 资产类型
        assetType: {
            type: Number,
            default: 1
        }
    },
    data() {
        const that = this;
        return {
            loading: false,
            filterText: "",
            departmentTreeConfig: {
                id: "departmentcode",
                parentId: "departmentparentcode",
                children: "children"
            },
            defaultProps: {
                children: "children",
                label: "departmentname"
            },
            modalForm: {
                departmentparentcode: "",
                departmentparentname: "",
                // 部门编码 主键
                departmentcode: "",
                // 分组名称
                departmentname: "",
                // 资产组网段
                zczwd: ""
            },
            rules: {
                departmentparentname: [{ required: true, message: "必填字段" }],
                departmentname: [
                    { required: true, message: "必填字段" },
                    { min: 3, max: 10, message: "只能输入 3 到 10 个字符" }
                ],
                zczwd: [{ required: false, message: "必填字段" }, {validator: that.checkZczwd}]
            }
        };
    },

    watch: {
        formDefaultData: {
            handler(newVal, oldVal) {
                console.log("监听 formDefaultData", newVal);
                this.$nextTick(() => {
                    copyProperty(newVal, this.modalForm);
                });
            },
            deep: true,
            immediate: true
        },
        filterText(val) {
            this.$refs.departmentTree.filter(val);
        }
    },

    computed: {},
    created() {
        this.initData();
    },
    methods: {
        handleModalClose() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        handleDialogCancel() {
            this.modalInfo.show = false;
            this.$refs.myForm.clearValidate();
        },
        setDefault() {},
        initData() {},
        focusEvent1() {
            this.$refs.xDown1.showPanel();
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.departmentname.indexOf(value) !== -1;
        },
        // 校验资产组网段字段
        checkZczwd({ itemValue }) {
            if (itemValue) {
                const findItem = itemValue.split(",").find(item => {
                    if (item) {
                        if ((item.length - replaceAll(item, "-", "").length) > 1) {
                            return true
                        } else if ((item.length - replaceAll(item, "/", "").length) > 1) {
                            return true
                        }
                    }
                    
                })
                if (findItem) {
                    return new Error("缺少(,)分隔符");
                }
                
            }
        },
        // 处理节点点击
        handleNodeClick(data, node) {
            console.log("点击树节点", node);
            this.modalForm.departmentparentname = node.data.departmentname;
            this.modalForm.departmentparentcode = node.data.departmentcode;
            this.$refs.xDown1.hidePanel();
        },
        getQueryCondition() {
            let queryCondition = JSON.parse(JSON.stringify(this.modalForm));
            queryCondition.assetType = this.assetType;
            return queryCondition;
        },
        submitForm() {
            this.$refs.myForm
                .validate()
                .then(() => {
                    this.loading = true;

                    if (this.modalForm.departmentcode) {
                        modifyDepartment(this.getQueryCondition())
                            .then(res => {
                                if (res.data.result) {
                                     let content = `<div id="msg">`
                                     content += `<p>修改分组成功</p>`
                                    content += `<p>入库不成功的IP【${res.data.existsIpList && res.data.existsIpList.length > 0 ? res.data.existsIpList : '无'}】
                                                    ${res.data.existsIpList && res.data.existsIpList.length > 0 ? '，数据库存在此IP</p>' : '</p>'}`
                                     content += `<p>格式校验不通过的IP【${res.data.nopassIpList && res.data.nopassIpList.length > 0 ? res.data.nopassIpList : '无'}】</p>`
                                     content += `</div>`
                                     const content2 = "修改分组成功"
                                     this.$alert(this.assetType == 1 ? content : content2, "消息提示", {
                                        dangerouslyUseHTMLString: true,
                                        type: 'success'
                                    });
                                    this.$emit("refreshDepartmentTree");
                                    this.modalInfo.show = false;
                                }
                            })
                            .finally(() => {
                                this.loading = false;
                                clearProperty(this.modalForm);
                                this.modalInfo.show = false;
                            });
                    } else {
                        saveDepartment(this.getQueryCondition())
                            .then(res => {
                                if (res.data.result) {
                                    const content = `新增分组成功<br/>
                                                    入库不成功的IP【${res.data.existsIpList && res.data.existsIpList,length > 0 || '无'}】，数据库存在此IP<br/>
                                                    格式校验不通过的IP【${res.data.nopassIpList && res.data.nopassIpList.length > 0 ? res.data.nopassIpList : '无'}】`
                                     const content2 = "新增分组成功"
                                     this.$alert(this.assetType == 1 ? content : content2, "消息提示", {
                                        dangerouslyUseHTMLString: true,
                                        type: 'success'
                                    });
                                    // 向父组件传递事件，刷新部门树，并且选中当前新增的部门
                                    this.$emit(
                                        "refreshDepartmentTree",
                                        res.data.result.departmentcode,
                                        "0"
                                    );
                                    this.$emit(
                                        "switchAssetStatus",
                                        res.data.result.departmentcode
                                    );
                                    this.modalInfo.show = false;
                                }
                            })
                            .finally(() => {
                                this.loading = false;
                                clearProperty(this.modalForm);
                                this.modalInfo.show = false;
                            });
                    }
                })
                .catch(err => {
                    console.log(err);
                    return false;
                });
        }
    }
};
</script>
<style scoped>
.tipSpan {
    color: red;
}
</style>
