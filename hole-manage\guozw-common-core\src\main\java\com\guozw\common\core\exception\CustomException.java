package com.guozw.common.core.exception;

import com.guozw.common.core.constant.ErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 自定义异常
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomException extends RuntimeException {

    private static final long serialVersionUID = 691137562750063346L;
    private String message;
    private Integer code;

    public CustomException(ErrorCodeEnum errorCodeEnum) {
        this.message = errorCodeEnum.getMessage();
        this.code = errorCodeEnum.getCode();
    }

}
