package com.guozw.common.gateway.security;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.guozw.common.core.base.BaseResult;
import com.guozw.common.core.constant.CommonConstant;
import com.guozw.common.core.constant.JwtConstant;
import com.guozw.common.core.util.CommonUtils;
import com.guozw.common.core.util.JwtTokenUtils;
import com.guozw.common.core.util.RedisUtils;
import com.guozw.common.gateway.config.CustomPropertiesConfig;
import com.guozw.log.entity.PlatformAccessLog;
import com.guozw.log.facade.PlatformAccessLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.server.WebFilterExchange;
import org.springframework.security.web.server.authentication.ServerAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * 登录成功处理
 */
@Slf4j
@Component
public class CustomServerAuthenticationSuccessHandler implements ServerAuthenticationSuccessHandler {
    @DubboReference
    private PlatformAccessLogService platformAccessLogService;
    @Autowired
    private CustomPropertiesConfig customPropertiesConfig;

    @Override
    public Mono<Void> onAuthenticationSuccess(WebFilterExchange webFilterExchange, Authentication authentication) {
        log.info("登录成功");

        CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
        String username = customUserDetails.getUsername();

        //检查账号有没有被冻结
        String message = checkLoginTimes(username);
        if (message != null) {
            return com.guozw.common.gateway.util.CommonUtils.writeErrorMessage(webFilterExchange.getExchange().getResponse(), message);
        }
        //登录成功且没有冻结 就把缓存删除
        RedisUtils.delete(username + CommonConstant.REDIS_KEY);
        //创建token
        String token = JwtTokenUtils.createToken(username, false);
        // 将token存入Redis缓存
        RedisUtils.setEx(token, token, Long.parseLong(JwtConstant.JWT_TOKEN_EXPIRATION), TimeUnit.SECONDS);

        // token加上前缀
        token = StrUtil.format("{}{}", JwtConstant.JWT_TOKEN_PREFIX, token);
        ServerHttpResponse response = webFilterExchange.getExchange().getResponse();

        // 将token放入响应头中
        response.getHeaders().set(JwtConstant.JWT_TOKEN_HEADER, token);
        response.setStatusCode(HttpStatus.OK);
        response.getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String result = JSONUtil.toJsonStr(BaseResult.success().setMessage("登录成功").setData(token));
        DataBuffer buffer = response.bufferFactory().wrap(result.getBytes(CharsetUtil.CHARSET_UTF_8));


        try {
            // 保存登录日志
            saveLoginLog(webFilterExchange, username);
        } catch (Exception e) {
            log.error("保存登录日志失败【{}】", ExceptionUtil.stacktraceToString(e));
        }

        return response.writeWith(Mono.just(buffer));
    }

    private void saveLoginLog(WebFilterExchange webFilterExchange, String username) throws Exception{
        ServerHttpRequest request = webFilterExchange.getExchange().getRequest();
        PlatformAccessLog platformAccessLog = new PlatformAccessLog();
        platformAccessLog.setPlatformaccesslogcode(IdUtil.getSnowflakeNextId())
                .setAccessmodule(CommonConstant.MODULE_SECURITY)
                .setAccesstype(CommonConstant.ACCESS_TYPE_LOGIN)
                .setAccessdesc("登录成功")
                .setClientip(CommonUtils.getIpAddress(request))
                .setRequesturi(customPropertiesConfig.getUrl_login())
                .setRequestmethod(HttpMethod.POST.name())
                .setUsername(username);
        platformAccessLog.setCreatedate(DateUtil.date());
        platformAccessLog.setModifydate(DateUtil.date());

        platformAccessLogService.savePlatformAccessLogAsync(platformAccessLog);
    }

    private String checkLoginTimes(String username) {
        Integer o = (Integer)RedisUtils.get(username + CommonConstant.REDIS_KEY);
        if (o != null && o >= CommonConstant.LOGIN_TIMES){
            Long expire = RedisUtils.getExpire(username + CommonConstant.REDIS_KEY, TimeUnit.SECONDS);
            if (expire != null && expire > 0) {
                String s = DateUtil.formatBetween(expire, BetweenFormatter.Level.MINUTE);
                return "当前账户已被冻结，请于" + s + "后重试！";
            }
        }
        return null;
    }
}
